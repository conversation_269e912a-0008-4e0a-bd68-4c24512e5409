import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * 通用API响应DTO
 */
export class ApiResponseDto<T = any> {
  @ApiProperty({ description: '是否成功', example: true })
  success: boolean;

  @ApiProperty({ description: '响应消息', example: '操作成功' })
  message: string;

  @ApiPropertyOptional({ description: '响应数据' })
  data?: T;

  @ApiPropertyOptional({ description: '错误代码', example: 'VALIDATION_ERROR' })
  errorCode?: string;

  @ApiProperty({ description: '时间戳', example: '2024-01-01T00:00:00.000Z' })
  timestamp: string;

  constructor(success: boolean, message: string, data?: T, errorCode?: string) {
    this.success = success;
    this.message = message;
    this.data = data;
    this.errorCode = errorCode;
    this.timestamp = new Date().toISOString();
  }

  static success<T>(message: string = '操作成功', data?: T): ApiResponseDto<T> {
    return new ApiResponseDto(true, message, data);
  }

  static error(message: string, errorCode?: string): ApiResponseDto {
    return new ApiResponseDto(false, message, undefined, errorCode);
  }
}

/**
 * 节点信息响应DTO
 */
export class NodeResponseDto {
  @ApiProperty({ description: '节点ID', example: 'edge-node-001' })
  nodeId: string;

  @ApiProperty({ description: '节点区域', example: 'beijing-zone-1' })
  region: string;

  @ApiProperty({ description: '节点端点', example: 'http://*************:8080' })
  endpoint: string;

  @ApiProperty({ description: '节点状态', example: 'online' })
  status: string;

  @ApiProperty({ description: '最后心跳时间', example: '2024-01-01T00:00:00.000Z' })
  lastHeartbeat: string;

  @ApiProperty({ description: '注册时间', example: '2024-01-01T00:00:00.000Z' })
  registeredAt: string;

  @ApiPropertyOptional({ description: '节点指标' })
  metrics?: {
    currentUsers: number;
    cpuUsage: number;
    memoryUsage: number;
    networkLatency: number;
    uptime: number;
  };

  @ApiPropertyOptional({ description: '节点能力' })
  capabilities?: {
    maxUsers: number;
    supportedFeatures: string[];
    resources: {
      cpu: string;
      memory: string;
      storage: string;
    };
  };
}

/**
 * 集群统计响应DTO
 */
export class ClusterStatsResponseDto {
  @ApiProperty({ description: '总节点数', example: 10 })
  totalNodes: number;

  @ApiProperty({ description: '在线节点数', example: 8 })
  onlineNodes: number;

  @ApiProperty({ description: '离线节点数', example: 1 })
  offlineNodes: number;

  @ApiProperty({ description: '过载节点数', example: 1 })
  overloadedNodes: number;

  @ApiProperty({ description: '总用户数', example: 150 })
  totalUsers: number;

  @ApiProperty({ description: '平均CPU使用率', example: 45.5 })
  averageCpuUsage: number;

  @ApiProperty({ description: '平均内存使用率', example: 60.2 })
  averageMemoryUsage: number;

  @ApiProperty({ description: '可用区域列表', example: ['beijing-zone-1', 'shanghai-zone-1'] })
  availableRegions: string[];
}

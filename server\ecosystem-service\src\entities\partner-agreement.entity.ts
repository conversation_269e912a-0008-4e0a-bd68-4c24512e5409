import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { Partner } from './partner.entity';

export enum AgreementType {
  PARTNERSHIP = 'partnership',
  RESELLER = 'reseller',
  TECHNOLOGY = 'technology',
  DISTRIBUTION = 'distribution'
}

export enum AgreementStatus {
  ACTIVE = 'active',
  EXPIRED = 'expired',
  TERMINATED = 'terminated',
  DRAFT = 'draft'
}

@Entity('partner_agreements')
@Index(['partnerId', 'status'])
export class PartnerAgreement {
  @PrimaryGeneratedColumn('uuid')
  agreementId: string;

  @Column('uuid')
  @Index()
  partnerId: string;

  @Column({
    type: 'enum',
    enum: AgreementType,
  })
  type: AgreementType;

  @Column({
    type: 'enum',
    enum: AgreementStatus,
    default: AgreementStatus.DRAFT,
  })
  @Index()
  status: AgreementStatus;

  // 协议条款 - 存储为JSON
  @Column('json')
  terms: {
    revenue_sharing: number; // %
    support_obligations: string[];
    marketing_rights: string[];
    territory_restrictions: string[];
    exclusivity: boolean;
    minimum_commitments: any;
  };

  @Column({ type: 'date' })
  signedAt: Date;

  @Column({ type: 'date' })
  expiresAt: Date;

  @Column('text', { nullable: true })
  notes: string;

  @Column({ length: 500, nullable: true })
  documentUrl: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => Partner, partner => partner.agreements, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'partnerId' })
  partner: Partner;
}

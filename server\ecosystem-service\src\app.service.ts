import { Injectable } from '@nestjs/common';

@Injectable()
export class AppService {
  getServiceInfo() {
    return {
      name: 'ecosystem-service',
      description: '生态系统建设服务 - 合作伙伴生态构建、开放API平台、第三方应用集成、行业标准制定',
      version: '1.0.0',
      status: 'running',
      timestamp: new Date().toISOString(),
      features: [
        '合作伙伴管理',
        'API平台管理',
        '第三方应用集成',
        '行业标准制定',
        '生态系统监控'
      ]
    };
  }

  getVersion() {
    return {
      version: '1.0.0',
      buildTime: new Date().toISOString(),
      nodeVersion: process.version,
      environment: process.env.NODE_ENV || 'development'
    };
  }
}

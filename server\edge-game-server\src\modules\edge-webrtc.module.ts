import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';

// 服务
import { EdgeWebRTCService } from '../services/edge-webrtc.service';
import { EdgeSignalingService } from '../services/edge-signaling.service';
import { EdgePeerService } from '../services/edge-peer.service';

// 控制器
import { EdgeWebRTCController } from '../controllers/edge-webrtc.controller';

// 网关
import { EdgeWebRTCGateway } from '../gateways/edge-webrtc.gateway';

/**
 * 边缘WebRTC模块
 * 提供WebRTC通信和信令服务
 */
@Module({
  imports: [
    ConfigModule,
    EventEmitterModule,
  ],
  
  controllers: [
    EdgeWebRTCController,
  ],
  
  providers: [
    EdgeWebRTCService,
    EdgeSignalingService,
    EdgePeerService,
    EdgeWebRTCGateway,
  ],
  
  exports: [
    EdgeWebRTCService,
    EdgeSignalingService,
    EdgePeerService,
  ],
})
export class EdgeWebRTCModule {}

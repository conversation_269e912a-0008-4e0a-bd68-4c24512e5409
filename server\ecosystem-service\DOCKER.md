# Docker 部署指南

## 概述

本文档描述如何使用 Docker 和 docker-compose 部署生态系统建设服务。

## 前置要求

- Docker >= 20.10
- docker-compose >= 1.29
- 至少 2GB 可用内存
- 至少 10GB 可用磁盘空间

## 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd ecosystem-service
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp .env.docker .env

# 编辑环境变量（重要：修改密码和密钥）
nano .env
```

### 3. 生产环境部署

```bash
# 构建并启动服务
./scripts/docker-deploy.sh production

# 或者手动执行
docker-compose up -d
```

### 4. 开发环境部署

```bash
# 启动开发环境
./scripts/docker-deploy.sh development

# 或者手动执行
docker-compose -f docker-compose.dev.yml up -d
```

## 服务架构

### 生产环境服务

- **ecosystem-service**: 主应用服务 (端口: 3030)
- **mysql**: MySQL 数据库 (端口: 3306)
- **redis**: Redis 缓存 (端口: 6379)
- **nginx**: 反向代理 (端口: 80, 443)

### 开发环境额外服务

- **phpmyadmin**: MySQL 管理界面 (端口: 8080)
- **redis-commander**: Redis 管理界面 (端口: 8081)

## 配置说明

### 环境变量

| 变量名 | 描述 | 默认值 |
|--------|------|--------|
| `NODE_ENV` | 运行环境 | `production` |
| `PORT` | 应用端口 | `3030` |
| `DB_HOST` | 数据库主机 | `mysql` |
| `DB_PASSWORD` | 数据库密码 | `ecosystem_password` |
| `REDIS_HOST` | Redis主机 | `redis` |
| `JWT_SECRET` | JWT密钥 | **必须修改** |

### 数据卷

- `mysql_data`: MySQL 数据持久化
- `redis_data`: Redis 数据持久化
- `ecosystem_storage`: 应用文件存储
- `ecosystem_logs`: 应用日志
- `nginx_logs`: Nginx 日志

## 管理命令

### 查看服务状态

```bash
docker-compose ps
```

### 查看日志

```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f ecosystem-service
```

### 重启服务

```bash
# 重启所有服务
docker-compose restart

# 重启特定服务
docker-compose restart ecosystem-service
```

### 停止服务

```bash
docker-compose down
```

### 完全清理（包括数据）

```bash
docker-compose down -v
docker system prune -a
```

## 健康检查

### 自动健康检查

所有服务都配置了健康检查：

- **应用服务**: `GET /health`
- **MySQL**: `mysqladmin ping`
- **Redis**: `redis-cli ping`

### 手动健康检查

```bash
# 检查应用健康状态
curl http://localhost:3030/health

# 检查详细健康状态
curl http://localhost:3030/health/detailed
```

## 监控和日志

### 访问管理界面

**开发环境:**
- phpMyAdmin: http://localhost:8080
- Redis Commander: http://localhost:8081

### 日志位置

```bash
# 应用日志
docker-compose logs ecosystem-service

# 数据库日志
docker-compose logs mysql

# Nginx日志
docker-compose logs nginx
```

## 备份和恢复

### 数据库备份

```bash
# 创建备份
docker-compose exec mysql mysqldump -u ecosystem_user -p ecosystem_service > backup.sql

# 恢复备份
docker-compose exec -T mysql mysql -u ecosystem_user -p ecosystem_service < backup.sql
```

### Redis备份

```bash
# 创建备份
docker-compose exec redis redis-cli --rdb /data/dump.rdb

# 复制备份文件
docker cp $(docker-compose ps -q redis):/data/dump.rdb ./redis-backup.rdb
```

## 性能优化

### 生产环境建议

1. **资源限制**:
   ```yaml
   deploy:
     resources:
       limits:
         memory: 1G
         cpus: '0.5'
   ```

2. **数据库优化**:
   - 调整 MySQL 配置
   - 设置适当的连接池大小
   - 启用查询缓存

3. **Redis优化**:
   - 配置内存限制
   - 启用持久化
   - 设置合适的过期策略

## 安全配置

### 生产环境安全检查清单

- [ ] 修改所有默认密码
- [ ] 使用强JWT密钥
- [ ] 配置防火墙规则
- [ ] 启用SSL/TLS
- [ ] 限制数据库访问
- [ ] 配置日志轮转
- [ ] 定期更新镜像

### SSL/TLS配置

```nginx
server {
    listen 443 ssl http2;
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    # ... 其他SSL配置
}
```

## 故障排除

### 常见问题

1. **服务无法启动**:
   ```bash
   # 检查日志
   docker-compose logs
   
   # 检查端口占用
   netstat -tulpn | grep :3030
   ```

2. **数据库连接失败**:
   ```bash
   # 检查数据库状态
   docker-compose exec mysql mysql -u root -p -e "SHOW DATABASES;"
   ```

3. **Redis连接失败**:
   ```bash
   # 检查Redis状态
   docker-compose exec redis redis-cli ping
   ```

### 调试模式

```bash
# 启用调试模式
NODE_ENV=development docker-compose -f docker-compose.dev.yml up
```

## 更新和维护

### 更新应用

```bash
# 拉取最新代码
git pull

# 重新构建镜像
docker-compose build

# 重启服务
docker-compose up -d
```

### 数据库迁移

```bash
# 运行迁移
docker-compose exec ecosystem-service npm run migration:run
```

## 支持

如有问题，请查看：
- 应用日志: `docker-compose logs ecosystem-service`
- 健康检查: `curl http://localhost:3030/health/detailed`
- 项目文档: `README.md`

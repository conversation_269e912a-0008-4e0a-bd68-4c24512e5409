#!/bin/bash

# 群体协调服务生产部署脚本
# 用于构建和部署生产环境

set -e

echo "🚀 开始部署群体协调服务..."

# 设置变量
SERVICE_NAME="coordination-service"
IMAGE_NAME="coordination-service"
CONTAINER_NAME="coordination-service"
VERSION=${1:-latest}

# 检查 Docker
echo "🐳 检查 Docker..."
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

if ! docker info &> /dev/null; then
    echo "❌ Docker 服务未运行，请启动 Docker 服务"
    exit 1
fi

echo "✅ Docker 检查通过"

# 检查 Docker Compose
echo "🐳 检查 Docker Compose..."
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

echo "✅ Docker Compose 检查通过"

# 构建镜像
echo "🔨 构建 Docker 镜像..."
docker build -t $IMAGE_NAME:$VERSION .
docker tag $IMAGE_NAME:$VERSION $IMAGE_NAME:latest

echo "✅ Docker 镜像构建完成"

# 停止现有容器
echo "🛑 停止现有容器..."
docker-compose down || true

# 启动服务
echo "🚀 启动服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 健康检查
echo "🔍 执行健康检查..."
max_attempts=30
attempt=1

while [ $attempt -le $max_attempts ]; do
    if curl -f http://localhost:3010/api/v1/coordination/health &> /dev/null; then
        echo "✅ 服务健康检查通过"
        break
    else
        echo "⏳ 等待服务启动... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    fi
done

if [ $attempt -gt $max_attempts ]; then
    echo "❌ 服务启动失败，健康检查超时"
    echo "📋 查看服务日志:"
    docker-compose logs $SERVICE_NAME
    exit 1
fi

# 显示服务状态
echo "📊 服务状态:"
docker-compose ps

echo "🎉 部署完成!"
echo "📍 服务地址: http://localhost:3010"
echo "📍 健康检查: http://localhost:3010/api/v1/coordination/health"
echo "📍 Redis 管理: http://localhost:8081 (用户名: admin, 密码: admin123)"

# 显示有用的命令
echo ""
echo "📋 有用的命令:"
echo "  查看日志: docker-compose logs -f $SERVICE_NAME"
echo "  重启服务: docker-compose restart $SERVICE_NAME"
echo "  停止服务: docker-compose down"
echo "  进入容器: docker-compose exec $SERVICE_NAME sh"

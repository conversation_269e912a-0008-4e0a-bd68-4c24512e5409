version: '3.8'

services:
  # 边缘AI服务
  edge-ai-service:
    build: .
    container_name: edge-ai-service
    ports:
      - "3006:3006"
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=edge_ai
      - DB_PASSWORD=edge_ai_password
      - DB_DATABASE=edge_ai_service
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    depends_on:
      - mysql
      - redis
    networks:
      - edge-ai-network
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
      - ./models:/app/models

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: edge-ai-mysql
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=edge_ai_service
      - MYSQL_USER=edge_ai
      - MYSQL_PASSWORD=edge_ai_password
    ports:
      - "3307:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./scripts/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - edge-ai-network
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: edge-ai-redis
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
    networks:
      - edge-ai-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: edge-ai-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - edge-ai-service
    networks:
      - edge-ai-network
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:

networks:
  edge-ai-network:
    driver: bridge

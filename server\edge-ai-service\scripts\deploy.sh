#!/bin/bash

# 边缘AI服务部署脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
ENVIRONMENT=${1:-production}
SERVICE_NAME="edge-ai-service"
DOCKER_IMAGE="$SERVICE_NAME:latest"
CONTAINER_NAME="$SERVICE_NAME-$ENVIRONMENT"

echo -e "${BLUE}🚀 开始部署边缘AI计算服务...${NC}"
echo -e "${BLUE}环境: $ENVIRONMENT${NC}\n"

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker未安装，请先安装Docker${NC}"
    exit 1
fi

# 检查Docker Compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ Docker Compose未安装，请先安装Docker Compose${NC}"
    exit 1
fi

# 停止现有容器
echo -e "${YELLOW}🛑 停止现有容器...${NC}"
docker-compose down || true

# 清理旧镜像
echo -e "${YELLOW}🧹 清理旧镜像...${NC}"
docker image prune -f

# 构建新镜像
echo -e "${YELLOW}🔨 构建Docker镜像...${NC}"
docker build -t $DOCKER_IMAGE .

# 检查环境配置文件
if [ ! -f ".env" ]; then
    echo -e "${YELLOW}⚠️  未找到.env文件，复制示例配置...${NC}"
    cp .env.example .env
    echo -e "${YELLOW}请编辑.env文件配置正确的环境变量${NC}"
fi

# 创建必要的目录
echo -e "${YELLOW}📁 创建必要的目录...${NC}"
mkdir -p logs
mkdir -p models
mkdir -p nginx/ssl

# 启动服务
echo -e "${YELLOW}🚀 启动服务...${NC}"
if [ "$ENVIRONMENT" = "development" ]; then
    docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
else
    docker-compose up -d
fi

# 等待服务启动
echo -e "${YELLOW}⏳ 等待服务启动...${NC}"
sleep 30

# 健康检查
echo -e "${YELLOW}🔍 执行健康检查...${NC}"
max_attempts=30
attempt=1

while [ $attempt -le $max_attempts ]; do
    if curl -f http://localhost:3006/api/v1/edge/statistics > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 服务健康检查通过${NC}"
        break
    else
        echo -e "${YELLOW}⏳ 等待服务启动... (尝试 $attempt/$max_attempts)${NC}"
        sleep 5
        ((attempt++))
    fi
done

if [ $attempt -gt $max_attempts ]; then
    echo -e "${RED}❌ 服务启动失败，健康检查超时${NC}"
    echo -e "${RED}查看日志: docker-compose logs${NC}"
    exit 1
fi

# 显示服务状态
echo -e "\n${GREEN}🎉 部署完成！${NC}"
echo -e "${GREEN}服务状态:${NC}"
docker-compose ps

echo -e "\n${GREEN}服务访问地址:${NC}"
echo -e "${GREEN}  - API文档: http://localhost/api/docs${NC}"
echo -e "${GREEN}  - 服务API: http://localhost/api/v1${NC}"
echo -e "${GREEN}  - WebSocket: ws://localhost/edge-ai${NC}"

echo -e "\n${GREEN}管理命令:${NC}"
echo -e "${GREEN}  - 查看日志: docker-compose logs -f${NC}"
echo -e "${GREEN}  - 停止服务: docker-compose down${NC}"
echo -e "${GREEN}  - 重启服务: docker-compose restart${NC}"
echo -e "${GREEN}  - 查看状态: docker-compose ps${NC}"

# 运行API测试
if [ "$ENVIRONMENT" = "development" ]; then
    echo -e "\n${YELLOW}🧪 运行API测试...${NC}"
    sleep 5
    node scripts/test-api.js || echo -e "${YELLOW}⚠️  API测试失败，请检查服务状态${NC}"
fi

echo -e "\n${GREEN}✅ 边缘AI计算服务部署完成！${NC}"

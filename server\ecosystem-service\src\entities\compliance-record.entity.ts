import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { IndustryStandard } from './industry-standard.entity';

@Entity('compliance_records')
@Index(['standardId', 'entityId'])
@Index(['standardId', 'assessmentDate'])
export class ComplianceRecord {
  @PrimaryGeneratedColumn('uuid')
  recordId: string;

  @Column('uuid')
  @Index()
  standardId: string;

  @Column({ length: 255 })
  @Index()
  entityId: string; // 被评估实体ID（合作伙伴、应用等）

  @Column({ length: 100 })
  entityType: string; // 'partner', 'application', 'api'

  @Column({ length: 255 })
  entityName: string;

  @Column({ type: 'decimal', precision: 5, scale: 2 })
  complianceScore: number; // 0-100

  @Column({ length: 100 })
  complianceLevel: string; // 合规级别

  @Column('json', { nullable: true })
  assessmentResults: {
    assessor_id: string;
    assessor_name: string;
    methodology: string;
    criteria_scores: Array<{
      criterion_id: string;
      criterion_name: string;
      score: number;
      max_score: number;
      comments: string;
    }>;
    strengths: string[];
    weaknesses: string[];
    recommendations: string[];
    action_items: Array<{
      item: string;
      priority: 'high' | 'medium' | 'low';
      due_date: Date;
      responsible: string;
    }>;
  };

  @Column({ type: 'date' })
  @Index()
  assessmentDate: Date;

  @Column({ type: 'date', nullable: true })
  nextAssessmentDate: Date;

  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @Column('text', { nullable: true })
  notes: string;

  @CreateDateColumn()
  createdAt: Date;

  // 关联关系
  @ManyToOne(() => IndustryStandard, standard => standard.complianceRecords, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'standardId' })
  standard: IndustryStandard;
}

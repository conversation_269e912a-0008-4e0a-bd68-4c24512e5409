{"name": "edge-game-server", "version": "1.0.0", "description": "边缘游戏服务器 - 轻量级边缘节点游戏服务", "author": "DL Engine Team", "private": true, "license": "MIT", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "lint:check": "eslint \"{src,apps,libs,test}/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "clean": "rimraf dist node_modules package-lock.json", "clean:install": "npm run clean && npm install --legacy-peer-deps", "docker:build": "docker build -t edge-game-server .", "docker:run": "docker run -p 8080:8080 -p 3030:3030 edge-game-server", "docker:compose": "docker-compose up -d", "docker:down": "docker-compose down"}, "dependencies": {"@nestjs/common": "^10.3.0", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.3.0", "@nestjs/event-emitter": "^2.0.3", "@nestjs/microservices": "^10.3.0", "@nestjs/platform-express": "^10.3.0", "@nestjs/platform-socket.io": "^10.3.0", "@nestjs/schedule": "^4.0.0", "@nestjs/swagger": "^7.1.17", "@nestjs/throttler": "^5.1.1", "@nestjs/websockets": "^10.3.0", "axios": "^1.10.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "compression": "^1.7.4", "dayjs": "^1.11.10", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "ioredis": "^5.3.2", "lodash": "^4.17.21", "node-cron": "^3.0.3", "prom-client": "^15.1.0", "redis": "^4.6.12", "reflect-metadata": "^0.1.14", "rxjs": "^7.8.1", "simple-peer": "^9.11.1", "socket.io": "^4.7.4", "uuid": "^9.0.1", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "ws": "^8.16.0"}, "devDependencies": {"@nestjs/cli": "^10.3.0", "@nestjs/schematics": "^10.1.0", "@nestjs/testing": "^10.3.0", "@types/compression": "^1.7.5", "@types/express": "^4.17.21", "@types/jest": "^29.5.11", "@types/lodash": "^4.14.202", "@types/node": "^20.10.6", "@types/node-cron": "^3.0.11", "@types/simple-peer": "^9.11.8", "@types/supertest": "^6.0.2", "@types/uuid": "^9.0.7", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^6.18.1", "@typescript-eslint/parser": "^6.18.1", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.2", "jest": "^29.7.0", "prettier": "^3.1.1", "rimraf": "^5.0.5", "source-map-support": "^0.5.21", "supertest": "^6.3.4", "ts-jest": "^29.1.1", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["edge-computing", "game-server", "webrtc", "real-time", "microservice", "<PERSON><PERSON><PERSON>", "dl-engine"]}
import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { Transport } from '@nestjs/microservices';
import { AppModule } from './app.module';

async function bootstrap() {
  const logger = new Logger('Bootstrap');
  
  try {
    // 创建Nest应用实例
    const app = await NestFactory.create(AppModule);
    const configService = app.get(ConfigService);
    
    // 配置微服务
    app.connectMicroservice({
      transport: Transport.TCP,
      options: {
        host: configService.get<string>('ECOSYSTEM_SERVICE_HOST', 'localhost'),
        port: configService.get<number>('ECOSYSTEM_SERVICE_PORT', 3030),
      },
    });
    
    // 配置HTTP服务
    // 全局前缀
    app.setGlobalPrefix(configService.get<string>('API_PREFIX', 'api'));
    
    // 全局管道
    app.useGlobalPipes(
      new ValidationPipe({
        whitelist: true,
        transform: true,
        forbidNonWhitelisted: true,
      }),
    );
    
    // 启用CORS
    app.enableCors({
      origin: configService.get<string>('CORS_ORIGIN', '*'),
      methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
      credentials: true,
    });
    
    // Swagger文档配置
    if (configService.get<string>('NODE_ENV') !== 'production') {
      const config = new DocumentBuilder()
        .setTitle('生态系统建设服务 API')
        .setDescription('合作伙伴生态构建、开放API平台、第三方应用集成、行业标准制定')
        .setVersion('1.0')
        .addTag('ecosystem', '生态系统管理')
        .addTag('partners', '合作伙伴管理')
        .addTag('apis', 'API平台管理')
        .addTag('applications', '第三方应用管理')
        .addTag('standards', '行业标准管理')
        .addBearerAuth()
        .build();
      
      const document = SwaggerModule.createDocument(app, config);
      SwaggerModule.setup('api/docs', app, document);
    }
    
    // 启动微服务
    await app.startAllMicroservices();
    
    // 启动HTTP服务
    const port = configService.get<number>('PORT', 3030);
    const host = configService.get<string>('HOST', '0.0.0.0');
    
    await app.listen(port, host);
    
    logger.log(`生态系统建设服务已启动`);
    logger.log(`HTTP服务: http://${host}:${port}`);
    logger.log(`API文档: http://${host}:${port}/api/docs`);
    logger.log(`环境: ${configService.get<string>('NODE_ENV', 'development')}`);
    
    // 优雅关闭处理
    process.on('SIGTERM', async () => {
      logger.log('收到SIGTERM信号，开始优雅关闭...');
      await app.close();
      process.exit(0);
    });

    process.on('SIGINT', async () => {
      logger.log('收到SIGINT信号，开始优雅关闭...');
      await app.close();
      process.exit(0);
    });
    
  } catch (error) {
    logger.error('启动失败:', error);
    process.exit(1);
  }
}

bootstrap();

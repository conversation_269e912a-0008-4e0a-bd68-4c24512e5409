/**
 * 模型管理服务
 * 
 * 负责模型的生命周期管理，包括：
 * - 模型文件管理
 * - 模型版本控制
 * - 模型配置管理
 * - 模型存储和检索
 */

import { Injectable, Logger, Inject } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as fs from 'fs/promises';
import * as path from 'path';
import * as crypto from 'crypto';
import { UpdateModelDto } from '../dto/model.dto';

/**
 * 模型文件信息接口
 */
export interface ModelFileInfo {
  id: string;
  filename: string;
  path: string;
  size: number;
  hash: string;
  uploadedAt: number;
  mimetype: string;
}

/**
 * 模型配置接口
 */
export interface ModelConfig {
  id: string;
  name: string;
  type: string;
  version: string;
  description?: string;
  tags?: string[];
  inputShape?: number[];
  outputShape?: number[];
  preprocessing?: any;
  postprocessing?: any;
  metadata?: { [key: string]: any };
  createdAt: number;
  updatedAt: number;
}

/**
 * 模型管理服务
 */
@Injectable()
export class ModelManagementService {
  private readonly logger = new Logger(ModelManagementService.name);
  
  private modelConfigs = new Map<string, ModelConfig>();
  private modelFiles = new Map<string, ModelFileInfo>();

  constructor(
    private readonly eventEmitter: EventEmitter2,
    @Inject('MODEL_CONFIG') private readonly config: any,
  ) {
    this.initializeService();
  }

  /**
   * 初始化服务
   */
  private async initializeService(): Promise<void> {
    try {
      // 确保模型存储目录存在
      await this.ensureStorageDirectory();
      
      // 加载已有模型配置
      await this.loadExistingConfigs();
      
      this.logger.log('模型管理服务已初始化');
      
    } catch (error) {
      this.logger.error('模型管理服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 保存上传的模型文件
   */
  public async saveUploadedModel(file: Express.Multer.File, modelId: string): Promise<string> {
    try {
      // 验证文件格式
      this.validateModelFile(file);
      
      // 生成文件路径
      const filename = `${modelId}_${Date.now()}_${file.originalname}`;
      const filePath = path.join(this.config.modelStoragePath, filename);
      
      // 保存文件
      await fs.writeFile(filePath, file.buffer);
      
      // 计算文件哈希
      const hash = crypto.createHash('sha256').update(file.buffer).digest('hex');
      
      // 保存文件信息
      const fileInfo: ModelFileInfo = {
        id: modelId,
        filename,
        path: filePath,
        size: file.size,
        hash,
        uploadedAt: Date.now(),
        mimetype: file.mimetype,
      };
      
      this.modelFiles.set(modelId, fileInfo);
      
      this.eventEmitter.emit('model.file.uploaded', {
        modelId,
        filename,
        size: file.size,
      });
      
      this.logger.log(`模型文件已保存: ${filename}`);
      
      return filePath;
      
    } catch (error) {
      this.logger.error('保存模型文件失败:', error);
      throw error;
    }
  }

  /**
   * 创建模型配置
   */
  public async createModelConfig(config: Partial<ModelConfig>): Promise<ModelConfig> {
    try {
      const modelConfig: ModelConfig = {
        id: config.id!,
        name: config.name!,
        type: config.type!,
        version: config.version!,
        description: config.description,
        tags: config.tags || [],
        inputShape: config.inputShape,
        outputShape: config.outputShape,
        preprocessing: config.preprocessing,
        postprocessing: config.postprocessing,
        metadata: config.metadata || {},
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };
      
      this.modelConfigs.set(config.id!, modelConfig);
      
      // 保存到文件
      await this.saveConfigToFile(modelConfig);
      
      this.eventEmitter.emit('model.config.created', modelConfig);
      
      this.logger.log(`模型配置已创建: ${config.id}`);
      
      return modelConfig;
      
    } catch (error) {
      this.logger.error('创建模型配置失败:', error);
      throw error;
    }
  }

  /**
   * 更新模型配置
   */
  public async updateModelConfig(modelId: string, updateDto: UpdateModelDto): Promise<ModelConfig> {
    try {
      const existingConfig = this.modelConfigs.get(modelId);
      
      if (!existingConfig) {
        throw new Error(`模型配置不存在: ${modelId}`);
      }
      
      const updatedConfig: ModelConfig = {
        ...existingConfig,
        ...updateDto,
        updatedAt: Date.now(),
      };
      
      this.modelConfigs.set(modelId, updatedConfig);
      
      // 保存到文件
      await this.saveConfigToFile(updatedConfig);
      
      this.eventEmitter.emit('model.config.updated', {
        modelId,
        changes: updateDto,
      });
      
      this.logger.log(`模型配置已更新: ${modelId}`);
      
      return updatedConfig;
      
    } catch (error) {
      this.logger.error('更新模型配置失败:', error);
      throw error;
    }
  }

  /**
   * 获取模型配置
   */
  public getModelConfig(modelId: string): ModelConfig | null {
    return this.modelConfigs.get(modelId) || null;
  }

  /**
   * 获取所有模型配置
   */
  public getAllModelConfigs(): ModelConfig[] {
    return Array.from(this.modelConfigs.values());
  }

  /**
   * 删除模型配置
   */
  public async deleteModelConfig(modelId: string): Promise<void> {
    try {
      const config = this.modelConfigs.get(modelId);
      
      if (!config) {
        throw new Error(`模型配置不存在: ${modelId}`);
      }
      
      // 删除配置
      this.modelConfigs.delete(modelId);
      
      // 删除配置文件
      await this.deleteConfigFile(modelId);
      
      // 删除模型文件
      const fileInfo = this.modelFiles.get(modelId);
      if (fileInfo) {
        await this.deleteModelFile(fileInfo.path);
        this.modelFiles.delete(modelId);
      }
      
      this.eventEmitter.emit('model.config.deleted', { modelId });
      
      this.logger.log(`模型配置已删除: ${modelId}`);
      
    } catch (error) {
      this.logger.error('删除模型配置失败:', error);
      throw error;
    }
  }

  /**
   * 重新加载模型
   */
  public async reloadModel(modelId: string): Promise<void> {
    try {
      const config = this.modelConfigs.get(modelId);
      
      if (!config) {
        throw new Error(`模型配置不存在: ${modelId}`);
      }
      
      this.eventEmitter.emit('model.reload.requested', { modelId });
      
      this.logger.log(`模型重新加载请求已发送: ${modelId}`);
      
    } catch (error) {
      this.logger.error('重新加载模型失败:', error);
      throw error;
    }
  }

  /**
   * 获取模型文件信息
   */
  public getModelFileInfo(modelId: string): ModelFileInfo | null {
    return this.modelFiles.get(modelId) || null;
  }

  /**
   * 验证模型文件
   */
  private validateModelFile(file: Express.Multer.File): void {
    // 检查文件大小
    if (file.size > this.config.maxModelSize) {
      throw new Error(`模型文件过大: ${file.size} bytes, 最大允许: ${this.config.maxModelSize} bytes`);
    }
    
    // 检查文件格式
    const extension = path.extname(file.originalname).toLowerCase().substring(1);
    if (!this.config.supportedFormats.includes(extension)) {
      throw new Error(`不支持的模型格式: ${extension}, 支持的格式: ${this.config.supportedFormats.join(', ')}`);
    }
  }

  /**
   * 确保存储目录存在
   */
  private async ensureStorageDirectory(): Promise<void> {
    try {
      await fs.access(this.config.modelStoragePath);
    } catch (error) {
      // 目录不存在，创建它
      await fs.mkdir(this.config.modelStoragePath, { recursive: true });
      this.logger.log(`创建模型存储目录: ${this.config.modelStoragePath}`);
    }
  }

  /**
   * 加载已有配置
   */
  private async loadExistingConfigs(): Promise<void> {
    try {
      const configDir = path.join(this.config.modelStoragePath, 'configs');
      
      try {
        await fs.access(configDir);
      } catch (error) {
        // 配置目录不存在，创建它
        await fs.mkdir(configDir, { recursive: true });
        return;
      }
      
      const files = await fs.readdir(configDir);
      const configFiles = files.filter(file => file.endsWith('.json'));
      
      for (const file of configFiles) {
        try {
          const filePath = path.join(configDir, file);
          const content = await fs.readFile(filePath, 'utf-8');
          const config: ModelConfig = JSON.parse(content);
          
          this.modelConfigs.set(config.id, config);
          
        } catch (error) {
          this.logger.warn(`加载配置文件失败: ${file}`, error);
        }
      }
      
      this.logger.log(`已加载 ${this.modelConfigs.size} 个模型配置`);
      
    } catch (error) {
      this.logger.error('加载已有配置失败:', error);
    }
  }

  /**
   * 保存配置到文件
   */
  private async saveConfigToFile(config: ModelConfig): Promise<void> {
    try {
      const configDir = path.join(this.config.modelStoragePath, 'configs');
      await fs.mkdir(configDir, { recursive: true });
      
      const filePath = path.join(configDir, `${config.id}.json`);
      await fs.writeFile(filePath, JSON.stringify(config, null, 2));
      
    } catch (error) {
      this.logger.error('保存配置文件失败:', error);
      throw error;
    }
  }

  /**
   * 删除配置文件
   */
  private async deleteConfigFile(modelId: string): Promise<void> {
    try {
      const configDir = path.join(this.config.modelStoragePath, 'configs');
      const filePath = path.join(configDir, `${modelId}.json`);
      
      await fs.unlink(filePath);
      
    } catch (error) {
      // 文件可能不存在，忽略错误
      this.logger.warn(`删除配置文件失败: ${modelId}`, error);
    }
  }

  /**
   * 删除模型文件
   */
  private async deleteModelFile(filePath: string): Promise<void> {
    try {
      await fs.unlink(filePath);
      
    } catch (error) {
      // 文件可能不存在，忽略错误
      this.logger.warn(`删除模型文件失败: ${filePath}`, error);
    }
  }
}

# 群体协调服务 Docker 镜像

# 使用官方 Node.js 18 Alpine 镜像作为基础镜像
FROM node:18-alpine AS base

# 设置工作目录
WORKDIR /app

# 安装必要的系统依赖
RUN apk add --no-cache \
    dumb-init \
    && rm -rf /var/cache/apk/*

# 创建非root用户
RUN addgroup -g 1001 -S nodejs \
    && adduser -S nestjs -u 1001

# ================================
# 依赖安装阶段
# ================================
FROM base AS deps

# 复制包管理文件
COPY package*.json ./

# 安装所有依赖（包括开发依赖）
RUN npm ci --include=dev

# ================================
# 构建阶段
# ================================
FROM base AS builder

# 复制依赖
COPY --from=deps /app/node_modules ./node_modules

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 安装生产依赖
RUN npm ci --only=production && npm cache clean --force

# ================================
# 生产镜像
# ================================
FROM base AS runner

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=3010

# 复制必要文件
COPY --from=builder --chown=nestjs:nodejs /app/dist ./dist
COPY --from=builder --chown=nestjs:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nestjs:nodejs /app/package.json ./package.json

# 切换到非root用户
USER nestjs

# 暴露端口
EXPOSE 3010

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:3010/api/v1/coordination/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# 启动应用
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "dist/main.js"]

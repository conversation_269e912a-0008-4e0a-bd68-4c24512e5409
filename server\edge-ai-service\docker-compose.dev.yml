version: '3.8'

services:
  # 开发环境覆盖配置
  edge-ai-service:
    environment:
      - NODE_ENV=development
      - LOG_LEVEL=debug
    volumes:
      - .:/app
      - /app/node_modules
    command: npm run start:dev
    ports:
      - "3006:3006"
      - "9229:9229" # 调试端口

  # 开发数据库（使用不同端口避免冲突）
  mysql:
    environment:
      - MYSQL_ROOT_PASSWORD=dev_root_password
      - MYSQL_DATABASE=edge_ai_service_dev
      - MYSQL_USER=edge_ai_dev
      - MYSQL_PASSWORD=edge_ai_dev_password
    ports:
      - "3308:3306"

  # 开发Redis
  redis:
    ports:
      - "6381:6379"

  # 开发环境不需要Nginx
  nginx:
    profiles:
      - production

# 生态系统建设服务 (Ecosystem Service)

## 项目概述

生态系统建设服务是DL Engine项目的核心微服务之一，专门负责合作伙伴生态构建、开放API平台管理、第三方应用集成和行业标准制定。

## 🎯 核心功能

### 1. 合作伙伴管理
- **合作伙伴注册与审核**：支持多种类型合作伙伴的申请和审核流程
- **等级管理**：白金、黄金、白银、青铜、认证等级体系
- **性能监控**：收入、客户、项目、满意度等关键指标跟踪
- **认证管理**：合作伙伴资质认证和续期管理

### 2. API平台管理
- **API规范发布**：支持REST、GraphQL、WebSocket、gRPC等多种API类型
- **文档生成**：自动生成API文档和SDK
- **使用监控**：API调用统计、性能分析、错误跟踪
- **版本管理**：API版本控制和向后兼容性管理

### 3. 第三方应用集成
- **应用市场**：第三方应用的发布、审核和管理
- **应用分类**：Web应用、移动应用、桌面应用、IoT应用等
- **评价系统**：用户评价和反馈管理
- **指标监控**：下载量、活跃用户、收入等指标跟踪

### 4. 行业标准制定
- **标准创建**：行业标准的制定和发布
- **合规检查**：标准合规性验证和监控
- **认证流程**：标准认证申请和审核流程
- **要求管理**：标准要求和验收标准管理

## 🏗️ 项目结构

```
src/
├── main.ts                    # 应用入口文件
├── app.module.ts              # 根模块
├── app.controller.ts          # 根控制器
├── app.service.ts             # 根服务
├── modules/                   # 功能模块
│   ├── ecosystem/             # 生态系统核心模块
│   │   ├── ecosystem.module.ts
│   │   ├── ecosystem.controller.ts
│   │   └── ecosystem-platform.service.ts
│   ├── partners/              # 合作伙伴管理模块
│   │   ├── partners.module.ts
│   │   ├── partners.controller.ts
│   │   └── partners.service.ts
│   ├── api-platform/          # API平台管理模块
│   │   ├── api-platform.module.ts
│   │   ├── api-platform.controller.ts
│   │   └── api-platform.service.ts
│   ├── applications/          # 第三方应用模块
│   │   ├── applications.module.ts
│   │   ├── applications.controller.ts
│   │   └── applications.service.ts
│   ├── standards/             # 行业标准模块
│   │   ├── standards.module.ts
│   │   ├── standards.controller.ts
│   │   └── standards.service.ts
│   └── health/                # 健康检查模块
│       ├── health.module.ts
│       ├── health.controller.ts
│       └── health.service.ts
├── dto/                       # 数据传输对象
│   └── partner.dto.ts
├── entities/                  # 数据库实体（待实现）
└── common/                    # 公共组件（待实现）
```

## 🚀 快速开始

### 环境要求
- Node.js >= 18.0.0
- npm >= 8.0.0
- MySQL >= 8.0 (可选)
- Redis >= 6.0 (可选)

### 安装依赖
```bash
npm install
```

### 环境配置
复制环境配置文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置必要的参数：
```env
# 基础配置
NODE_ENV=development
PORT=3030
HOST=0.0.0.0

# 数据库配置
DB_TYPE=mysql
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=password
DB_DATABASE=ecosystem_service

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
```

### 启动服务
```bash
# 开发模式
npm run start:dev

# 生产模式
npm run build
npm run start:prod
```

## 📚 API文档

服务启动后，可以通过以下地址访问API文档：
- Swagger文档：`http://localhost:3030/api/docs`

### 主要API端点

#### 生态系统管理
- `GET /api/ecosystem/statistics` - 获取生态系统统计信息
- `POST /api/ecosystem/partners/register` - 注册合作伙伴
- `POST /api/ecosystem/apis/publish` - 发布API规范
- `POST /api/ecosystem/applications/submit` - 提交第三方应用
- `POST /api/ecosystem/standards/create` - 创建行业标准

#### 合作伙伴管理
- `GET /api/partners` - 获取合作伙伴列表
- `GET /api/partners/:id` - 获取合作伙伴详情
- `POST /api/partners` - 创建合作伙伴申请
- `PUT /api/partners/:id` - 更新合作伙伴信息
- `GET /api/partners/:id/performance` - 获取合作伙伴性能指标

#### API平台管理
- `GET /api/api-platform/apis` - 获取API列表
- `GET /api/api-platform/apis/:id` - 获取API详情
- `POST /api/api-platform/apis` - 发布API规范
- `GET /api/api-platform/apis/:id/usage` - 获取API使用统计
- `GET /api/api-platform/apis/:id/documentation` - 获取API文档

#### 第三方应用管理
- `GET /api/applications` - 获取应用列表
- `GET /api/applications/:id` - 获取应用详情
- `POST /api/applications` - 提交第三方应用
- `GET /api/applications/:id/metrics` - 获取应用指标
- `GET /api/applications/:id/reviews` - 获取应用评价

#### 行业标准管理
- `GET /api/standards` - 获取标准列表
- `GET /api/standards/:id` - 获取标准详情
- `POST /api/standards` - 创建行业标准
- `GET /api/standards/:id/compliance` - 获取标准合规信息
- `POST /api/standards/:id/certification` - 申请标准认证

#### 健康检查
- `GET /api/health` - 基础健康检查
- `GET /api/health/detailed` - 详细健康检查
- `GET /api/health/ready` - 就绪检查
- `GET /api/health/live` - 存活检查

## 🔧 开发指南

### 运行命令
```bash
# 开发模式
npm run start:dev

# 生产模式
npm run start:prod

# 构建
npm run build

# 测试
npm run test

# 代码检查
npm run lint

# 代码格式化
npm run format
```

### 代码规范
- 使用TypeScript进行开发
- 遵循NestJS最佳实践
- 使用ESLint和Prettier进行代码检查和格式化
- 编写单元测试和集成测试

## 📊 技术栈

### 核心技术
- **Node.js**: 18.x+
- **NestJS**: 10.x
- **TypeScript**: 5.x
- **Express**: HTTP服务器

### 数据库
- **MySQL**: 主数据库
- **Redis**: 缓存和会话存储

### 开发工具
- **Jest**: 单元测试
- **ESLint**: 代码检查
- **Prettier**: 代码格式化
- **Swagger**: API文档

## 🚀 部署说明

### Docker部署
```bash
# 构建镜像
docker build -t ecosystem-service .

# 运行容器
docker run -p 3030:3030 ecosystem-service
```

### 环境变量
详细的环境变量配置请参考 `.env.example` 文件。

## 📈 监控和日志

- 健康检查端点用于监控服务状态
- 结构化日志记录关键操作
- 性能指标收集和分析
- 错误跟踪和报警

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证。

import { SetMetadata } from '@nestjs/common';

export const CACHE_KEY = 'cache_key';
export const CACHE_TTL = 'cache_ttl';

/**
 * 缓存装饰器
 * @param key 缓存键模板，支持参数占位符 {0}, {1}, {2}...
 * @param ttl 缓存时间（秒），默认3600秒（1小时）
 */
export const Cacheable = (key: string, ttl: number = 3600) => {
  return (target: any, propertyName: string, descriptor: PropertyDescriptor) => {
    SetMetadata(CACHE_KEY, key)(target, propertyName, descriptor);
    SetMetadata(CACHE_TTL, ttl)(target, propertyName, descriptor);
  };
};

/**
 * 缓存清除装饰器
 * @param patterns 要清除的缓存键模式数组
 */
export const CacheEvict = (patterns: string[]) => {
  return SetMetadata('cache_evict', patterns);
};

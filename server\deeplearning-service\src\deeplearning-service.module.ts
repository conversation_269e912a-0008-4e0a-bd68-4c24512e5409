/**
 * 深度学习推理服务主模块
 * 
 * 配置和组织所有服务模块、控制器、提供者等
 */

import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';

// 控制器
import { InferenceController } from './controllers/inference.controller';
import { ModelController } from './controllers/model.controller';
import { MonitoringController } from './controllers/monitoring.controller';
import { HealthController } from './controllers/health.controller';

// 服务
import { ModelInferenceService } from './services/model-inference.service';
import { ModelManagementService } from './services/model-management.service';
import { MonitoringService } from './services/monitoring.service';

// 提供者
// import { RedisProvider } from './providers/redis.provider';
import { ConfigProvider } from './providers/config.provider';

/**
 * 深度学习推理服务主模块
 */
@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
      cache: true,
      expandVariables: true,
      validationOptions: {
        allowUnknown: false,
        abortEarly: true,
      },
    }),

    // 事件发射器模块
    EventEmitterModule.forRoot({
      wildcard: false,
      delimiter: '.',
      newListener: false,
      removeListener: false,
      maxListeners: 20,
      verboseMemoryLeak: false,
      ignoreErrors: false,
    }),

    // 定时任务模块
    ScheduleModule.forRoot(),
  ],

  controllers: [
    InferenceController,
    ModelController,
    MonitoringController,
    HealthController,
  ],

  providers: [
    // 核心服务
    ModelInferenceService,
    ModelManagementService,
    MonitoringService,

    // 基础设施提供者
    // RedisProvider,
    ConfigProvider,

    // Redis配置工厂 (暂时禁用)
    // {
    //   provide: 'REDIS_CONFIG',
    //   useFactory: (configProvider: ConfigProvider) => ({
    //     host: configProvider.get('REDIS_HOST', 'localhost'),
    //     port: parseInt(configProvider.get('REDIS_PORT', '6379')),
    //     password: configProvider.get('REDIS_PASSWORD'),
    //     db: parseInt(configProvider.get('REDIS_DB', '0')),
    //     connectTimeout: parseInt(configProvider.get('REDIS_CONNECT_TIMEOUT', '10000')),
    //     commandTimeout: parseInt(configProvider.get('REDIS_COMMAND_TIMEOUT', '5000')),
    //     retryStrategy: (times: number) => Math.min(times * 50, 2000),
    //     enableReadyCheck: false,
    //     maxRetriesPerRequest: null,
    //   }),
    //   inject: [ConfigProvider],
    // },

    // 推理服务配置工厂
    {
      provide: 'INFERENCE_CONFIG',
      useFactory: (configProvider: ConfigProvider) => ({
        maxConcurrentInferences: parseInt(configProvider.get('MAX_CONCURRENT_INFERENCES', '10')),
        maxQueueSize: parseInt(configProvider.get('MAX_QUEUE_SIZE', '1000')),
        modelCacheSize: parseInt(configProvider.get('MODEL_CACHE_SIZE', '5')),
        defaultTimeout: parseInt(configProvider.get('INFERENCE_TIMEOUT', '30000')),
        cleanupInterval: parseInt(configProvider.get('CLEANUP_INTERVAL', '3600000')),
        metricsRetention: parseInt(configProvider.get('METRICS_RETENTION', '86400000')), // 24小时
      }),
      inject: [ConfigProvider],
    },

    // 模型管理配置工厂
    {
      provide: 'MODEL_CONFIG',
      useFactory: (configProvider: ConfigProvider) => ({
        modelStoragePath: configProvider.get('MODEL_STORAGE_PATH', './models'),
        maxModelSize: parseInt(configProvider.get('MAX_MODEL_SIZE', '**********')), // 1GB
        supportedFormats: configProvider.get('SUPPORTED_MODEL_FORMATS', 'onnx,pytorch,tensorflow').split(','),
        autoLoadModels: configProvider.get('AUTO_LOAD_MODELS', 'true') === 'true',
        modelRegistryUrl: configProvider.get('MODEL_REGISTRY_URL'),
      }),
      inject: [ConfigProvider],
    },

    // 监控配置工厂
    {
      provide: 'MONITORING_CONFIG',
      useFactory: (configProvider: ConfigProvider) => ({
        enableMetrics: configProvider.get('ENABLE_METRICS', 'true') === 'true',
        metricsInterval: parseInt(configProvider.get('METRICS_INTERVAL', '60000')),
        enableHealthCheck: configProvider.get('ENABLE_HEALTH_CHECK', 'true') === 'true',
        healthCheckInterval: parseInt(configProvider.get('HEALTH_CHECK_INTERVAL', '30000')),
        alertThresholds: {
          errorRate: parseFloat(configProvider.get('ALERT_ERROR_RATE_THRESHOLD', '0.1')),
          latency: parseInt(configProvider.get('ALERT_LATENCY_THRESHOLD', '5000')),
          queueSize: parseInt(configProvider.get('ALERT_QUEUE_SIZE_THRESHOLD', '500')),
          memoryUsage: parseFloat(configProvider.get('ALERT_MEMORY_USAGE_THRESHOLD', '0.8')),
        },
      }),
      inject: [ConfigProvider],
    },
  ],

  exports: [
    ModelInferenceService,
    ModelManagementService,
    MonitoringService,
    // RedisProvider,
    ConfigProvider,
  ],
})
export class DeepLearningServiceModule {
  constructor() {
    console.log('🧠 深度学习推理服务模块已初始化');
  }
}

#!/bin/bash

# Docker部署脚本
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置
ENVIRONMENT=${1:-production}
COMPOSE_FILE="docker-compose.yml"

if [ "$ENVIRONMENT" = "development" ] || [ "$ENVIRONMENT" = "dev" ]; then
    COMPOSE_FILE="docker-compose.dev.yml"
fi

echo -e "${GREEN}开始部署生态系统服务 (环境: ${ENVIRONMENT})...${NC}"

# 检查Docker和docker-compose是否可用
if ! command -v docker &> /dev/null; then
    echo -e "${RED}错误: Docker 未安装${NC}"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}错误: docker-compose 未安装${NC}"
    exit 1
fi

# 检查配置文件是否存在
if [ ! -f "$COMPOSE_FILE" ]; then
    echo -e "${RED}错误: 配置文件 $COMPOSE_FILE 不存在${NC}"
    exit 1
fi

# 创建必要的目录
echo -e "${YELLOW}创建必要的目录...${NC}"
mkdir -p docker/nginx/conf.d
mkdir -p docker/mysql/init
mkdir -p storage
mkdir -p logs

# 停止现有服务
echo -e "${YELLOW}停止现有服务...${NC}"
docker-compose -f $COMPOSE_FILE down

# 拉取最新镜像
echo -e "${YELLOW}拉取最新镜像...${NC}"
docker-compose -f $COMPOSE_FILE pull

# 启动服务
echo -e "${YELLOW}启动服务...${NC}"
docker-compose -f $COMPOSE_FILE up -d

# 等待服务启动
echo -e "${YELLOW}等待服务启动...${NC}"
sleep 30

# 检查服务状态
echo -e "${YELLOW}检查服务状态...${NC}"
docker-compose -f $COMPOSE_FILE ps

# 健康检查
echo -e "${YELLOW}执行健康检查...${NC}"
if [ "$ENVIRONMENT" = "development" ] || [ "$ENVIRONMENT" = "dev" ]; then
    HEALTH_URL="http://localhost:3030/health"
else
    HEALTH_URL="http://localhost/health"
fi

# 等待服务就绪
MAX_ATTEMPTS=30
ATTEMPT=1

while [ $ATTEMPT -le $MAX_ATTEMPTS ]; do
    if curl -f $HEALTH_URL > /dev/null 2>&1; then
        echo -e "${GREEN}服务健康检查通过!${NC}"
        break
    else
        echo -e "${YELLOW}等待服务就绪... (尝试 $ATTEMPT/$MAX_ATTEMPTS)${NC}"
        sleep 10
        ATTEMPT=$((ATTEMPT + 1))
    fi
done

if [ $ATTEMPT -gt $MAX_ATTEMPTS ]; then
    echo -e "${RED}服务健康检查失败!${NC}"
    echo -e "${YELLOW}查看服务日志:${NC}"
    docker-compose -f $COMPOSE_FILE logs --tail=50
    exit 1
fi

# 显示服务信息
echo -e "${GREEN}部署完成!${NC}"
echo -e "${BLUE}服务访问地址:${NC}"

if [ "$ENVIRONMENT" = "development" ] || [ "$ENVIRONMENT" = "dev" ]; then
    echo -e "  - API服务: http://localhost:3030"
    echo -e "  - API文档: http://localhost:3030/api/docs"
    echo -e "  - 健康检查: http://localhost:3030/health"
    echo -e "  - phpMyAdmin: http://localhost:8080"
    echo -e "  - Redis Commander: http://localhost:8081"
else
    echo -e "  - API服务: http://localhost"
    echo -e "  - API文档: http://localhost/api/docs"
    echo -e "  - 健康检查: http://localhost/health"
fi

echo -e "${BLUE}管理命令:${NC}"
echo -e "  - 查看日志: docker-compose -f $COMPOSE_FILE logs -f"
echo -e "  - 停止服务: docker-compose -f $COMPOSE_FILE down"
echo -e "  - 重启服务: docker-compose -f $COMPOSE_FILE restart"
echo -e "  - 查看状态: docker-compose -f $COMPOSE_FILE ps"

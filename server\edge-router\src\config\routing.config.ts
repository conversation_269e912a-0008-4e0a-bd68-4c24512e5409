import { registerAs } from '@nestjs/config';

export const routingConfig = registerAs('routing', () => ({
  weights: {
    geographic: parseFloat(process.env.ROUTING_WEIGHTS_GEOGRAPHIC) || 0.25,
    latency: parseFloat(process.env.ROUTING_WEIGHTS_LATENCY) || 0.30,
    load: parseFloat(process.env.ROUTING_WEIGHTS_LOAD) || 0.25,
    reliability: parseFloat(process.env.ROUTING_WEIGHTS_RELIABILITY) || 0.20,
  },
  cache: {
    locationTtl: parseInt(process.env.CACHE_TTL_LOCATION, 10) || 3600000, // 1小时
    networkQualityTtl: parseInt(process.env.CACHE_TTL_NETWORK_QUALITY, 10) || 300000, // 5分钟
    maxSize: parseInt(process.env.CACHE_MAX_SIZE, 10) || 10000,
  },
  networkMonitoring: {
    enabled: process.env.NETWORK_MONITORING_ENABLED === 'true',
    interval: parseInt(process.env.NETWORK_MONITORING_INTERVAL, 10) || 30000,
    latencyTimeout: parseInt(process.env.LATENCY_MEASUREMENT_TIMEOUT, 10) || 5000,
  },
  geoIpService: {
    url: process.env.GEO_IP_SERVICE_URL || 'http://ip-api.com/json',
    timeout: parseInt(process.env.GEO_IP_SERVICE_TIMEOUT, 10) || 5000,
    enabled: process.env.GEO_IP_SERVICE_ENABLED === 'true',
  },
  performance: {
    maxRoutingHistory: parseInt(process.env.MAX_ROUTING_HISTORY, 10) || 100,
    routingDecisionTimeout: parseInt(process.env.ROUTING_DECISION_TIMEOUT, 10) || 10000,
    concurrentRoutingLimit: parseInt(process.env.CONCURRENT_ROUTING_LIMIT, 10) || 1000,
  },
}));

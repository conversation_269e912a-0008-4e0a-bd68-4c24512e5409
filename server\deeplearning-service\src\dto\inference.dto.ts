/**
 * 推理相关数据传输对象
 */

import { IsString, IsNumber, IsOptional, IsObject, IsArray, IsEnum, Min, Max } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * 提交推理请求DTO
 */
export class SubmitInferenceDto {
  @ApiProperty({ description: '模型ID', example: 'model_001' })
  @IsString()
  modelId: string;

  @ApiProperty({ description: '输入数据', example: { text: 'Hello world' } })
  @IsObject()
  input: any;

  @ApiProperty({ description: '用户ID', example: 'user_123' })
  @IsString()
  userId: string;

  @ApiPropertyOptional({ description: '优先级 (1-10)', example: 5, minimum: 1, maximum: 10 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  priority?: number;

  @ApiPropertyOptional({ description: '超时时间 (毫秒)', example: 30000 })
  @IsOptional()
  @IsNumber()
  @Min(1000)
  timeout?: number;

  @ApiPropertyOptional({ description: '会话ID', example: 'session_456' })
  @IsOptional()
  @IsString()
  sessionId?: string;

  @ApiPropertyOptional({ description: '元数据', example: { source: 'web' } })
  @IsOptional()
  @IsObject()
  metadata?: { [key: string]: any };
}

/**
 * 批量推理请求DTO
 */
export class BatchInferenceDto {
  @ApiProperty({ description: '模型ID', example: 'model_001' })
  @IsString()
  modelId: string;

  @ApiProperty({ description: '输入数据数组', example: [{ text: 'Hello' }, { text: 'World' }] })
  @IsArray()
  inputs: any[];

  @ApiProperty({ description: '用户ID', example: 'user_123' })
  @IsString()
  userId: string;

  @ApiPropertyOptional({ description: '优先级 (1-10)', example: 5, minimum: 1, maximum: 10 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  priority?: number;

  @ApiPropertyOptional({ description: '超时时间 (毫秒)', example: 30000 })
  @IsOptional()
  @IsNumber()
  @Min(1000)
  timeout?: number;

  @ApiPropertyOptional({ description: '会话ID', example: 'session_456' })
  @IsOptional()
  @IsString()
  sessionId?: string;

  @ApiPropertyOptional({ description: '元数据', example: { source: 'batch' } })
  @IsOptional()
  @IsObject()
  metadata?: { [key: string]: any };
}

/**
 * 推理结果DTO
 */
export class InferenceResultDto {
  @ApiProperty({ description: '请求ID', example: 'req_123456789' })
  requestId: string;

  @ApiProperty({ description: '模型ID', example: 'model_001' })
  modelId: string;

  @ApiProperty({ description: '输出结果', example: { prediction: 'positive', score: 0.95 } })
  output: any;

  @ApiProperty({ description: '置信度', example: 0.95, minimum: 0, maximum: 1 })
  confidence: number;

  @ApiProperty({ description: '处理时间 (毫秒)', example: 150 })
  processingTime: number;

  @ApiProperty({ description: '队列等待时间 (毫秒)', example: 50 })
  queueTime: number;

  @ApiProperty({ description: '状态', example: 'success', enum: ['success', 'error'] })
  @IsEnum(['success', 'error'])
  status: 'success' | 'error';

  @ApiPropertyOptional({ description: '错误信息', example: null })
  error?: string;

  @ApiProperty({ description: '元数据', example: { modelVersion: '1.0.0' } })
  metadata: { [key: string]: any };

  @ApiProperty({ description: '时间戳', example: 1640995200000 })
  timestamp: number;
}

/**
 * 推理状态DTO
 */
export class InferenceStatusDto {
  @ApiProperty({ description: '请求ID', example: 'req_123456789' })
  requestId: string;

  @ApiProperty({ 
    description: '状态', 
    example: 'processing', 
    enum: ['queued', 'processing', 'completed', 'failed', 'cancelled'] 
  })
  @IsEnum(['queued', 'processing', 'completed', 'failed', 'cancelled'])
  status: 'queued' | 'processing' | 'completed' | 'failed' | 'cancelled';

  @ApiPropertyOptional({ description: '队列位置', example: 3 })
  queuePosition?: number;

  @ApiPropertyOptional({ description: '预估完成时间 (毫秒)', example: 15000 })
  estimatedTime?: number;

  @ApiPropertyOptional({ description: '进度 (0-1)', example: 0.6, minimum: 0, maximum: 1 })
  progress?: number;
}

/**
 * 推理历史查询DTO
 */
export class InferenceHistoryQueryDto {
  @ApiProperty({ description: '用户ID', example: 'user_123' })
  @IsString()
  userId: string;

  @ApiPropertyOptional({ description: '模型ID', example: 'model_001' })
  @IsOptional()
  @IsString()
  modelId?: string;

  @ApiPropertyOptional({ description: '开始时间', example: 1640995200000 })
  @IsOptional()
  @IsNumber()
  startTime?: number;

  @ApiPropertyOptional({ description: '结束时间', example: 1640995200000 })
  @IsOptional()
  @IsNumber()
  endTime?: number;

  @ApiPropertyOptional({ description: '状态过滤', example: 'success' })
  @IsOptional()
  @IsEnum(['success', 'error'])
  status?: 'success' | 'error';

  @ApiPropertyOptional({ description: '限制数量', example: 10, minimum: 1, maximum: 100 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  limit?: number;

  @ApiPropertyOptional({ description: '偏移量', example: 0, minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  offset?: number;
}

/**
 * 推理历史记录DTO
 */
export class InferenceHistoryDto {
  @ApiProperty({ description: '请求ID', example: 'req_123456789' })
  requestId: string;

  @ApiProperty({ description: '模型ID', example: 'model_001' })
  modelId: string;

  @ApiProperty({ description: '模型名称', example: 'Text Classifier' })
  modelName: string;

  @ApiProperty({ description: '状态', example: 'success' })
  status: string;

  @ApiProperty({ description: '处理时间 (毫秒)', example: 150 })
  processingTime: number;

  @ApiProperty({ description: '置信度', example: 0.95 })
  confidence: number;

  @ApiProperty({ description: '创建时间', example: 1640995200000 })
  createdAt: number;

  @ApiProperty({ description: '完成时间', example: 1640995200150 })
  completedAt: number;

  @ApiPropertyOptional({ description: '错误信息', example: null })
  error?: string;
}

/**
 * 推理统计DTO
 */
export class InferenceStatsDto {
  @ApiProperty({ description: '总请求数', example: 1000 })
  totalRequests: number;

  @ApiProperty({ description: '活跃请求数', example: 5 })
  activeRequests: number;

  @ApiProperty({ description: '队列中请求数', example: 10 })
  queuedRequests: number;

  @ApiProperty({ description: '已完成请求数', example: 950 })
  completedRequests: number;

  @ApiProperty({ description: '失败请求数', example: 45 })
  failedRequests: number;

  @ApiProperty({ description: '平均延迟 (毫秒)', example: 125.5 })
  averageLatency: number;

  @ApiProperty({ description: '吞吐量 (请求/分钟)', example: 120.5 })
  throughput: number;

  @ApiProperty({ description: '错误率', example: 0.045 })
  errorRate: number;

  @ApiProperty({ description: '模型利用率', example: { model_001: 500, model_002: 300 } })
  modelUtilization: { [modelId: string]: number };

  @ApiProperty({ 
    description: '资源使用情况', 
    example: { cpuUsage: 0.65, memoryUsage: 512.5, gpuUsage: 0.8 } 
  })
  resourceUsage: {
    cpuUsage: number;
    memoryUsage: number;
    gpuUsage?: number;
  };
}

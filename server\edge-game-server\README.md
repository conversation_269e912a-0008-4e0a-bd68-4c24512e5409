# 边缘游戏服务器 (Edge Game Server)

边缘游戏服务器是DL引擎生态系统中的轻量级边缘节点服务，专为边缘计算环境设计，提供低延迟的游戏服务和实时通信功能。

## 🚀 主要特性

### 核心功能
- **轻量级设计**: 专为边缘节点优化，资源占用小
- **实时通信**: 基于WebRTC的低延迟通信
- **智能负载均衡**: 自适应负载分配和资源管理
- **健康监控**: 实时健康检查和性能监控
- **数据同步**: 与中心节点的智能数据同步

### 技术特性
- **微服务架构**: 基于NestJS的模块化设计
- **WebSocket支持**: 实时双向通信
- **Redis缓存**: 高性能本地缓存
- **事件驱动**: 基于事件的松耦合架构
- **容器化部署**: Docker支持

## 📋 系统要求

- Node.js >= 18.0.0
- npm >= 8.0.0
- Redis (可选，用于缓存)
- Docker (可选，用于容器化部署)

## 🛠️ 安装和配置

### 1. 克隆项目
```bash
git clone <repository-url>
cd edge-game-server
```

### 2. 安装依赖
```bash
npm install
```

### 3. 环境配置
复制环境配置文件并根据需要修改：
```bash
cp .env.example .env.edge
```

主要配置项：
```env
# 边缘节点配置
EDGE_NODE_ID=edge-node-001
EDGE_REGION=asia-east-1
EDGE_HTTP_PORT=8080
EDGE_GAME_SERVER_PORT=3030

# 中心节点连接
CENTRAL_HUB_URL=http://central-hub:3000

# 资源限制
MAX_USERS_PER_EDGE=50
MAX_CONCURRENT_GAMES=10

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
```

### 4. 启动服务

#### 开发模式
```bash
npm run start:dev
```

#### 生产模式
```bash
npm run build
npm run start:prod
```

#### Docker部署
```bash
# 构建镜像
npm run docker:build

# 运行容器
npm run docker:run

# 使用Docker Compose
npm run docker:compose
```

## 🏗️ 项目结构

```
src/
├── controllers/          # 控制器
│   ├── edge-game-server.controller.ts
│   ├── edge-health.controller.ts
│   ├── edge-instance.controller.ts
│   ├── edge-cache.controller.ts
│   ├── edge-sync.controller.ts
│   ├── edge-webrtc.controller.ts
│   └── edge-monitoring.controller.ts
├── services/             # 服务层
│   ├── edge-node-registration.service.ts
│   ├── edge-load-balancer.service.ts
│   ├── edge-health-check.service.ts
│   ├── edge-instance.service.ts
│   ├── edge-cache.service.ts
│   ├── edge-sync.service.ts
│   ├── edge-webrtc.service.ts
│   └── edge-monitoring.service.ts
├── modules/              # 功能模块
│   ├── edge-instance.module.ts
│   ├── edge-cache.module.ts
│   ├── edge-sync.module.ts
│   ├── edge-webrtc.module.ts
│   └── edge-monitoring.module.ts
├── gateways/             # WebSocket网关
│   └── edge-webrtc.gateway.ts
├── edge-game-server.module.ts
└── main.ts
```

## 🔧 API文档

服务启动后，可以通过以下地址访问API文档：
- Swagger UI: `http://localhost:8080/api/docs`

### 主要API端点

#### 节点管理
- `GET /api/edge/info` - 获取节点信息
- `PUT /api/edge/status` - 更新节点状态
- `GET /api/edge/stats` - 获取统计信息

#### 健康检查
- `GET /api/edge/health` - 基础健康检查
- `GET /api/edge/health/detailed` - 详细健康检查
- `GET /api/edge/health/stats` - 健康统计信息

#### 实例管理
- `POST /api/edge/instances/games` - 创建游戏实例
- `GET /api/edge/instances/games` - 获取游戏实例列表
- `PUT /api/edge/instances/games/:id/start` - 启动游戏实例

#### 负载均衡
- `GET /api/edge/load-balance` - 获取负载均衡信息
- `POST /api/edge/select-node` - 选择最佳节点

## 🔍 监控和日志

### 健康检查
服务提供多层次的健康检查：
- CPU使用率监控
- 内存使用率监控
- 磁盘使用率监控
- 网络延迟监控
- 服务状态监控

### 性能指标
- 实时性能指标收集
- 资源使用情况统计
- 用户连接数统计
- 游戏实例统计

### 日志管理
- 结构化日志输出
- 日志级别控制
- 日志文件轮转

## 🌐 WebRTC通信

### 信令服务
- WebSocket信令通道
- 房间管理
- 用户状态管理

### 对等连接
- P2P连接建立
- 媒体流传输
- 数据通道通信

## 🔄 数据同步

### 同步策略
- 增量同步
- 批量同步
- 冲突解决

### 同步内容
- 游戏状态同步
- 用户数据同步
- 配置信息同步

## 🧪 测试

```bash
# 单元测试
npm run test

# 端到端测试
npm run test:e2e

# 测试覆盖率
npm run test:cov
```

## 📦 部署

### Docker部署
```bash
# 构建镜像
docker build -t edge-game-server .

# 运行容器
docker run -p 8080:8080 -p 3030:3030 edge-game-server
```

### Kubernetes部署
参考 `k8s/` 目录下的配置文件。

## 🤝 贡献

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请通过以下方式联系：
- 提交 Issue
- 发送邮件到开发团队
- 查看项目文档

## 🔗 相关项目

- [DL Engine Core](../dl-engine-core) - DL引擎核心
- [Central Hub](../central-hub) - 中心节点服务
- [Edge Registry](../edge-registry) - 边缘节点注册服务

groups:
  - name: edge-ai-service
    rules:
      # 服务可用性告警
      - alert: EdgeAIServiceDown
        expr: up{job="edge-ai-service"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "边缘AI服务不可用"
          description: "边缘AI服务已停止响应超过1分钟"

      # 高错误率告警
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "边缘AI服务错误率过高"
          description: "5分钟内错误率超过10%"

      # 高响应时间告警
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "边缘AI服务响应时间过长"
          description: "95%的请求响应时间超过1秒"

      # 内存使用率告警
      - alert: HighMemoryUsage
        expr: (process_resident_memory_bytes / process_virtual_memory_max_bytes) > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "边缘AI服务内存使用率过高"
          description: "内存使用率超过80%"

      # CPU使用率告警
      - alert: HighCPUUsage
        expr: rate(process_cpu_seconds_total[5m]) > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "边缘AI服务CPU使用率过高"
          description: "CPU使用率超过80%"

  - name: edge-devices
    rules:
      # 边缘设备离线告警
      - alert: EdgeDeviceOffline
        expr: edge_device_status == 0
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "边缘设备离线"
          description: "设备 {{ $labels.device_id }} 已离线超过2分钟"

      # 设备温度过高告警
      - alert: DeviceHighTemperature
        expr: edge_device_temperature > 80
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "边缘设备温度过高"
          description: "设备 {{ $labels.device_id }} 温度达到 {{ $value }}°C"

      # 设备CPU使用率过高告警
      - alert: DeviceHighCPUUsage
        expr: edge_device_cpu_usage > 90
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "边缘设备CPU使用率过高"
          description: "设备 {{ $labels.device_id }} CPU使用率达到 {{ $value }}%"

      # 设备内存使用率过高告警
      - alert: DeviceHighMemoryUsage
        expr: edge_device_memory_usage > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "边缘设备内存使用率过高"
          description: "设备 {{ $labels.device_id }} 内存使用率达到 {{ $value }}%"

  - name: inference
    rules:
      # 推理队列积压告警
      - alert: InferenceQueueBacklog
        expr: edge_inference_queue_size > 100
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "推理队列积压"
          description: "推理队列中有 {{ $value }} 个待处理请求"

      # 推理失败率过高告警
      - alert: HighInferenceFailureRate
        expr: rate(edge_inference_failures_total[5m]) / rate(edge_inference_requests_total[5m]) > 0.05
        for: 3m
        labels:
          severity: warning
        annotations:
          summary: "推理失败率过高"
          description: "5分钟内推理失败率超过5%"

  - name: database
    rules:
      # 数据库连接数过高告警
      - alert: HighDatabaseConnections
        expr: mysql_global_status_threads_connected > 80
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "数据库连接数过高"
          description: "MySQL连接数达到 {{ $value }}"

      # 数据库查询时间过长告警
      - alert: SlowDatabaseQueries
        expr: mysql_global_status_slow_queries > 10
        for: 1m
        labels:
          severity: warning
        annotations:
          summary: "数据库慢查询过多"
          description: "检测到 {{ $value }} 个慢查询"

/**
 * 简化的测试启动文件
 */

import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { Module, Controller, Get } from '@nestjs/common';

/**
 * 简单的测试控制器
 */
@Controller()
export class TestController {
  @Get('health')
  getHealth() {
    return {
      status: 'ok',
      timestamp: Date.now(),
      message: '深度学习推理服务运行正常'
    };
  }

  @Get('info')
  getInfo() {
    return {
      service: 'deeplearning-service',
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      port: process.env.PORT || 3020
    };
  }
}

/**
 * 简化的测试模块
 */
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
  ],
  controllers: [TestController],
})
export class TestModule {}

/**
 * 启动测试应用
 */
async function bootstrap() {
  const logger = new Logger('TestBootstrap');

  try {
    // 创建NestJS应用
    const app = await NestFactory.create(TestModule, {
      logger: ['log', 'error', 'warn'],
    });

    // 配置全局验证管道
    app.useGlobalPipes(new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }));

    // 启动HTTP服务
    const port = process.env.PORT || 3020;
    const host = process.env.HOST || '0.0.0.0';
    
    await app.listen(port, host);

    logger.log(`🚀 测试服务已启动`);
    logger.log(`📍 服务地址: http://${host}:${port}`);
    logger.log(`📍 健康检查: http://${host}:${port}/health`);
    logger.log(`📍 服务信息: http://${host}:${port}/info`);

  } catch (error) {
    logger.error('测试服务启动失败:', error);
    process.exit(1);
  }
}

// 启动应用
bootstrap();

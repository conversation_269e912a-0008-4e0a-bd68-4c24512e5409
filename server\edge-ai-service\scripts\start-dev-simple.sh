#!/bin/bash

# 边缘AI服务开发环境启动脚本（简化版）
# 此脚本可以在没有数据库和Redis的情况下启动服务

echo "🚀 启动边缘AI计算服务（开发模式）"

# 检查Node.js版本
NODE_VERSION=$(node --version)
echo "📦 Node.js版本: $NODE_VERSION"

# 检查npm版本
NPM_VERSION=$(npm --version)
echo "📦 npm版本: $NPM_VERSION"

# 设置环境变量（开发模式）
export NODE_ENV=development
export PORT=3001
export LOG_LEVEL=debug

# 数据库配置（可选）
export DB_HOST=localhost
export DB_PORT=3306
export DB_USERNAME=root
export DB_PASSWORD=
export DB_DATABASE=edge_ai_service

# Redis配置（可选）
export REDIS_HOST=localhost
export REDIS_PORT=6379

# 跨域配置
export ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:8080

echo "🔧 环境变量已设置"
echo "   - NODE_ENV: $NODE_ENV"
echo "   - PORT: $PORT"
echo "   - 数据库: $DB_HOST:$DB_PORT/$DB_DATABASE"
echo "   - Redis: $REDIS_HOST:$REDIS_PORT"

# 启动服务
echo "🎯 启动服务..."
npm run start:dev

echo "✅ 服务启动完成"
echo "📖 API文档: http://localhost:$PORT/api/docs"
echo "🔗 服务地址: http://localhost:$PORT/api/v1"

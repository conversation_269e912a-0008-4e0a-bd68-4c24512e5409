/**
 * 群体协调服务主模块
 */
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';

// 服务
import { GroupCoordinationService } from './services/group-coordination.service';

// 控制器
import { CoordinationController } from './controllers/coordination.controller';

@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
      cache: true,
      expandVariables: true
    }),

    // 事件发射器模块
    EventEmitterModule.forRoot({
      wildcard: false,
      delimiter: '.',
      newListener: false,
      removeListener: false,
      maxListeners: 10,
      verboseMemoryLeak: false,
      ignoreErrors: false,
    }),

    // 定时任务模块
    ScheduleModule.forRoot(),
  ],

  controllers: [
    CoordinationController
  ],

  providers: [
    GroupCoordinationService,
    
    // 全局提供者
    {
      provide: 'APP_CONFIG',
      useFactory: (configService: ConfigService) => ({
        name: 'coordination-service',
        version: '1.0.0',
        environment: configService.get<string>('NODE_ENV', 'development'),
        port: configService.get<number>('PORT', 3010),
        debug: configService.get<boolean>('DEBUG', false)
      }),
      inject: [ConfigService]
    },

    // Redis 配置
    {
      provide: 'REDIS_CONFIG',
      useFactory: (configService: ConfigService) => ({
        host: configService.get<string>('REDIS_HOST', 'localhost'),
        port: configService.get<number>('REDIS_PORT', 6379),
        password: configService.get<string>('REDIS_PASSWORD'),
        db: configService.get<number>('REDIS_DB', 0),
        retryDelayOnFailover: 100,
        enableReadyCheck: false,
        maxRetriesPerRequest: null,
      }),
      inject: [ConfigService]
    }
  ],

  exports: [
    GroupCoordinationService
  ]
})
export class CoordinationServiceModule {
  constructor(private configService: ConfigService) {
    // 模块初始化日志
    console.log('群体协调服务模块已加载');
    console.log(`环境: ${this.configService.get<string>('NODE_ENV', 'development')}`);
    console.log(`Redis: ${this.configService.get<string>('REDIS_HOST', 'localhost')}:${this.configService.get<number>('REDIS_PORT', 6379)}`);
  }
}

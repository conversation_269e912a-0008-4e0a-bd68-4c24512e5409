import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';

import { EdgeRegistryService } from '../services/edge-registry.service';
import { RegisterNodeDto } from '../dto/register-node.dto';
import { UpdateHeartbeatDto } from '../dto/update-node.dto';
import { SelectOptimalNodeDto, LoadBalancingStrategy } from '../dto/select-node.dto';
import { ApiResponseDto, NodeResponseDto, ClusterStatsResponseDto } from '../dto/response.dto';

/**
 * 边缘注册中心控制器
 */
@ApiTags('edge-registry')
@Controller('edge')
export class EdgeRegistryController {
  private readonly logger = new Logger(EdgeRegistryController.name);

  constructor(
    private readonly edgeRegistryService: EdgeRegistryService,
  ) {}

  /**
   * 注册边缘节点
   */
  @Post('register')
  @ApiOperation({ summary: '注册边缘节点', description: '注册新的边缘节点到注册中心' })
  @ApiResponse({ 
    status: 201, 
    description: '注册成功', 
    type: ApiResponseDto<NodeResponseDto> 
  })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 409, description: '节点已存在' })
  async registerNode(@Body() registerDto: RegisterNodeDto): Promise<ApiResponseDto<NodeResponseDto>> {
    try {
      this.logger.log(`注册边缘节点: ${registerDto.nodeId}`);
      
      const node = await this.edgeRegistryService.registerNode(registerDto);
      
      const nodeResponse: NodeResponseDto = {
        nodeId: node.nodeId,
        region: node.region,
        endpoint: node.endpoint,
        status: node.status,
        lastHeartbeat: node.lastHeartbeat.toISOString(),
        registeredAt: node.registeredAt.toISOString(),
        metrics: node.metrics,
        capabilities: node.capabilities,
      };

      return ApiResponseDto.success('节点注册成功', nodeResponse);
    } catch (error) {
      this.logger.error(`注册节点失败: ${error.message}`, error.stack);
      throw new HttpException(
        ApiResponseDto.error(`注册节点失败: ${error.message}`, 'REGISTRATION_FAILED'),
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  /**
   * 注销边缘节点
   */
  @Delete(':nodeId')
  @ApiOperation({ summary: '注销边缘节点', description: '从注册中心注销指定的边缘节点' })
  @ApiParam({ name: 'nodeId', description: '节点ID', example: 'edge-node-001' })
  @ApiResponse({ status: 200, description: '注销成功' })
  @ApiResponse({ status: 404, description: '节点不存在' })
  async unregisterNode(@Param('nodeId') nodeId: string): Promise<ApiResponseDto> {
    try {
      this.logger.log(`注销边缘节点: ${nodeId}`);
      
      const success = await this.edgeRegistryService.unregisterNode(nodeId);
      
      if (!success) {
        throw new HttpException(
          ApiResponseDto.error('节点不存在', 'NODE_NOT_FOUND'),
          HttpStatus.NOT_FOUND,
        );
      }

      return ApiResponseDto.success('节点注销成功');
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      
      this.logger.error(`注销节点失败: ${error.message}`, error.stack);
      throw new HttpException(
        ApiResponseDto.error(`注销节点失败: ${error.message}`, 'UNREGISTRATION_FAILED'),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 更新节点心跳
   */
  @Put(':nodeId/heartbeat')
  @ApiOperation({ summary: '更新节点心跳', description: '更新指定节点的心跳信息和指标' })
  @ApiParam({ name: 'nodeId', description: '节点ID', example: 'edge-node-001' })
  @ApiResponse({ status: 200, description: '心跳更新成功' })
  @ApiResponse({ status: 404, description: '节点不存在' })
  async updateHeartbeat(
    @Param('nodeId') nodeId: string,
    @Body() updateDto: UpdateHeartbeatDto,
  ): Promise<ApiResponseDto> {
    try {
      const success = await this.edgeRegistryService.updateHeartbeat(nodeId, updateDto.metrics);
      
      if (!success) {
        throw new HttpException(
          ApiResponseDto.error('节点不存在', 'NODE_NOT_FOUND'),
          HttpStatus.NOT_FOUND,
        );
      }

      return ApiResponseDto.success('心跳更新成功');
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      
      this.logger.error(`更新心跳失败: ${error.message}`, error.stack);
      throw new HttpException(
        ApiResponseDto.error(`更新心跳失败: ${error.message}`, 'HEARTBEAT_UPDATE_FAILED'),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 选择最优节点
   */
  @Post('select-optimal')
  @ApiOperation({ summary: '选择最优节点', description: '根据负载均衡策略选择最优的边缘节点' })
  @ApiResponse({ 
    status: 200, 
    description: '选择成功', 
    type: ApiResponseDto<NodeResponseDto> 
  })
  @ApiResponse({ status: 404, description: '没有可用节点' })
  async selectOptimalNode(@Body() selectDto: SelectOptimalNodeDto): Promise<ApiResponseDto<NodeResponseDto>> {
    try {
      const node = await this.edgeRegistryService.getOptimalNode(
        selectDto.clientLocation,
        selectDto.strategy || LoadBalancingStrategy.HYBRID,
        selectDto.region,
      );

      if (!node) {
        throw new HttpException(
          ApiResponseDto.error('没有可用的边缘节点', 'NO_AVAILABLE_NODES'),
          HttpStatus.NOT_FOUND,
        );
      }

      const nodeResponse: NodeResponseDto = {
        nodeId: node.nodeId,
        region: node.region,
        endpoint: node.endpoint,
        status: node.status,
        lastHeartbeat: node.lastHeartbeat.toISOString(),
        registeredAt: node.registeredAt.toISOString(),
        metrics: node.metrics,
        capabilities: node.capabilities,
      };

      return ApiResponseDto.success('选择最优节点成功', nodeResponse);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      
      this.logger.error(`选择最优节点失败: ${error.message}`, error.stack);
      throw new HttpException(
        ApiResponseDto.error(`选择最优节点失败: ${error.message}`, 'NODE_SELECTION_FAILED'),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 获取所有节点
   */
  @Get('nodes')
  @ApiOperation({ summary: '获取所有节点', description: '获取注册中心中的所有边缘节点信息' })
  @ApiQuery({ name: 'region', required: false, description: '过滤指定区域的节点' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: ApiResponseDto<NodeResponseDto[]>
  })
  async getAllNodes(@Query('region') region?: string): Promise<ApiResponseDto<NodeResponseDto[]>> {
    try {
      const nodes = region
        ? this.edgeRegistryService.getNodesByRegion(region)
        : this.edgeRegistryService.getAllNodes();

      const nodeResponses: NodeResponseDto[] = nodes.map(node => ({
        nodeId: node.nodeId,
        region: node.region,
        endpoint: node.endpoint,
        status: node.status,
        lastHeartbeat: node.lastHeartbeat.toISOString(),
        registeredAt: node.registeredAt.toISOString(),
        metrics: node.metrics,
        capabilities: node.capabilities,
      }));

      return ApiResponseDto.success('获取节点列表成功', nodeResponses);
    } catch (error) {
      this.logger.error(`获取节点列表失败: ${error.message}`, error.stack);
      throw new HttpException(
        ApiResponseDto.error(`获取节点列表失败: ${error.message}`, 'GET_NODES_FAILED'),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 获取单个节点信息
   */
  @Get('nodes/:nodeId')
  @ApiOperation({ summary: '获取节点信息', description: '获取指定节点的详细信息' })
  @ApiParam({ name: 'nodeId', description: '节点ID', example: 'edge-node-001' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: ApiResponseDto<NodeResponseDto>
  })
  @ApiResponse({ status: 404, description: '节点不存在' })
  async getNode(@Param('nodeId') nodeId: string): Promise<ApiResponseDto<NodeResponseDto>> {
    try {
      const node = this.edgeRegistryService.getNode(nodeId);

      if (!node) {
        throw new HttpException(
          ApiResponseDto.error('节点不存在', 'NODE_NOT_FOUND'),
          HttpStatus.NOT_FOUND,
        );
      }

      const nodeResponse: NodeResponseDto = {
        nodeId: node.nodeId,
        region: node.region,
        endpoint: node.endpoint,
        status: node.status,
        lastHeartbeat: node.lastHeartbeat.toISOString(),
        registeredAt: node.registeredAt.toISOString(),
        metrics: node.metrics,
        capabilities: node.capabilities,
      };

      return ApiResponseDto.success('获取节点信息成功', nodeResponse);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error(`获取节点信息失败: ${error.message}`, error.stack);
      throw new HttpException(
        ApiResponseDto.error(`获取节点信息失败: ${error.message}`, 'GET_NODE_FAILED'),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 获取集群统计信息
   */
  @Get('stats')
  @ApiOperation({ summary: '获取集群统计', description: '获取边缘节点集群的统计信息' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: ApiResponseDto<ClusterStatsResponseDto>
  })
  async getClusterStats(): Promise<ApiResponseDto<ClusterStatsResponseDto>> {
    try {
      const stats = this.edgeRegistryService.getClusterStats();
      const availableRegions = this.edgeRegistryService.getAvailableRegions();

      const statsResponse: ClusterStatsResponseDto = {
        ...stats,
        availableRegions,
      };

      return ApiResponseDto.success('获取集群统计成功', statsResponse);
    } catch (error) {
      this.logger.error(`获取集群统计失败: ${error.message}`, error.stack);
      throw new HttpException(
        ApiResponseDto.error(`获取集群统计失败: ${error.message}`, 'GET_STATS_FAILED'),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 获取可用区域
   */
  @Get('regions')
  @ApiOperation({ summary: '获取可用区域', description: '获取所有可用的边缘节点区域' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: ApiResponseDto<string[]>
  })
  async getAvailableRegions(): Promise<ApiResponseDto<string[]>> {
    try {
      const regions = this.edgeRegistryService.getAvailableRegions();
      return ApiResponseDto.success('获取可用区域成功', regions);
    } catch (error) {
      this.logger.error(`获取可用区域失败: ${error.message}`, error.stack);
      throw new HttpException(
        ApiResponseDto.error(`获取可用区域失败: ${error.message}`, 'GET_REGIONS_FAILED'),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

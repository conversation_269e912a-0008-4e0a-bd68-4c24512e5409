import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';

// 服务
import { EdgeMonitoringService } from '../services/edge-monitoring.service';
import { EdgeMetricsService } from '../services/edge-metrics.service';
import { EdgeLoggingService } from '../services/edge-logging.service';

// 控制器
import { EdgeMonitoringController } from '../controllers/edge-monitoring.controller';

/**
 * 边缘监控模块
 * 提供性能监控、指标收集和日志管理功能
 */
@Module({
  imports: [
    ConfigModule,
    EventEmitterModule,
    ScheduleModule,
  ],
  
  controllers: [
    EdgeMonitoringController,
  ],
  
  providers: [
    EdgeMonitoringService,
    EdgeMetricsService,
    EdgeLoggingService,
  ],
  
  exports: [
    EdgeMonitoringService,
    EdgeMetricsService,
    EdgeLoggingService,
  ],
})
export class EdgeMonitoringModule {}

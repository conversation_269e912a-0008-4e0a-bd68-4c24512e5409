import { Controller, Get, Post, Put, Delete, Body, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { ApiPlatformService } from './api-platform.service';

@ApiTags('api-platform')
@Controller('api-platform')
export class ApiPlatformController {
  constructor(private readonly apiPlatformService: ApiPlatformService) {}

  @Get('apis')
  @ApiOperation({ summary: '获取API列表' })
  @ApiResponse({ status: 200, description: 'API列表' })
  @ApiQuery({ name: 'type', required: false, description: 'API类型' })
  @ApiQuery({ name: 'status', required: false, description: 'API状态' })
  async getAPIs(
    @Query('type') type?: string,
    @Query('status') status?: string,
  ) {
    return this.apiPlatformService.getAPIs({ type, status });
  }

  @Get('apis/:id')
  @ApiOperation({ summary: '获取API详情' })
  @ApiResponse({ status: 200, description: 'API详情' })
  @ApiParam({ name: 'id', description: 'API ID' })
  async getAPI(@Param('id') id: string) {
    return this.apiPlatformService.getAPI(id);
  }

  @Post('apis')
  @ApiOperation({ summary: '发布API规范' })
  @ApiResponse({ status: 201, description: 'API发布成功' })
  async publishAPI(@Body() apiSpec: any) {
    return this.apiPlatformService.publishAPI(apiSpec);
  }

  @Put('apis/:id')
  @ApiOperation({ summary: '更新API规范' })
  @ApiResponse({ status: 200, description: 'API更新成功' })
  @ApiParam({ name: 'id', description: 'API ID' })
  async updateAPI(@Param('id') id: string, @Body() updateData: any) {
    return this.apiPlatformService.updateAPI(id, updateData);
  }

  @Delete('apis/:id')
  @ApiOperation({ summary: '删除API规范' })
  @ApiResponse({ status: 200, description: 'API删除成功' })
  @ApiParam({ name: 'id', description: 'API ID' })
  async deleteAPI(@Param('id') id: string) {
    return this.apiPlatformService.deleteAPI(id);
  }

  @Get('apis/:id/usage')
  @ApiOperation({ summary: '获取API使用统计' })
  @ApiResponse({ status: 200, description: 'API使用统计' })
  @ApiParam({ name: 'id', description: 'API ID' })
  async getAPIUsage(@Param('id') id: string) {
    return this.apiPlatformService.getAPIUsage(id);
  }

  @Get('apis/:id/documentation')
  @ApiOperation({ summary: '获取API文档' })
  @ApiResponse({ status: 200, description: 'API文档' })
  @ApiParam({ name: 'id', description: 'API ID' })
  async getAPIDocumentation(@Param('id') id: string) {
    return this.apiPlatformService.getAPIDocumentation(id);
  }
}

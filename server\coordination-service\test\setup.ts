/**
 * Jest 测试设置文件
 * 全局测试配置和初始化
 */

import { Test } from '@nestjs/testing';

// 设置测试超时时间
jest.setTimeout(30000);

// 全局测试前设置
beforeAll(async () => {
  // 设置测试环境变量
  process.env.NODE_ENV = 'test';
  process.env.REDIS_HOST = 'localhost';
  process.env.REDIS_PORT = '6379';
  process.env.REDIS_DB = '1'; // 使用测试数据库
  process.env.LOG_LEVEL = 'error'; // 减少测试日志输出
});

// 全局测试后清理
afterAll(async () => {
  // 清理测试数据
  // 这里可以添加清理Redis测试数据的逻辑
});

// 每个测试前设置
beforeEach(() => {
  // 清除所有模拟
  jest.clearAllMocks();
});

// 每个测试后清理
afterEach(() => {
  // 恢复所有模拟
  jest.restoreAllMocks();
});

// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
});

// 模拟Redis连接（用于测试）
jest.mock('ioredis', () => {
  return jest.fn().mockImplementation(() => ({
    get: jest.fn(),
    set: jest.fn(),
    setex: jest.fn(),
    del: jest.fn(),
    publish: jest.fn(),
    subscribe: jest.fn(),
    unsubscribe: jest.fn(),
    on: jest.fn(),
    disconnect: jest.fn(),
    scard: jest.fn().mockResolvedValue(0),
  }));
});

// 导出测试工具函数
export const createTestingModule = async (providers: any[] = []) => {
  const module = await Test.createTestingModule({
    providers,
  }).compile();

  return module;
};

export const mockRedisClient = {
  get: jest.fn(),
  set: jest.fn(),
  setex: jest.fn(),
  del: jest.fn(),
  publish: jest.fn(),
  subscribe: jest.fn(),
  unsubscribe: jest.fn(),
  on: jest.fn(),
  disconnect: jest.fn(),
  scard: jest.fn().mockResolvedValue(0),
};

export const mockEventEmitter = {
  emit: jest.fn(),
  on: jest.fn(),
  off: jest.fn(),
  removeAllListeners: jest.fn(),
};

import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import {
  HealthCheckService,
  HealthCheck,
  MemoryHealthIndicator,
  DiskHealthIndicator,
} from '@nestjs/terminus';

import { EdgeRegistryClientService } from '../services/edge-registry-client.service';
import { RoutingCacheService } from '../services/routing-cache.service';
import { ApiResponseDto } from '../dto/routing-response.dto';

/**
 * 健康检查控制器
 */
@ApiTags('health')
@Controller('health')
export class HealthController {
  constructor(
    private readonly health: HealthCheckService,
    private readonly memory: MemoryHealthIndicator,
    private readonly disk: DiskHealthIndicator,
    private readonly edgeRegistryClient: EdgeRegistryClientService,
    private readonly cacheService: RoutingCacheService,
  ) {}

  /**
   * 基础健康检查
   */
  @Get()
  @ApiOperation({ summary: '基础健康检查', description: '检查服务基本健康状态' })
  @ApiResponse({ status: 200, description: '服务健康' })
  @ApiResponse({ status: 503, description: '服务不健康' })
  @HealthCheck()
  check() {
    return this.health.check([
      () => this.memory.checkHeap('memory_heap', 150 * 1024 * 1024),
      () => this.memory.checkRSS('memory_rss', 150 * 1024 * 1024),
    ]);
  }

  /**
   * 详细健康检查
   */
  @Get('detailed')
  @ApiOperation({ summary: '详细健康检查', description: '检查服务详细健康状态，包括内存、磁盘、外部依赖等' })
  @ApiResponse({ status: 200, description: '服务健康' })
  @ApiResponse({ status: 503, description: '服务不健康' })
  @HealthCheck()
  detailedCheck() {
    return this.health.check([
      () => this.memory.checkHeap('memory_heap', 200 * 1024 * 1024),
      () => this.memory.checkRSS('memory_rss', 200 * 1024 * 1024),
      () => this.disk.checkStorage('storage', { 
        path: '/', 
        thresholdPercent: 0.9 
      }),
      // 检查边缘注册中心连接
      async () => {
        const isHealthy = await this.edgeRegistryClient.checkHealth();
        return {
          'edge-registry': {
            status: isHealthy ? 'up' : 'down',
          },
        };
      },
      // 检查缓存服务
      () => {
        const cacheStats = this.cacheService.getCacheStats();
        return {
          'cache': {
            status: 'up',
            memorySize: cacheStats.memoryCacheSize,
            redisConnected: cacheStats.redisConnected,
          },
        };
      },
    ]);
  }

  /**
   * 就绪检查
   */
  @Get('ready')
  @ApiOperation({ summary: '就绪检查', description: '检查服务是否准备好接收请求' })
  @ApiResponse({ status: 200, description: '服务就绪' })
  @ApiResponse({ status: 503, description: '服务未就绪' })
  async readinessCheck(): Promise<ApiResponseDto> {
    try {
      // 检查边缘注册中心连接
      const registryHealthy = await this.edgeRegistryClient.checkHealth();
      
      if (!registryHealthy) {
        return ApiResponseDto.error('边缘注册中心连接失败', 'EDGE_REGISTRY_UNAVAILABLE');
      }
      
      return ApiResponseDto.success('服务就绪');
    } catch (error) {
      return ApiResponseDto.error('服务未就绪', 'SERVICE_NOT_READY');
    }
  }

  /**
   * 存活检查
   */
  @Get('live')
  @ApiOperation({ summary: '存活检查', description: '检查服务是否存活' })
  @ApiResponse({ status: 200, description: '服务存活' })
  async livenessCheck(): Promise<ApiResponseDto> {
    return ApiResponseDto.success('服务存活');
  }

  /**
   * 依赖检查
   */
  @Get('dependencies')
  @ApiOperation({ summary: '依赖检查', description: '检查外部依赖服务状态' })
  @ApiResponse({ status: 200, description: '依赖检查完成' })
  async dependenciesCheck(): Promise<ApiResponseDto> {
    try {
      const dependencies = {
        edgeRegistry: await this.edgeRegistryClient.checkHealth(),
        cache: this.cacheService.getCacheStats(),
      };

      const allHealthy = dependencies.edgeRegistry;
      
      return ApiResponseDto.success(
        allHealthy ? '所有依赖正常' : '部分依赖异常',
        dependencies
      );
    } catch (error) {
      return ApiResponseDto.error('依赖检查失败', 'DEPENDENCIES_CHECK_FAILED');
    }
  }

  /**
   * 性能指标
   */
  @Get('metrics')
  @ApiOperation({ summary: '性能指标', description: '获取服务性能指标' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getMetrics(): Promise<ApiResponseDto> {
    try {
      const memoryUsage = process.memoryUsage();
      const cacheStats = this.cacheService.getCacheStats();
      
      const metrics = {
        memory: {
          rss: Math.round(memoryUsage.rss / 1024 / 1024), // MB
          heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
          heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
          external: Math.round(memoryUsage.external / 1024 / 1024), // MB
        },
        uptime: Math.round(process.uptime()), // 秒
        cache: cacheStats,
        timestamp: new Date().toISOString(),
      };

      return ApiResponseDto.success('获取性能指标成功', metrics);
    } catch (error) {
      return ApiResponseDto.error('获取性能指标失败', 'METRICS_FAILED');
    }
  }
}

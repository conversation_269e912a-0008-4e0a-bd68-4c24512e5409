import { Controller, Get, Post, Put, Delete, Body, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { ApplicationsService } from './applications.service';

@ApiTags('applications')
@Controller('applications')
export class ApplicationsController {
  constructor(private readonly applicationsService: ApplicationsService) {}

  @Get()
  @ApiOperation({ summary: '获取第三方应用列表' })
  @ApiResponse({ status: 200, description: '应用列表' })
  @ApiQuery({ name: 'type', required: false, description: '应用类型' })
  @ApiQuery({ name: 'category', required: false, description: '应用分类' })
  @ApiQuery({ name: 'status', required: false, description: '应用状态' })
  async getApplications(
    @Query('type') type?: string,
    @Query('category') category?: string,
    @Query('status') status?: string,
  ) {
    return this.applicationsService.getApplications({ type, category, status });
  }

  @Get(':id')
  @ApiOperation({ summary: '获取应用详情' })
  @ApiResponse({ status: 200, description: '应用详情' })
  @ApiParam({ name: 'id', description: '应用ID' })
  async getApplication(@Param('id') id: string) {
    return this.applicationsService.getApplication(id);
  }

  @Post()
  @ApiOperation({ summary: '提交第三方应用' })
  @ApiResponse({ status: 201, description: '应用提交成功' })
  async submitApplication(@Body() application: any) {
    return this.applicationsService.submitApplication(application);
  }

  @Put(':id')
  @ApiOperation({ summary: '更新应用信息' })
  @ApiResponse({ status: 200, description: '应用更新成功' })
  @ApiParam({ name: 'id', description: '应用ID' })
  async updateApplication(@Param('id') id: string, @Body() updateData: any) {
    return this.applicationsService.updateApplication(id, updateData);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除应用' })
  @ApiResponse({ status: 200, description: '应用删除成功' })
  @ApiParam({ name: 'id', description: '应用ID' })
  async deleteApplication(@Param('id') id: string) {
    return this.applicationsService.deleteApplication(id);
  }

  @Get(':id/metrics')
  @ApiOperation({ summary: '获取应用指标' })
  @ApiResponse({ status: 200, description: '应用指标' })
  @ApiParam({ name: 'id', description: '应用ID' })
  async getApplicationMetrics(@Param('id') id: string) {
    return this.applicationsService.getApplicationMetrics(id);
  }

  @Get(':id/reviews')
  @ApiOperation({ summary: '获取应用评价' })
  @ApiResponse({ status: 200, description: '应用评价列表' })
  @ApiParam({ name: 'id', description: '应用ID' })
  async getApplicationReviews(@Param('id') id: string) {
    return this.applicationsService.getApplicationReviews(id);
  }

  @Post(':id/reviews')
  @ApiOperation({ summary: '添加应用评价' })
  @ApiResponse({ status: 201, description: '评价添加成功' })
  @ApiParam({ name: 'id', description: '应用ID' })
  async addApplicationReview(@Param('id') id: string, @Body() review: any) {
    return this.applicationsService.addApplicationReview(id, review);
  }
}

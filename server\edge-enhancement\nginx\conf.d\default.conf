# 边缘计算增强服务 - 默认配置

# 上游服务器组
upstream edge_enhancement_api {
    # 负载均衡策略：least_conn（最少连接）
    least_conn;
    
    # 主服务实例
    server edge-enhancement:3040 weight=1 max_fails=3 fail_timeout=30s;
    
    # 备用实例（如果有的话）
    # server edge-enhancement-replica:3040 weight=1 max_fails=3 fail_timeout=30s backup;
    
    # 保持连接
    keepalive 32;
    keepalive_requests 100;
    keepalive_timeout 60s;
}

# 主服务器配置
server {
    listen 80;
    server_name _;
    
    # 客户端最大请求体大小
    client_max_body_size 10M;
    
    # 安全头设置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header X-Robots-Tag "noindex, nofollow" always;
    
    # API路由
    location /api/v1/edge-enhancement/ {
        # 限流：每秒最多10个请求，突发20个
        limit_req zone=api_limit burst=20 nodelay;
        
        # 代理设置
        proxy_pass http://edge_enhancement_api/api/v1/edge-enhancement/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header X-Forwarded-Host $host;
        proxy_set_header X-Forwarded-Port $server_port;
        
        # 超时设置
        proxy_connect_timeout 10s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
        
        # 缓冲设置
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
        proxy_busy_buffers_size 8k;
        
        # 错误处理
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        proxy_next_upstream_tries 3;
        proxy_next_upstream_timeout 10s;
    }
    
    # API文档路由
    location /api/docs {
        # 限流：每秒最多2个请求
        limit_req zone=docs_limit burst=5 nodelay;
        
        proxy_pass http://edge_enhancement_api/api/docs;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 缓存API文档静态资源
        location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1h;
            add_header Cache-Control "public";
        }
    }
    
    # 健康检查（不记录日志）
    location /health {
        access_log off;
        proxy_pass http://edge_enhancement_api/api/v1/edge-enhancement/health;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        
        # 快速超时
        proxy_connect_timeout 3s;
        proxy_send_timeout 3s;
        proxy_read_timeout 3s;
    }
    
    # 服务状态
    location /status {
        proxy_pass http://edge_enhancement_api/api/v1/edge-enhancement/status;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 根路径重定向到API文档
    location = / {
        return 302 /api/docs;
    }
    
    # 默认路由
    location / {
        proxy_pass http://edge_enhancement_api;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # 错误页面
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    location = /404.html {
        internal;
        return 404 '{"error": "Not Found", "message": "请求的资源不存在"}';
        add_header Content-Type application/json;
    }
    
    location = /50x.html {
        internal;
        return 500 '{"error": "Internal Server Error", "message": "服务器内部错误"}';
        add_header Content-Type application/json;
    }
}

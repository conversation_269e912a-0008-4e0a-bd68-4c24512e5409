import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { CacheService } from '../redis/cache.service';
import * as crypto from 'crypto';

export interface User {
  id: string;
  username: string;
  email: string;
  roles: string[];
  permissions: string[];
  partnerId?: string;
  organizationId?: string;
}

export interface JwtPayload {
  sub: string;
  username: string;
  email: string;
  roles: string[];
  permissions: string[];
  partnerId?: string;
  organizationId?: string;
  iat?: number;
  exp?: number;
}

export interface ApiKeyInfo {
  keyId: string;
  name: string;
  userId: string;
  roles: string[];
  permissions: string[];
  rateLimit?: {
    requests: number;
    window: number; // 秒
  };
  isActive: boolean;
  expiresAt?: Date;
}

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly cacheService: CacheService,
  ) {}

  /**
   * 生成JWT令牌
   */
  async generateToken(user: User): Promise<string> {
    const payload: JwtPayload = {
      sub: user.id,
      username: user.username,
      email: user.email,
      roles: user.roles,
      permissions: user.permissions,
      partnerId: user.partnerId,
      organizationId: user.organizationId,
    };

    return this.jwtService.sign(payload);
  }

  /**
   * 验证JWT令牌
   */
  async validateToken(token: string): Promise<User | null> {
    try {
      const payload = this.jwtService.verify(token) as JwtPayload;
      
      // 检查令牌是否在黑名单中
      const isBlacklisted = await this.isTokenBlacklisted(token);
      if (isBlacklisted) {
        throw new UnauthorizedException('Token has been revoked');
      }

      return {
        id: payload.sub,
        username: payload.username,
        email: payload.email,
        roles: payload.roles,
        permissions: payload.permissions,
        partnerId: payload.partnerId,
        organizationId: payload.organizationId,
      };
    } catch (error) {
      this.logger.warn(`Token validation failed: ${error.message}`);
      return null;
    }
  }

  /**
   * 生成API密钥
   */
  async generateApiKey(userId: string, name: string, options: {
    roles?: string[];
    permissions?: string[];
    rateLimit?: { requests: number; window: number };
    expiresIn?: number; // 天数
  } = {}): Promise<{ keyId: string; apiKey: string }> {
    const keyId = crypto.randomUUID();
    const apiKey = this.generateSecureKey();

    const apiKeyInfo: ApiKeyInfo = {
      keyId,
      name,
      userId,
      roles: options.roles || [],
      permissions: options.permissions || [],
      rateLimit: options.rateLimit,
      isActive: true,
      expiresAt: options.expiresIn ? new Date(Date.now() + options.expiresIn * 24 * 60 * 60 * 1000) : undefined,
    };

    // 存储API密钥信息（使用哈希后的密钥作为键）
    const hashedKey = this.hashApiKey(apiKey);
    const cacheKey = this.cacheService.generateKey('apikey', hashedKey);
    await this.cacheService.set(cacheKey, apiKeyInfo, options.expiresIn ? options.expiresIn * 24 * 60 * 60 : undefined);

    // 存储用户的API密钥列表
    const userKeysKey = this.cacheService.generateKey('user', 'apikeys', userId);
    const userKeys = await this.cacheService.get<string[]>(userKeysKey) || [];
    userKeys.push(keyId);
    await this.cacheService.set(userKeysKey, userKeys);

    return { keyId, apiKey };
  }

  /**
   * 验证API密钥
   */
  async validateApiKey(apiKey: string): Promise<ApiKeyInfo | null> {
    try {
      const hashedKey = this.hashApiKey(apiKey);
      const cacheKey = this.cacheService.generateKey('apikey', hashedKey);
      const apiKeyInfo = await this.cacheService.get<ApiKeyInfo>(cacheKey);

      if (!apiKeyInfo || !apiKeyInfo.isActive) {
        return null;
      }

      // 检查是否过期
      if (apiKeyInfo.expiresAt && new Date() > apiKeyInfo.expiresAt) {
        await this.revokeApiKey(apiKey);
        return null;
      }

      return apiKeyInfo;
    } catch (error) {
      this.logger.warn(`API key validation failed: ${error.message}`);
      return null;
    }
  }

  /**
   * 撤销API密钥
   */
  async revokeApiKey(apiKey: string): Promise<boolean> {
    try {
      const hashedKey = this.hashApiKey(apiKey);
      const cacheKey = this.cacheService.generateKey('apikey', hashedKey);
      return await this.cacheService.del(cacheKey);
    } catch (error) {
      this.logger.error(`Failed to revoke API key: ${error.message}`);
      return false;
    }
  }

  /**
   * 撤销JWT令牌（加入黑名单）
   */
  async revokeToken(token: string): Promise<boolean> {
    try {
      const payload = this.jwtService.decode(token) as JwtPayload;
      if (!payload || !payload.exp) {
        return false;
      }

      const blacklistKey = this.cacheService.generateKey('blacklist', 'token', token);
      const ttl = payload.exp - Math.floor(Date.now() / 1000);
      
      if (ttl > 0) {
        await this.cacheService.set(blacklistKey, true, ttl);
      }

      return true;
    } catch (error) {
      this.logger.error(`Failed to revoke token: ${error.message}`);
      return false;
    }
  }

  /**
   * 检查令牌是否在黑名单中
   */
  async isTokenBlacklisted(token: string): Promise<boolean> {
    try {
      const blacklistKey = this.cacheService.generateKey('blacklist', 'token', token);
      return await this.cacheService.exists(blacklistKey);
    } catch (error) {
      this.logger.error(`Failed to check token blacklist: ${error.message}`);
      return false;
    }
  }

  /**
   * 检查用户是否有指定权限
   */
  hasPermission(user: User, permission: string): boolean {
    return user.permissions.includes(permission) || user.permissions.includes('*');
  }

  /**
   * 检查用户是否有指定角色
   */
  hasRole(user: User, role: string): boolean {
    return user.roles.includes(role) || user.roles.includes('admin');
  }

  /**
   * 检查用户是否有任一指定角色
   */
  hasAnyRole(user: User, roles: string[]): boolean {
    return roles.some(role => this.hasRole(user, role));
  }

  /**
   * 生成安全的API密钥
   */
  private generateSecureKey(): string {
    return crypto.randomBytes(32).toString('hex');
  }

  /**
   * 哈希API密钥
   */
  private hashApiKey(apiKey: string): string {
    return crypto.createHash('sha256').update(apiKey).digest('hex');
  }
}

import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsNumber, IsObject, ValidateNested, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * 节点指标信息DTO
 */
export class NodeMetricsDto {
  @ApiPropertyOptional({ description: '当前用户数', example: 25 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  currentUsers?: number;

  @ApiPropertyOptional({ description: 'CPU使用率(%)', example: 45.5 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  cpuUsage?: number;

  @ApiPropertyOptional({ description: '内存使用率(%)', example: 60.2 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  memoryUsage?: number;

  @ApiPropertyOptional({ description: '网络延迟(ms)', example: 15 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  networkLatency?: number;

  @ApiPropertyOptional({ description: '运行时间(秒)', example: 86400 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  uptime?: number;
}

/**
 * 更新节点心跳DTO
 */
export class UpdateHeartbeatDto {
  @ApiPropertyOptional({ description: '节点指标信息', type: NodeMetricsDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => NodeMetricsDto)
  metrics?: NodeMetricsDto;

  @ApiPropertyOptional({ description: '额外元数据', example: { lastActivity: '2024-01-01T00:00:00Z' } })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

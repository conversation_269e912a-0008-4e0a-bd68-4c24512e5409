# 深度学习推理服务 (Deep Learning Inference Service)

## 概述

深度学习推理服务是一个基于NestJS的高性能微服务，专门用于AI模型的推理、管理和监控。该服务提供完整的模型生命周期管理，支持多种深度学习框架，并具备企业级的监控和告警功能。

## 主要功能

### 🧠 核心功能
- **模型推理** - 支持同步和异步推理，批量处理，文件上传推理
- **模型管理** - 模型加载、卸载、版本管理、配置管理
- **队列管理** - 智能任务调度，优先级队列，负载均衡
- **缓存优化** - 模型缓存，结果缓存，智能预加载
- **批处理** - 动态批处理，延迟优化，吞吐量最大化

### 📊 监控功能
- **实时监控** - 系统资源、推理性能、队列状态
- **性能分析** - 延迟分析、吞吐量统计、错误率监控
- **告警系统** - 智能阈值告警、多级告警、自动恢复
- **健康检查** - 服务健康状态、依赖检查、自动诊断
- **报告生成** - 性能报告、使用统计、趋势分析

### 🔧 管理功能
- **模型仓库** - 模型存储、版本控制、元数据管理
- **配置管理** - 动态配置、环境隔离、热更新
- **用户管理** - 多租户支持、权限控制、使用配额
- **API管理** - RESTful API、Swagger文档、版本控制

## 技术栈

- **框架**: NestJS 10.x
- **语言**: TypeScript 5.x
- **数据库**: Redis (缓存和消息队列)
- **消息**: EventEmitter2 (事件系统)
- **调度**: @nestjs/schedule (定时任务)
- **验证**: class-validator (数据验证)
- **文档**: Swagger/OpenAPI
- **测试**: Jest (单元测试)
- **监控**: 自定义监控系统

## 支持的模型格式

- **ONNX** - 跨平台神经网络交换格式
- **PyTorch** - PyTorch模型格式
- **TensorFlow** - TensorFlow SavedModel格式
- **自定义格式** - 可扩展支持其他格式

## 快速开始

### 环境要求
- Node.js >= 18.0.0
- Redis >= 6.0.0
- npm >= 8.0.0
- (可选) GPU支持

### 安装依赖
```bash
npm install
```

### 环境配置
复制环境配置文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置必要的参数：
```env
# 基础配置
NODE_ENV=development
PORT=3020
HOST=0.0.0.0

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379

# 模型存储路径
MODEL_STORAGE_PATH=./models
```

### 启动服务
```bash
# 开发模式
npm run start:dev

# 生产模式
npm run build
npm run start:prod

# 调试模式
npm run start:debug
```

### 验证安装
访问以下地址验证服务是否正常运行：
- 健康检查: http://localhost:3020/health
- API文档: http://localhost:3020/api/docs
- 实时指标: http://localhost:3020/api/v1/monitoring/realtime

## API 接口

### 推理接口

#### 提交推理请求
```http
POST /api/v1/inference/submit
Content-Type: application/json

{
  "modelId": "text-classifier-v1",
  "input": {
    "text": "这是一个测试文本"
  },
  "userId": "user123",
  "priority": 5
}
```

#### 获取推理结果
```http
GET /api/v1/inference/result/{requestId}
```

#### 批量推理
```http
POST /api/v1/inference/batch
Content-Type: application/json

{
  "modelId": "text-classifier-v1",
  "inputs": [
    {"text": "文本1"},
    {"text": "文本2"}
  ],
  "userId": "user123"
}
```

#### 文件上传推理
```http
POST /api/v1/inference/upload/{modelId}
Content-Type: multipart/form-data

file: [文件数据]
userId: user123
```

### 模型管理接口

#### 获取模型列表
```http
GET /api/v1/models
```

#### 加载模型
```http
POST /api/v1/models/load
Content-Type: application/json

{
  "id": "text-classifier-v1",
  "name": "文本分类器",
  "type": "classification",
  "version": "1.0.0",
  "path": "/models/text_classifier.onnx"
}
```

#### 卸载模型
```http
DELETE /api/v1/models/{modelId}
```

#### 获取模型指标
```http
GET /api/v1/models/{modelId}/metrics
```

### 监控接口

#### 获取系统指标
```http
GET /api/v1/monitoring/metrics
```

#### 获取实时指标
```http
GET /api/v1/monitoring/realtime
```

#### 获取性能报告
```http
GET /api/v1/monitoring/report?startTime=1640995200000&endTime=1640998800000
```

## 配置说明

### 推理配置
```env
# 最大并发推理数
MAX_CONCURRENT_INFERENCES=10

# 最大队列大小
MAX_QUEUE_SIZE=1000

# 推理超时时间 (毫秒)
INFERENCE_TIMEOUT=30000
```

### 模型配置
```env
# 模型存储路径
MODEL_STORAGE_PATH=./models

# 最大模型文件大小 (1GB)
MAX_MODEL_SIZE=**********

# 支持的模型格式
SUPPORTED_MODEL_FORMATS=onnx,pytorch,tensorflow
```

### 监控配置
```env
# 启用监控
ENABLE_METRICS=true

# 指标收集间隔 (毫秒)
METRICS_INTERVAL=60000

# 告警阈值
ALERT_ERROR_RATE_THRESHOLD=0.1
ALERT_LATENCY_THRESHOLD=5000
```

## 开发指南

### 项目结构
```
src/
├── controllers/          # 控制器
│   ├── inference.controller.ts
│   ├── model.controller.ts
│   ├── monitoring.controller.ts
│   └── health.controller.ts
├── services/            # 服务层
│   ├── model-inference.service.ts
│   ├── model-management.service.ts
│   └── monitoring.service.ts
├── dto/                 # 数据传输对象
│   ├── inference.dto.ts
│   ├── model.dto.ts
│   └── monitoring.dto.ts
├── providers/           # 提供者
│   ├── redis.provider.ts
│   └── config.provider.ts
├── deeplearning-service.module.ts  # 主模块
└── main.ts             # 应用入口
```

### 添加新的模型类型
1. 在 `ModelInferenceService` 中添加模型加载逻辑
2. 实现对应的推理方法
3. 更新模型配置和验证规则
4. 添加相应的测试用例

### 自定义监控指标
1. 在 `MonitoringService` 中定义新指标
2. 实现指标收集逻辑
3. 添加告警规则
4. 更新监控接口

### 运行测试
```bash
# 单元测试
npm run test

# 测试覆盖率
npm run test:cov

# 端到端测试
npm run test:e2e

# 监听模式
npm run test:watch
```

### 代码规范
```bash
# 代码格式化
npm run format

# 代码检查
npm run lint

# 自动修复
npm run lint:fix
```

## 部署

### Docker部署
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
EXPOSE 3020
CMD ["node", "dist/main"]
```

### 构建和运行
```bash
# 构建镜像
npm run docker:build

# 运行容器
npm run docker:run

# 使用Docker Compose
npm run docker:up
```

### 生产环境配置
```env
NODE_ENV=production
PORT=3020
REDIS_HOST=redis-cluster
ENABLE_METRICS=true
LOG_LEVEL=warn
```

## 性能优化

### 推理优化
- 使用模型缓存减少加载时间
- 实现批处理提高吞吐量
- 配置合适的并发数和队列大小
- 启用GPU加速（如果可用）

### 内存优化
- 定期清理过期缓存
- 限制模型缓存大小
- 监控内存使用情况
- 实现内存泄漏检测

### 网络优化
- 使用连接池
- 启用压缩
- 配置合适的超时时间
- 实现请求重试机制

## 监控和告警

### 关键指标
- **延迟**: P50, P95, P99响应时间
- **吞吐量**: 每秒处理请求数
- **错误率**: 失败请求比例
- **资源使用**: CPU、内存、GPU使用率
- **队列状态**: 队列长度、等待时间

### 告警规则
- 错误率超过10%
- 平均延迟超过5秒
- 队列长度超过500
- 内存使用率超过80%
- 服务不可用

## 故障排除

### 常见问题
1. **模型加载失败** - 检查模型文件路径和格式
2. **推理超时** - 调整超时配置或检查模型性能
3. **内存不足** - 减少并发数或增加内存
4. **Redis连接失败** - 检查Redis服务状态和配置

### 调试技巧
- 启用DEBUG模式查看详细日志
- 使用健康检查接口诊断问题
- 检查监控指标识别瓶颈
- 分析错误日志定位问题

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请联系开发团队。

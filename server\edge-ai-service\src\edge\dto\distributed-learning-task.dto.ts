import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsEnum, IsArray, IsNumber, IsOptional, IsObject } from 'class-validator';

export class DistributedLearningTaskDto {
  @ApiPropertyOptional({ description: '任务名称' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ 
    description: '学习算法',
    enum: ['federated_learning', 'distributed_training', 'ensemble_learning'],
    example: 'federated_learning'
  })
  @IsEnum(['federated_learning', 'distributed_training', 'ensemble_learning'])
  algorithm: 'federated_learning' | 'distributed_training' | 'ensemble_learning';

  @ApiProperty({ description: '参与者设备ID列表', type: [String] })
  @IsArray()
  participants: string[];

  @ApiProperty({ description: '模型模板' })
  @IsObject()
  modelTemplate: any;

  @ApiPropertyOptional({ 
    description: '聚合策略',
    example: 'federated_averaging'
  })
  @IsOptional()
  @IsString()
  aggregationStrategy?: string;

  @ApiPropertyOptional({ description: '训练轮数', example: 10 })
  @IsOptional()
  @IsNumber()
  rounds?: number;

  @ApiPropertyOptional({ description: '学习率', example: 0.01 })
  @IsOptional()
  @IsNumber()
  learningRate?: number;

  @ApiPropertyOptional({ description: '批处理大小', example: 32 })
  @IsOptional()
  @IsNumber()
  batchSize?: number;

  @ApiPropertyOptional({ description: '本地训练轮数', example: 5 })
  @IsOptional()
  @IsNumber()
  localEpochs?: number;

  @ApiPropertyOptional({ description: '最小参与者数量', example: 2 })
  @IsOptional()
  @IsNumber()
  minParticipants?: number;

  @ApiPropertyOptional({ description: '收敛阈值', example: 0.001 })
  @IsOptional()
  @IsNumber()
  convergenceThreshold?: number;

  @ApiPropertyOptional({ description: '隐私保护配置' })
  @IsOptional()
  @IsObject()
  privacyConfig?: {
    differentialPrivacy?: boolean;
    noiseLevel?: number;
    secureAggregation?: boolean;
  };

  @ApiPropertyOptional({ description: '数据分布配置' })
  @IsOptional()
  @IsObject()
  dataDistribution?: {
    iid?: boolean;
    skewness?: number;
    sampleRatio?: number;
  };
}

#!/bin/bash

# 边缘AI服务开发环境启动脚本

echo "🚀 启动边缘AI计算服务开发环境..."

# 检查Node.js版本
NODE_VERSION=$(node -v)
echo "📦 Node.js版本: $NODE_VERSION"

# 检查环境变量文件
if [ ! -f .env ]; then
    echo "⚠️  未找到.env文件，复制示例配置..."
    cp .env.example .env
    echo "✅ 已创建.env文件，请根据需要修改配置"
fi

# 安装依赖
echo "📦 安装依赖包..."
npm install

# 检查数据库连接
echo "🔍 检查数据库连接..."
# 这里可以添加数据库连接检查逻辑

# 检查Redis连接
echo "🔍 检查Redis连接..."
# 这里可以添加Redis连接检查逻辑

# 启动开发服务器
echo "🚀 启动开发服务器..."
npm run start:dev

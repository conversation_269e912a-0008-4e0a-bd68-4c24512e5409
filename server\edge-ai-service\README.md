# 边缘AI计算服务 (Edge AI Service)

边缘设备AI推理、分布式学习、实时决策优化微服务

## 功能特性

### 🔧 核心功能
- **边缘设备管理**: 设备注册、状态监控、性能跟踪
- **AI模型部署**: 模型优化、格式转换、设备适配
- **边缘推理**: 分布式推理、负载均衡、实时处理
- **分布式学习**: 联邦学习、分布式训练、模型聚合
- **实时优化**: 资源调度、性能优化、决策支持

### 🚀 技术特性
- **高性能**: 基于NestJS框架，支持高并发处理
- **可扩展**: 微服务架构，支持水平扩展
- **实时通信**: WebSocket支持，实时状态更新
- **数据持久化**: MySQL数据库，Redis缓存
- **API文档**: Swagger自动生成API文档

## 快速开始

### 环境要求
- Node.js >= 16.0.0
- MySQL >= 8.0
- Redis >= 6.0

### 安装依赖
```bash
npm install
```

### 环境配置
```bash
# 复制环境变量配置文件
cp .env.example .env

# 编辑配置文件
nano .env
```

### 数据库初始化
```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE edge_ai_service CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
```

### 启动服务
```bash
# 开发模式
npm run start:dev

# 生产模式
npm run build
npm run start
```

## API文档

服务启动后访问: http://localhost:3006/api/docs

### 主要API端点

#### 边缘设备管理
- `POST /api/v1/edge/devices/register` - 注册边缘设备
- `GET /api/v1/devices` - 获取设备列表
- `GET /api/v1/devices/:id` - 获取设备详情
- `PUT /api/v1/devices/:id/heartbeat` - 更新设备心跳

#### AI模型管理
- `POST /api/v1/edge/models/deploy` - 部署模型到设备
- `GET /api/v1/models` - 获取模型列表

#### 推理服务
- `POST /api/v1/edge/inference` - 执行边缘推理
- `GET /api/v1/inference/statistics` - 获取推理统计

#### 分布式学习
- `POST /api/v1/edge/learning/start` - 启动学习任务
- `GET /api/v1/learning` - 获取学习任务列表

#### 实时优化
- `POST /api/v1/edge/optimization` - 执行决策优化

#### 监控服务
- `GET /api/v1/monitoring/metrics` - 获取监控指标
- `GET /api/v1/edge/statistics` - 获取系统统计

## WebSocket事件

连接地址: `ws://localhost:3006/edge-ai`

### 客户端事件
- `device:register` - 设备注册
- `device:heartbeat` - 设备心跳
- `inference:request` - 推理请求
- `learning:subscribe` - 订阅学习任务
- `monitoring:subscribe` - 订阅监控更新

### 服务端事件
- `device:registered` - 设备注册成功
- `device:status` - 设备状态更新
- `inference:result` - 推理结果
- `learning:progress` - 学习进度
- `system:alert` - 系统告警

## 项目结构

```
src/
├── edge/                 # 边缘AI核心模块
│   ├── edge-ai.service.ts
│   ├── edge.controller.ts
│   ├── edge.gateway.ts
│   └── dto/
├── device/              # 设备管理模块
│   ├── entities/
│   ├── device.service.ts
│   └── device.controller.ts
├── model/               # 模型管理模块
├── inference/           # 推理服务模块
├── learning/            # 分布式学习模块
├── optimization/        # 优化服务模块
├── monitoring/          # 监控服务模块
├── websocket/           # WebSocket模块
├── app.module.ts        # 应用主模块
└── main.ts             # 应用入口
```

## 开发指南

### 添加新的边缘设备类型
1. 在 `EdgeDeviceType` 枚举中添加新类型
2. 更新设备兼容性检查逻辑
3. 添加对应的模型格式支持

### 添加新的AI模型格式
1. 在 `ModelFormat` 枚举中添加新格式
2. 实现格式转换逻辑
3. 更新设备兼容性映射

### 添加新的学习算法
1. 在 `LearningAlgorithm` 枚举中添加算法
2. 实现算法执行逻辑
3. 添加相应的配置参数

## 部署说明

### Docker部署
```bash
# 构建镜像
docker build -t edge-ai-service .

# 运行容器
docker run -d \
  --name edge-ai-service \
  -p 3006:3006 \
  -e DB_HOST=mysql \
  -e REDIS_HOST=redis \
  edge-ai-service
```

### 集群部署
支持Kubernetes部署，配置文件位于 `k8s/` 目录

## 监控和日志

### 性能监控
- 设备状态监控
- 推理性能指标
- 资源使用情况
- 系统健康检查

### 日志管理
- 结构化日志输出
- 多级别日志记录
- 错误追踪和告警

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

- 项目维护者: Edge AI Team
- 邮箱: <EMAIL>
- 文档: https://docs.edge-ai.example.com

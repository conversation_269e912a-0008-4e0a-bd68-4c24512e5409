{"name": "ecosystem-service", "version": "1.0.0", "description": "生态系统建设服务 - 合作伙伴生态构建、开放API平台、第三方应用集成、行业标准制定", "main": "dist/main.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/common": "^9.4.3", "@nestjs/config": "^2.3.4", "@nestjs/core": "^9.4.3", "@nestjs/jwt": "^9.0.0", "@nestjs/microservices": "^9.4.3", "@nestjs/passport": "^9.0.3", "@nestjs/platform-express": "^9.4.3", "@nestjs/platform-socket.io": "^9.4.3", "@nestjs/schedule": "^2.2.3", "@nestjs/swagger": "^6.3.0", "@nestjs/typeorm": "^9.0.1", "@nestjs/websockets": "^9.4.3", "@types/passport-jwt": "^4.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "express-rate-limit": "^6.10.0", "lodash": "^4.17.21", "moment": "^2.29.4", "mysql2": "^3.6.0", "passport": "^0.6.0", "passport-custom": "^1.1.1", "passport-jwt": "^4.0.1", "redis": "^4.6.7", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "socket.io": "^4.7.2", "swagger-ui-express": "^5.0.0", "typeorm": "^0.3.17", "uuid": "^9.0.0"}, "devDependencies": {"@nestjs/cli": "^9.5.0", "@nestjs/schematics": "^9.2.0", "@nestjs/testing": "^9.4.3", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/lodash": "^4.14.195", "@types/node": "^20.3.1", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.5.0", "prettier": "^2.8.8", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s", "!**/*.d.ts", "!**/node_modules/**", "!**/test-utils/**", "!**/*.interface.ts"], "coverageDirectory": "../coverage", "coverageReporters": ["text", "lcov", "html"], "testEnvironment": "node", "moduleNameMapping": {"^@/(.*)$": "<rootDir>/$1"}, "setupFilesAfterEnv": ["<rootDir>/test-utils/setup.ts"], "testTimeout": 30000}}
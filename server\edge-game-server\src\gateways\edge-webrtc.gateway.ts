import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  MessageBody,
  ConnectedSocket,
} from '@nestjs/websockets';
import { Logger } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { EdgeSignalingService } from '../services/edge-signaling.service';

/**
 * 边缘WebRTC网关
 * 处理WebRTC信令和实时通信
 */
@WebSocketGateway({
  cors: {
    origin: '*',
  },
  namespace: '/webrtc',
})
export class EdgeWebRTCGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(EdgeWebRTCGateway.name);
  private readonly connectedClients = new Map<string, Socket>();

  constructor(private readonly signalingService: EdgeSignalingService) {}

  /**
   * 客户端连接
   */
  handleConnection(client: Socket): void {
    const userId = client.handshake.query.userId as string;
    if (userId) {
      this.connectedClients.set(userId, client);
      this.logger.log(`WebRTC客户端连接: ${userId}`);
    }
  }

  /**
   * 客户端断开连接
   */
  handleDisconnect(client: Socket): void {
    const userId = client.handshake.query.userId as string;
    if (userId) {
      this.connectedClients.delete(userId);
      this.logger.log(`WebRTC客户端断开: ${userId}`);
    }
  }

  /**
   * 处理信令消息
   */
  @SubscribeMessage('signaling')
  async handleSignaling(
    @MessageBody() data: { type: string; payload: any; targetUserId?: string },
    @ConnectedSocket() client: Socket,
  ): Promise<void> {
    const userId = client.handshake.query.userId as string;
    
    try {
      await this.signalingService.handleSignaling(userId, data);
      
      // 如果有目标用户，转发消息
      if (data.targetUserId) {
        const targetClient = this.connectedClients.get(data.targetUserId);
        if (targetClient) {
          targetClient.emit('signaling', {
            type: data.type,
            payload: data.payload,
            fromUserId: userId,
          });
        }
      }
    } catch (error) {
      this.logger.error(`处理信令消息失败: ${error.message}`);
      client.emit('error', { message: error.message });
    }
  }

  /**
   * 加入房间
   */
  @SubscribeMessage('join-room')
  async handleJoinRoom(
    @MessageBody() data: { roomId: string },
    @ConnectedSocket() client: Socket,
  ): Promise<void> {
    const userId = client.handshake.query.userId as string;
    
    try {
      await client.join(data.roomId);
      this.logger.log(`用户 ${userId} 加入房间 ${data.roomId}`);
      
      // 通知房间内其他用户
      client.to(data.roomId).emit('user-joined', { userId });
    } catch (error) {
      this.logger.error(`加入房间失败: ${error.message}`);
      client.emit('error', { message: error.message });
    }
  }

  /**
   * 离开房间
   */
  @SubscribeMessage('leave-room')
  async handleLeaveRoom(
    @MessageBody() data: { roomId: string },
    @ConnectedSocket() client: Socket,
  ): Promise<void> {
    const userId = client.handshake.query.userId as string;
    
    try {
      await client.leave(data.roomId);
      this.logger.log(`用户 ${userId} 离开房间 ${data.roomId}`);
      
      // 通知房间内其他用户
      client.to(data.roomId).emit('user-left', { userId });
    } catch (error) {
      this.logger.error(`离开房间失败: ${error.message}`);
      client.emit('error', { message: error.message });
    }
  }

  /**
   * 广播消息到房间
   */
  broadcastToRoom(roomId: string, event: string, data: any): void {
    this.server.to(roomId).emit(event, data);
  }

  /**
   * 发送消息给特定用户
   */
  sendToUser(userId: string, event: string, data: any): void {
    const client = this.connectedClients.get(userId);
    if (client) {
      client.emit(event, data);
    }
  }
}

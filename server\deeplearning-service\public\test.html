<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>深度学习推理服务测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #555;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        select, textarea, input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 20px;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .loading {
            color: #007bff;
            font-style: italic;
        }
        .model-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .model-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
        }
        .model-card h3 {
            margin: 0 0 10px 0;
            color: #007bff;
        }
        .model-status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-ready {
            background-color: #28a745;
            color: white;
        }
        .status-loading {
            background-color: #ffc107;
            color: black;
        }
        .status-error {
            background-color: #dc3545;
            color: white;
        }
    </style>
</head>
<body>
    <h1>🧠 深度学习推理服务测试</h1>

    <!-- 模型列表 -->
    <div class="container">
        <h2>📋 已加载模型</h2>
        <button onclick="loadModels()">刷新模型列表</button>
        <div id="modelList" class="model-list"></div>
    </div>

    <!-- 推理测试 -->
    <div class="container">
        <h2>🚀 推理测试</h2>
        <div class="form-group">
            <label for="modelSelect">选择模型:</label>
            <select id="modelSelect">
                <option value="">请选择模型...</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="inputText">输入数据 (JSON格式):</label>
            <textarea id="inputText" placeholder='例如: {"text": "这是一个测试文本"}'></textarea>
        </div>
        
        <div class="form-group">
            <label for="userId">用户ID:</label>
            <input type="text" id="userId" value="test-user" placeholder="输入用户ID">
        </div>
        
        <button onclick="submitInference()" id="submitBtn">提交推理请求</button>
        <button onclick="clearResults()">清空结果</button>
        
        <div id="inferenceResult" class="result" style="display: none;"></div>
    </div>

    <!-- 系统状态 -->
    <div class="container">
        <h2>📊 系统状态</h2>
        <button onclick="loadStats()">刷新统计</button>
        <button onclick="loadHealth()">健康检查</button>
        <div id="systemStats" class="result" style="display: none;"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3022/api/v1';
        
        // 页面加载时初始化
        window.onload = function() {
            loadModels();
            loadStats();
        };

        // 加载模型列表
        async function loadModels() {
            try {
                const response = await fetch(`${API_BASE}/models`);
                const data = await response.json();
                
                // 更新模型选择器
                const modelSelect = document.getElementById('modelSelect');
                modelSelect.innerHTML = '<option value="">请选择模型...</option>';
                
                // 更新模型列表显示
                const modelList = document.getElementById('modelList');
                modelList.innerHTML = '';
                
                data.models.forEach(model => {
                    // 添加到选择器
                    const option = document.createElement('option');
                    option.value = model.id;
                    option.textContent = `${model.name} (${model.id})`;
                    modelSelect.appendChild(option);
                    
                    // 添加到模型卡片
                    const card = document.createElement('div');
                    card.className = 'model-card';
                    card.innerHTML = `
                        <h3>${model.name}</h3>
                        <p><strong>ID:</strong> ${model.id}</p>
                        <p><strong>类型:</strong> ${model.type}</p>
                        <p><strong>版本:</strong> ${model.version}</p>
                        <p><strong>状态:</strong> <span class="model-status status-${model.status}">${model.status}</span></p>
                        <p><strong>使用次数:</strong> ${model.usageCount}</p>
                        <p><strong>内存使用:</strong> ${model.memoryUsage.toFixed(1)} MB</p>
                    `;
                    modelList.appendChild(card);
                });
                
            } catch (error) {
                console.error('加载模型列表失败:', error);
                document.getElementById('modelList').innerHTML = '<p class="error">加载模型列表失败</p>';
            }
        }

        // 提交推理请求
        async function submitInference() {
            const modelId = document.getElementById('modelSelect').value;
            const inputText = document.getElementById('inputText').value;
            const userId = document.getElementById('userId').value;
            const resultDiv = document.getElementById('inferenceResult');
            const submitBtn = document.getElementById('submitBtn');
            
            if (!modelId) {
                alert('请选择模型');
                return;
            }
            
            if (!inputText) {
                alert('请输入数据');
                return;
            }
            
            let inputData;
            try {
                inputData = JSON.parse(inputText);
            } catch (error) {
                alert('输入数据格式错误，请输入有效的JSON');
                return;
            }
            
            try {
                submitBtn.disabled = true;
                resultDiv.style.display = 'block';
                resultDiv.className = 'result loading';
                resultDiv.textContent = '正在提交推理请求...';
                
                // 提交推理请求
                const submitResponse = await fetch(`${API_BASE}/inference/submit`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        modelId: modelId,
                        input: inputData,
                        userId: userId,
                        priority: 5
                    })
                });
                
                const submitResult = await submitResponse.json();
                
                if (!submitResponse.ok) {
                    throw new Error(submitResult.message || '提交失败');
                }
                
                const requestId = submitResult.requestId;
                resultDiv.textContent = `请求已提交，ID: ${requestId}\n正在等待处理结果...`;
                
                // 轮询获取结果
                let attempts = 0;
                const maxAttempts = 20;
                
                const pollResult = async () => {
                    try {
                        const resultResponse = await fetch(`${API_BASE}/inference/result/${requestId}`);
                        
                        if (resultResponse.ok) {
                            const result = await resultResponse.json();
                            resultDiv.className = 'result success';
                            resultDiv.textContent = `推理完成！\n\n${JSON.stringify(result, null, 2)}`;
                            
                            // 刷新模型列表以更新使用统计
                            loadModels();
                            
                        } else if (attempts < maxAttempts) {
                            attempts++;
                            resultDiv.textContent = `请求已提交，ID: ${requestId}\n正在等待处理结果... (${attempts}/${maxAttempts})`;
                            setTimeout(pollResult, 1000);
                        } else {
                            resultDiv.className = 'result error';
                            resultDiv.textContent = '获取结果超时，请稍后手动查询';
                        }
                    } catch (error) {
                        if (attempts < maxAttempts) {
                            attempts++;
                            setTimeout(pollResult, 1000);
                        } else {
                            resultDiv.className = 'result error';
                            resultDiv.textContent = '获取结果失败: ' + error.message;
                        }
                    }
                };
                
                // 开始轮询
                setTimeout(pollResult, 1000);
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '推理请求失败: ' + error.message;
            } finally {
                submitBtn.disabled = false;
            }
        }

        // 加载系统统计
        async function loadStats() {
            try {
                const response = await fetch(`${API_BASE}/monitoring/stats`);
                const data = await response.json();
                
                const statsDiv = document.getElementById('systemStats');
                statsDiv.style.display = 'block';
                statsDiv.className = 'result success';
                statsDiv.textContent = JSON.stringify(data, null, 2);
                
            } catch (error) {
                const statsDiv = document.getElementById('systemStats');
                statsDiv.style.display = 'block';
                statsDiv.className = 'result error';
                statsDiv.textContent = '加载统计失败: ' + error.message;
            }
        }

        // 健康检查
        async function loadHealth() {
            try {
                const response = await fetch('http://localhost:3022/health/detailed');
                const data = await response.json();
                
                const statsDiv = document.getElementById('systemStats');
                statsDiv.style.display = 'block';
                statsDiv.className = 'result success';
                statsDiv.textContent = JSON.stringify(data, null, 2);
                
            } catch (error) {
                const statsDiv = document.getElementById('systemStats');
                statsDiv.style.display = 'block';
                statsDiv.className = 'result error';
                statsDiv.textContent = '健康检查失败: ' + error.message;
            }
        }

        // 清空结果
        function clearResults() {
            document.getElementById('inferenceResult').style.display = 'none';
            document.getElementById('systemStats').style.display = 'none';
        }

        // 设置示例输入
        function setExampleInput(type) {
            const inputText = document.getElementById('inputText');
            
            switch(type) {
                case 'text':
                    inputText.value = '{"text": "今天天气真好，我心情很愉快！"}';
                    break;
                case 'decision':
                    inputText.value = '{"scenario": "选择午餐", "options": ["中餐", "西餐", "日餐"]}';
                    break;
                case 'classification':
                    inputText.value = '{"text": "这个产品质量很好，我很满意"}';
                    break;
            }
        }
    </script>
</body>
</html>

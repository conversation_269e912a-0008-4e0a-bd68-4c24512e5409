/**
 * 健康检查控制器
 * 
 * 提供服务健康状态检查接口
 */

import {
  Controller,
  Get,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ModelInferenceService } from '../services/model-inference.service';
import { MonitoringService } from '../services/monitoring.service';

/**
 * 健康检查响应接口
 */
interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: number;
  uptime: number;
  version: string;
  environment: string;
  checks: {
    redis: {
      status: 'up' | 'down';
      responseTime?: number;
      error?: string;
    };
    models: {
      status: 'up' | 'down';
      loadedCount: number;
      readyCount: number;
    };
    queue: {
      status: 'up' | 'down';
      size: number;
      processing: number;
    };
    memory: {
      status: 'up' | 'down';
      usage: number;
      threshold: number;
    };
    disk: {
      status: 'up' | 'down';
      usage: number;
      threshold: number;
    };
  };
}

/**
 * 健康检查控制器
 */
@ApiTags('health')
@Controller()
export class HealthController {
  private readonly logger = new Logger(HealthController.name);
  private readonly startTime = Date.now();

  constructor(
    private readonly inferenceService: ModelInferenceService,
    private readonly monitoringService: MonitoringService,
  ) {}

  /**
   * 基础健康检查
   */
  @Get('health')
  @ApiOperation({ summary: '健康检查', description: '检查服务基本健康状态' })
  @ApiResponse({ status: 200, description: '服务健康' })
  @ApiResponse({ status: 503, description: '服务不健康' })
  async healthCheck(): Promise<{ status: string; timestamp: number }> {
    try {
      // 基础健康检查
      const isHealthy = await this.performBasicHealthCheck();

      if (isHealthy) {
        return {
          status: 'ok',
          timestamp: Date.now(),
        };
      } else {
        throw new HttpException('Service Unavailable', HttpStatus.SERVICE_UNAVAILABLE);
      }

    } catch (error) {
      this.logger.error('健康检查失败:', error);
      throw new HttpException('Service Unavailable', HttpStatus.SERVICE_UNAVAILABLE);
    }
  }

  /**
   * 详细健康检查
   */
  @Get('health/detailed')
  @ApiOperation({ summary: '详细健康检查', description: '获取详细的服务健康状态' })
  @ApiResponse({ status: 200, description: '详细健康状态' })
  async detailedHealthCheck(): Promise<HealthCheckResponse> {
    try {
      const checks = await this.performDetailedHealthCheck();
      
      // 确定整体状态
      const overallStatus = this.determineOverallStatus(checks);

      return {
        status: overallStatus,
        timestamp: Date.now(),
        uptime: Date.now() - this.startTime,
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        checks,
      };

    } catch (error) {
      this.logger.error('详细健康检查失败:', error);
      
      return {
        status: 'unhealthy',
        timestamp: Date.now(),
        uptime: Date.now() - this.startTime,
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        checks: {
          redis: { status: 'down', error: error instanceof Error ? error.message : String(error) },
          models: { status: 'down', loadedCount: 0, readyCount: 0 },
          queue: { status: 'down', size: 0, processing: 0 },
          memory: { status: 'down', usage: 0, threshold: 0.8 },
          disk: { status: 'down', usage: 0, threshold: 0.9 },
        },
      };
    }
  }

  /**
   * 就绪检查
   */
  @Get('ready')
  @ApiOperation({ summary: '就绪检查', description: '检查服务是否准备好接收请求' })
  @ApiResponse({ status: 200, description: '服务就绪' })
  @ApiResponse({ status: 503, description: '服务未就绪' })
  async readinessCheck(): Promise<{ status: string; ready: boolean }> {
    try {
      const isReady = await this.performReadinessCheck();

      if (isReady) {
        return {
          status: 'ready',
          ready: true,
        };
      } else {
        throw new HttpException('Service Not Ready', HttpStatus.SERVICE_UNAVAILABLE);
      }

    } catch (error) {
      this.logger.error('就绪检查失败:', error);
      throw new HttpException('Service Not Ready', HttpStatus.SERVICE_UNAVAILABLE);
    }
  }

  /**
   * 存活检查
   */
  @Get('live')
  @ApiOperation({ summary: '存活检查', description: '检查服务是否存活' })
  @ApiResponse({ status: 200, description: '服务存活' })
  async livenessCheck(): Promise<{ status: string; alive: boolean }> {
    // 存活检查通常只检查进程是否响应
    return {
      status: 'alive',
      alive: true,
    };
  }

  /**
   * 执行基础健康检查
   */
  private async performBasicHealthCheck(): Promise<boolean> {
    try {
      // 检查Redis连接
      const redisHealthy = await this.checkRedisHealth();
      
      // 检查基本功能
      const basicFunctional = this.checkBasicFunctionality();

      return redisHealthy && basicFunctional;

    } catch (error) {
      this.logger.error('基础健康检查失败:', error);
      return false;
    }
  }

  /**
   * 执行详细健康检查
   */
  private async performDetailedHealthCheck(): Promise<HealthCheckResponse['checks']> {
    const [
      redisCheck,
      modelsCheck,
      queueCheck,
      memoryCheck,
      diskCheck,
    ] = await Promise.allSettled([
      this.checkRedisDetailedHealth(),
      this.checkModelsHealth(),
      this.checkQueueHealth(),
      this.checkMemoryHealth(),
      this.checkDiskHealth(),
    ]);

    return {
      redis: redisCheck.status === 'fulfilled' ? redisCheck.value : { status: 'down', error: 'Check failed' },
      models: modelsCheck.status === 'fulfilled' ? modelsCheck.value : { status: 'down', loadedCount: 0, readyCount: 0 },
      queue: queueCheck.status === 'fulfilled' ? queueCheck.value : { status: 'down', size: 0, processing: 0 },
      memory: memoryCheck.status === 'fulfilled' ? memoryCheck.value : { status: 'down', usage: 0, threshold: 0.8 },
      disk: diskCheck.status === 'fulfilled' ? diskCheck.value : { status: 'down', usage: 0, threshold: 0.9 },
    };
  }

  /**
   * 执行就绪检查
   */
  private async performReadinessCheck(): Promise<boolean> {
    try {
      // 检查Redis连接
      const redisReady = await this.checkRedisHealth();
      
      // 检查至少有一个模型就绪
      const modelsReady = this.checkModelsReady();
      
      // 检查队列正常
      const queueReady = await this.checkQueueReady();

      return redisReady && modelsReady && queueReady;

    } catch (error) {
      this.logger.error('就绪检查失败:', error);
      return false;
    }
  }

  /**
   * 检查Redis健康状态
   */
  private async checkRedisHealth(): Promise<boolean> {
    try {
      // 这里需要实际的Redis健康检查逻辑
      // 暂时返回true
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 检查Redis详细健康状态
   */
  private async checkRedisDetailedHealth(): Promise<{ status: 'up' | 'down'; responseTime?: number; error?: string }> {
    try {
      const startTime = Date.now();
      // 执行Redis ping
      const responseTime = Date.now() - startTime;
      
      return {
        status: 'up',
        responseTime,
      };
    } catch (error) {
      return {
        status: 'down',
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * 检查基本功能
   */
  private checkBasicFunctionality(): boolean {
    try {
      // 检查基本服务是否可用
      return this.inferenceService !== undefined && this.monitoringService !== undefined;
    } catch (error) {
      return false;
    }
  }

  /**
   * 检查模型健康状态
   */
  private async checkModelsHealth(): Promise<{ status: 'up' | 'down'; loadedCount: number; readyCount: number }> {
    try {
      const models = this.inferenceService.getModels();
      const loadedCount = models.length;
      const readyCount = models.filter(model => model.status === 'ready').length;

      return {
        status: loadedCount > 0 ? 'up' : 'down',
        loadedCount,
        readyCount,
      };
    } catch (error) {
      return {
        status: 'down',
        loadedCount: 0,
        readyCount: 0,
      };
    }
  }

  /**
   * 检查队列健康状态
   */
  private async checkQueueHealth(): Promise<{ status: 'up' | 'down'; size: number; processing: number }> {
    try {
      const stats = this.inferenceService.getInferenceStats();

      return {
        status: 'up',
        size: stats.queuedRequests,
        processing: stats.activeRequests,
      };
    } catch (error) {
      return {
        status: 'down',
        size: 0,
        processing: 0,
      };
    }
  }

  /**
   * 检查内存健康状态
   */
  private async checkMemoryHealth(): Promise<{ status: 'up' | 'down'; usage: number; threshold: number }> {
    try {
      const memoryUsage = process.memoryUsage();
      const totalMemory = memoryUsage.heapTotal;
      const usedMemory = memoryUsage.heapUsed;
      const usage = usedMemory / totalMemory;
      const threshold = 0.8;

      return {
        status: usage < threshold ? 'up' : 'down',
        usage,
        threshold,
      };
    } catch (error) {
      return {
        status: 'down',
        usage: 0,
        threshold: 0.8,
      };
    }
  }

  /**
   * 检查磁盘健康状态
   */
  private async checkDiskHealth(): Promise<{ status: 'up' | 'down'; usage: number; threshold: number }> {
    try {
      // 简化的磁盘检查
      const threshold = 0.9;
      const usage = 0.5; // 模拟值

      return {
        status: usage < threshold ? 'up' : 'down',
        usage,
        threshold,
      };
    } catch (error) {
      return {
        status: 'down',
        usage: 0,
        threshold: 0.9,
      };
    }
  }

  /**
   * 检查模型是否就绪
   */
  private checkModelsReady(): boolean {
    try {
      const models = this.inferenceService.getModels();
      return models.some(model => model.status === 'ready');
    } catch (error) {
      return false;
    }
  }

  /**
   * 检查队列是否就绪
   */
  private async checkQueueReady(): Promise<boolean> {
    try {
      // 检查队列是否正常工作
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * 确定整体状态
   */
  private determineOverallStatus(checks: HealthCheckResponse['checks']): 'healthy' | 'unhealthy' | 'degraded' {
    const criticalChecks = [checks.redis, checks.models];
    const allChecks = Object.values(checks);

    // 如果关键检查失败，则不健康
    if (criticalChecks.some(check => check.status === 'down')) {
      return 'unhealthy';
    }

    // 如果任何检查失败，则降级
    if (allChecks.some(check => check.status === 'down')) {
      return 'degraded';
    }

    return 'healthy';
  }
}

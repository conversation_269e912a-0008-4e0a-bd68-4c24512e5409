import { 
  Controller, 
  Get, 
  Query, 
  HttpStatus, 
  HttpException,
  Logger 
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { EdgeHealthCheckService, HealthStatus } from '../services/edge-health-check.service';

/**
 * 边缘健康检查控制器
 * 提供健康检查相关的API接口
 */
@ApiTags('边缘健康检查')
@Controller('health')
export class EdgeHealthController {
  private readonly logger = new Logger(EdgeHealthController.name);

  constructor(
    private readonly healthCheckService: EdgeHealthCheckService,
  ) {}

  /**
   * 基础健康检查
   */
  @Get()
  @ApiOperation({ summary: '基础健康检查' })
  @ApiResponse({ status: 200, description: '节点健康' })
  @ApiResponse({ status: 503, description: '节点不健康' })
  async basicHealthCheck() {
    try {
      const isHealthy = this.healthCheckService.isNodeHealthy();
      const latestCheck = this.healthCheckService.getLatestHealthCheck();

      if (!isHealthy) {
        throw new HttpException(
          {
            success: false,
            status: 'unhealthy',
            message: '边缘节点状态不健康',
            lastCheck: latestCheck,
            timestamp: new Date().toISOString()
          },
          HttpStatus.SERVICE_UNAVAILABLE
        );
      }

      return {
        success: true,
        status: 'healthy',
        message: '边缘节点运行正常',
        lastCheck: latestCheck,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      
      this.logger.error(`健康检查失败: ${error.message}`);
      throw new HttpException(
        {
          success: false,
          status: 'error',
          message: '健康检查执行失败',
          error: error.message,
          timestamp: new Date().toISOString()
        },
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 详细健康检查
   */
  @Get('detailed')
  @ApiOperation({ summary: '详细健康检查' })
  @ApiResponse({ status: 200, description: '成功获取详细健康信息' })
  async detailedHealthCheck() {
    try {
      const healthResult = await this.healthCheckService.performHealthCheck();
      const healthStats = this.healthCheckService.getHealthStats();

      return {
        success: true,
        data: {
          current: healthResult,
          stats: healthStats,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      this.logger.error(`详细健康检查失败: ${error.message}`);
      throw new HttpException(
        '详细健康检查失败',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取健康检查历史
   */
  @Get('history')
  @ApiOperation({ summary: '获取健康检查历史' })
  @ApiResponse({ status: 200, description: '成功获取健康检查历史' })
  @ApiQuery({ name: 'limit', required: false, description: '限制返回记录数量' })
  getHealthHistory(@Query('limit') limit?: string) {
    try {
      const limitNum = limit ? parseInt(limit, 10) : undefined;
      const history = this.healthCheckService.getHealthCheckHistory(limitNum);

      return {
        success: true,
        data: {
          history,
          count: history.length,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      this.logger.error(`获取健康检查历史失败: ${error.message}`);
      throw new HttpException(
        '获取健康检查历史失败',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取健康统计信息
   */
  @Get('stats')
  @ApiOperation({ summary: '获取健康统计信息' })
  @ApiResponse({ status: 200, description: '成功获取健康统计信息' })
  getHealthStats() {
    try {
      const stats = this.healthCheckService.getHealthStats();
      const latest = this.healthCheckService.getLatestHealthCheck();

      return {
        success: true,
        data: {
          stats,
          latest,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      this.logger.error(`获取健康统计信息失败: ${error.message}`);
      throw new HttpException(
        '获取健康统计信息失败',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 立即执行健康检查
   */
  @Get('check')
  @ApiOperation({ summary: '立即执行健康检查' })
  @ApiResponse({ status: 200, description: '健康检查执行成功' })
  async performHealthCheck() {
    try {
      const result = await this.healthCheckService.performHealthCheck();

      return {
        success: true,
        data: result,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error(`执行健康检查失败: ${error.message}`);
      throw new HttpException(
        '执行健康检查失败',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 检查特定组件健康状态
   */
  @Get('component/:component')
  @ApiOperation({ summary: '检查特定组件健康状态' })
  @ApiResponse({ status: 200, description: '成功获取组件健康状态' })
  getComponentHealth(@Query('component') component: string) {
    try {
      const latest = this.healthCheckService.getLatestHealthCheck();
      
      if (!latest) {
        throw new HttpException(
          '没有可用的健康检查数据',
          HttpStatus.NOT_FOUND
        );
      }

      const validComponents = ['cpu', 'memory', 'disk', 'network', 'services'];
      if (!validComponents.includes(component)) {
        throw new HttpException(
          `无效的组件名称。有效组件: ${validComponents.join(', ')}`,
          HttpStatus.BAD_REQUEST
        );
      }

      const componentHealth = latest.checks[component];

      return {
        success: true,
        data: {
          component,
          health: componentHealth,
          overallStatus: latest.status,
          timestamp: latest.timestamp
        }
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      
      this.logger.error(`获取组件健康状态失败: ${error.message}`);
      throw new HttpException(
        '获取组件健康状态失败',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取系统运行时间
   */
  @Get('uptime')
  @ApiOperation({ summary: '获取系统运行时间' })
  @ApiResponse({ status: 200, description: '成功获取系统运行时间' })
  getUptime() {
    try {
      const stats = this.healthCheckService.getHealthStats();
      const uptimeMs = stats.uptime;
      const uptimeSeconds = Math.floor(uptimeMs / 1000);
      const uptimeMinutes = Math.floor(uptimeSeconds / 60);
      const uptimeHours = Math.floor(uptimeMinutes / 60);
      const uptimeDays = Math.floor(uptimeHours / 24);

      return {
        success: true,
        data: {
          uptime: {
            milliseconds: uptimeMs,
            seconds: uptimeSeconds,
            minutes: uptimeMinutes,
            hours: uptimeHours,
            days: uptimeDays,
            formatted: `${uptimeDays}天 ${uptimeHours % 24}小时 ${uptimeMinutes % 60}分钟 ${uptimeSeconds % 60}秒`
          },
          currentStatus: stats.currentStatus,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      this.logger.error(`获取系统运行时间失败: ${error.message}`);
      throw new HttpException(
        '获取系统运行时间失败',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取健康状态摘要
   */
  @Get('summary')
  @ApiOperation({ summary: '获取健康状态摘要' })
  @ApiResponse({ status: 200, description: '成功获取健康状态摘要' })
  getHealthSummary() {
    try {
      const latest = this.healthCheckService.getLatestHealthCheck();
      const stats = this.healthCheckService.getHealthStats();
      const isHealthy = this.healthCheckService.isNodeHealthy();

      if (!latest) {
        return {
          success: true,
          data: {
            status: 'unknown',
            message: '暂无健康检查数据',
            isHealthy: false,
            stats,
            timestamp: new Date().toISOString()
          }
        };
      }

      const summary = {
        status: latest.status,
        isHealthy,
        uptime: stats.uptime,
        lastCheck: latest.timestamp,
        components: {
          cpu: {
            status: latest.checks.cpu.status,
            usage: latest.checks.cpu.usage
          },
          memory: {
            status: latest.checks.memory.status,
            usage: latest.checks.memory.usage
          },
          disk: {
            status: latest.checks.disk.status,
            usage: latest.checks.disk.usage
          },
          network: {
            status: latest.checks.network.status,
            latency: latest.checks.network.latency
          },
          services: {
            status: latest.checks.services.status,
            activeCount: latest.checks.services.activeServices.length,
            failedCount: latest.checks.services.failedServices.length
          }
        }
      };

      return {
        success: true,
        data: summary,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error(`获取健康状态摘要失败: ${error.message}`);
      throw new HttpException(
        '获取健康状态摘要失败',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}

version: '3.8'

services:
  edge-router-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "3012:3012"
      - "9230:9229"  # Debug port
    environment:
      - NODE_ENV=development
      - PORT=3012
      - EDGE_REGISTRY_HOST=localhost
      - EDGE_REGISTRY_PORT=3011
      - REDIS_HOST=redis-dev
      - REDIS_PORT=6379
      - REDIS_DB=1
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - redis-dev
    restart: unless-stopped
    networks:
      - edge-router-dev-network
    command: npm run start:debug

  redis-dev:
    image: redis:7-alpine
    ports:
      - "6381:6379"
    volumes:
      - redis_dev_data:/data
    restart: unless-stopped
    networks:
      - edge-router-dev-network

volumes:
  redis_dev_data:

networks:
  edge-router-dev-network:
    driver: bridge

import { Module } from '@nestjs/common';
import { TerminusModule } from '@nestjs/terminus';

import { HealthController } from '../controllers/health.controller';
import { EdgeRegistryClientService } from '../services/edge-registry-client.service';
import { RoutingCacheService } from '../services/routing-cache.service';

/**
 * 健康检查模块
 */
@Module({
  imports: [TerminusModule],
  controllers: [HealthController],
  providers: [EdgeRegistryClientService, RoutingCacheService],
})
export class HealthModule {}

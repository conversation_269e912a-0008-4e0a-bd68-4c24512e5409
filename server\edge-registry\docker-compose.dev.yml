version: '3.8'

services:
  edge-registry-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "3011:3011"
      - "9229:9229"  # Debug port
    environment:
      - NODE_ENV=development
      - PORT=3011
      - DB_HOST=mysql-dev
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=password
      - DB_DATABASE=dl_edge_registry_dev
      - DB_SYNCHRONIZE=true
      - DB_LOGGING=true
      - REDIS_HOST=redis-dev
      - REDIS_PORT=6379
    volumes:
      - .:/app
      - /app/node_modules
    depends_on:
      - mysql-dev
      - redis-dev
    restart: unless-stopped
    networks:
      - edge-registry-dev-network
    command: npm run start:debug

  mysql-dev:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=password
      - MYSQL_DATABASE=dl_edge_registry_dev
      - MYSQL_CHARACTER_SET_SERVER=utf8mb4
      - MYSQL_COLLATION_SERVER=utf8mb4_unicode_ci
    ports:
      - "3307:3306"
    volumes:
      - mysql_dev_data:/var/lib/mysql
    restart: unless-stopped
    networks:
      - edge-registry-dev-network

  redis-dev:
    image: redis:7-alpine
    ports:
      - "6380:6379"
    volumes:
      - redis_dev_data:/data
    restart: unless-stopped
    networks:
      - edge-registry-dev-network

volumes:
  mysql_dev_data:
  redis_dev_data:

networks:
  edge-registry-dev-network:
    driver: bridge

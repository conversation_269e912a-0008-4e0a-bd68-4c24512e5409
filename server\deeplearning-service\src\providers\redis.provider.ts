/**
 * Redis 提供者
 * 
 * 提供Redis连接和操作封装
 */

import { Injectable, Logger, Inject, OnModuleDestroy } from '@nestjs/common';
import Redis from 'ioredis';

/**
 * Redis 提供者
 */
@Injectable()
export class RedisProvider implements OnModuleDestroy {
  private readonly logger = new Logger(RedisProvider.name);
  private client: Redis;
  private subscriber: Redis;
  private publisher: Redis;

  constructor(
    @Inject('REDIS_CONFIG') private readonly config: any,
  ) {
    this.initializeConnections();
  }

  /**
   * 初始化Redis连接
   */
  private initializeConnections(): void {
    try {
      // 主客户端
      this.client = new Redis(this.config);
      
      // 订阅客户端
      this.subscriber = new Redis(this.config);
      
      // 发布客户端
      this.publisher = new Redis(this.config);

      // 设置事件监听器
      this.setupEventListeners();

      this.logger.log('Redis连接已初始化');

    } catch (error) {
      this.logger.error('Redis连接初始化失败:', error);
      throw error;
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 主客户端事件
    this.client.on('connect', () => {
      this.logger.log('Redis主客户端已连接');
    });

    this.client.on('error', (error) => {
      this.logger.error('Redis主客户端错误:', error);
    });

    this.client.on('close', () => {
      this.logger.warn('Redis主客户端连接已关闭');
    });

    // 订阅客户端事件
    this.subscriber.on('connect', () => {
      this.logger.log('Redis订阅客户端已连接');
    });

    this.subscriber.on('error', (error) => {
      this.logger.error('Redis订阅客户端错误:', error);
    });

    // 发布客户端事件
    this.publisher.on('connect', () => {
      this.logger.log('Redis发布客户端已连接');
    });

    this.publisher.on('error', (error) => {
      this.logger.error('Redis发布客户端错误:', error);
    });
  }

  /**
   * 获取主客户端
   */
  public getClient(): Redis {
    return this.client;
  }

  /**
   * 获取订阅客户端
   */
  public getSubscriber(): Redis {
    return this.subscriber;
  }

  /**
   * 获取发布客户端
   */
  public getPublisher(): Redis {
    return this.publisher;
  }

  /**
   * 设置键值
   */
  public async set(key: string, value: any, ttl?: number): Promise<void> {
    try {
      const serializedValue = JSON.stringify(value);
      
      if (ttl) {
        await this.client.setex(key, ttl, serializedValue);
      } else {
        await this.client.set(key, serializedValue);
      }

    } catch (error) {
      this.logger.error(`设置键值失败: ${key}`, error);
      throw error;
    }
  }

  /**
   * 获取键值
   */
  public async get<T = any>(key: string): Promise<T | null> {
    try {
      const value = await this.client.get(key);
      
      if (value === null) {
        return null;
      }

      return JSON.parse(value) as T;

    } catch (error) {
      this.logger.error(`获取键值失败: ${key}`, error);
      throw error;
    }
  }

  /**
   * 删除键
   */
  public async delete(key: string): Promise<void> {
    try {
      await this.client.del(key);

    } catch (error) {
      this.logger.error(`删除键失败: ${key}`, error);
      throw error;
    }
  }

  /**
   * 检查键是否存在
   */
  public async exists(key: string): Promise<boolean> {
    try {
      const result = await this.client.exists(key);
      return result === 1;

    } catch (error) {
      this.logger.error(`检查键存在失败: ${key}`, error);
      throw error;
    }
  }

  /**
   * 设置键过期时间
   */
  public async expire(key: string, seconds: number): Promise<void> {
    try {
      await this.client.expire(key, seconds);

    } catch (error) {
      this.logger.error(`设置键过期时间失败: ${key}`, error);
      throw error;
    }
  }

  /**
   * 获取键的剩余生存时间
   */
  public async ttl(key: string): Promise<number> {
    try {
      return await this.client.ttl(key);

    } catch (error) {
      this.logger.error(`获取键TTL失败: ${key}`, error);
      throw error;
    }
  }

  /**
   * 列表操作 - 左推入
   */
  public async lpush(key: string, ...values: any[]): Promise<number> {
    try {
      const serializedValues = values.map(v => JSON.stringify(v));
      return await this.client.lpush(key, ...serializedValues);

    } catch (error) {
      this.logger.error(`列表左推入失败: ${key}`, error);
      throw error;
    }
  }

  /**
   * 列表操作 - 右推入
   */
  public async rpush(key: string, ...values: any[]): Promise<number> {
    try {
      const serializedValues = values.map(v => JSON.stringify(v));
      return await this.client.rpush(key, ...serializedValues);

    } catch (error) {
      this.logger.error(`列表右推入失败: ${key}`, error);
      throw error;
    }
  }

  /**
   * 列表操作 - 左弹出
   */
  public async lpop<T = any>(key: string): Promise<T | null> {
    try {
      const value = await this.client.lpop(key);
      
      if (value === null) {
        return null;
      }

      return JSON.parse(value) as T;

    } catch (error) {
      this.logger.error(`列表左弹出失败: ${key}`, error);
      throw error;
    }
  }

  /**
   * 列表操作 - 右弹出
   */
  public async rpop<T = any>(key: string): Promise<T | null> {
    try {
      const value = await this.client.rpop(key);
      
      if (value === null) {
        return null;
      }

      return JSON.parse(value) as T;

    } catch (error) {
      this.logger.error(`列表右弹出失败: ${key}`, error);
      throw error;
    }
  }

  /**
   * 列表操作 - 获取长度
   */
  public async llen(key: string): Promise<number> {
    try {
      return await this.client.llen(key);

    } catch (error) {
      this.logger.error(`获取列表长度失败: ${key}`, error);
      throw error;
    }
  }

  /**
   * 集合操作 - 添加成员
   */
  public async sadd(key: string, ...members: any[]): Promise<number> {
    try {
      const serializedMembers = members.map(m => JSON.stringify(m));
      return await this.client.sadd(key, ...serializedMembers);

    } catch (error) {
      this.logger.error(`集合添加成员失败: ${key}`, error);
      throw error;
    }
  }

  /**
   * 集合操作 - 移除成员
   */
  public async srem(key: string, ...members: any[]): Promise<number> {
    try {
      const serializedMembers = members.map(m => JSON.stringify(m));
      return await this.client.srem(key, ...serializedMembers);

    } catch (error) {
      this.logger.error(`集合移除成员失败: ${key}`, error);
      throw error;
    }
  }

  /**
   * 集合操作 - 获取所有成员
   */
  public async smembers<T = any>(key: string): Promise<T[]> {
    try {
      const members = await this.client.smembers(key);
      return members.map(m => JSON.parse(m)) as T[];

    } catch (error) {
      this.logger.error(`获取集合成员失败: ${key}`, error);
      throw error;
    }
  }

  /**
   * 集合操作 - 获取成员数量
   */
  public async scard(key: string): Promise<number> {
    try {
      return await this.client.scard(key);

    } catch (error) {
      this.logger.error(`获取集合大小失败: ${key}`, error);
      throw error;
    }
  }

  /**
   * 发布消息
   */
  public async publish(channel: string, message: any): Promise<number> {
    try {
      const serializedMessage = JSON.stringify(message);
      return await this.publisher.publish(channel, serializedMessage);

    } catch (error) {
      this.logger.error(`发布消息失败: ${channel}`, error);
      throw error;
    }
  }

  /**
   * 订阅频道
   */
  public async subscribe(channel: string, callback: (message: any) => void): Promise<void> {
    try {
      await this.subscriber.subscribe(channel);
      
      this.subscriber.on('message', (receivedChannel, message) => {
        if (receivedChannel === channel) {
          try {
            const parsedMessage = JSON.parse(message);
            callback(parsedMessage);
          } catch (error) {
            this.logger.error('解析订阅消息失败:', error);
          }
        }
      });

    } catch (error) {
      this.logger.error(`订阅频道失败: ${channel}`, error);
      throw error;
    }
  }

  /**
   * 取消订阅
   */
  public async unsubscribe(channel: string): Promise<void> {
    try {
      await this.subscriber.unsubscribe(channel);

    } catch (error) {
      this.logger.error(`取消订阅失败: ${channel}`, error);
      throw error;
    }
  }

  /**
   * 执行Lua脚本
   */
  public async eval(script: string, keys: string[], args: any[]): Promise<any> {
    try {
      const serializedArgs = args.map(arg => JSON.stringify(arg));
      return await this.client.eval(script, keys.length, ...keys, ...serializedArgs);

    } catch (error) {
      this.logger.error('执行Lua脚本失败:', error);
      throw error;
    }
  }

  /**
   * 获取匹配的键
   */
  public async keys(pattern: string): Promise<string[]> {
    try {
      return await this.client.keys(pattern);

    } catch (error) {
      this.logger.error(`获取匹配键失败: ${pattern}`, error);
      throw error;
    }
  }

  /**
   * 清空数据库
   */
  public async flushdb(): Promise<void> {
    try {
      await this.client.flushdb();

    } catch (error) {
      this.logger.error('清空数据库失败:', error);
      throw error;
    }
  }

  /**
   * 模块销毁时清理连接
   */
  public async onModuleDestroy(): Promise<void> {
    try {
      await this.client.disconnect();
      await this.subscriber.disconnect();
      await this.publisher.disconnect();
      
      this.logger.log('Redis连接已关闭');

    } catch (error) {
      this.logger.error('关闭Redis连接失败:', error);
    }
  }
}

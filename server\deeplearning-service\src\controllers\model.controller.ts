/**
 * 模型管理控制器
 * 
 * 处理模型管理相关的HTTP请求
 */

import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpException,
  Logger,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiConsumes, ApiBody } from '@nestjs/swagger';
import { ModelInferenceService } from '../services/model-inference.service';
import { ModelManagementService } from '../services/model-management.service';
import {
  LoadModelDto,
  ModelInfoDto,
  ModelMetricsDto,
  UpdateModelDto,
} from '../dto/model.dto';

/**
 * 模型管理控制器
 */
@ApiTags('models')
@Controller('models')
export class ModelController {
  private readonly logger = new Logger(ModelController.name);

  constructor(
    private readonly inferenceService: ModelInferenceService,
    private readonly modelManagementService: ModelManagementService,
  ) {}

  /**
   * 获取所有模型列表
   */
  @Get()
  @ApiOperation({ summary: '获取模型列表', description: '获取所有已加载的模型信息' })
  @ApiResponse({ status: 200, description: '模型列表', type: [ModelInfoDto] })
  async getModels(
    @Query('status') status?: string,
    @Query('type') type?: string,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
  ): Promise<{ models: ModelInfoDto[]; total: number }> {
    try {
      let models = this.inferenceService.getModels();

      // 过滤条件
      if (status) {
        models = models.filter(model => model.status === status);
      }

      if (type) {
        models = models.filter(model => model.type === type);
      }

      // 分页
      const total = models.length;
      const startIndex = offset || 0;
      const endIndex = limit ? startIndex + limit : models.length;
      const paginatedModels = models.slice(startIndex, endIndex);

      return {
        models: paginatedModels.map(model => ({
          id: model.id,
          name: model.name,
          type: model.type,
          version: model.version,
          status: model.status,
          loadTime: model.loadTime,
          lastUsed: model.lastUsed,
          usageCount: model.usageCount,
          memoryUsage: model.memoryUsage,
          config: model.config,
        })),
        total,
      };

    } catch (error) {
      this.logger.error('获取模型列表失败:', error);
      throw new HttpException('获取模型列表失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取特定模型信息
   */
  @Get(':modelId')
  @ApiOperation({ summary: '获取模型信息', description: '获取指定模型的详细信息' })
  @ApiResponse({ status: 200, description: '模型信息', type: ModelInfoDto })
  @ApiResponse({ status: 404, description: '模型不存在' })
  async getModel(@Param('modelId') modelId: string): Promise<ModelInfoDto> {
    try {
      const models = this.inferenceService.getModels();
      const model = models.find(m => m.id === modelId);

      if (!model) {
        throw new HttpException('模型不存在', HttpStatus.NOT_FOUND);
      }

      return {
        id: model.id,
        name: model.name,
        type: model.type,
        version: model.version,
        status: model.status,
        loadTime: model.loadTime,
        lastUsed: model.lastUsed,
        usageCount: model.usageCount,
        memoryUsage: model.memoryUsage,
        config: model.config,
      };

    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error('获取模型信息失败:', error);
      throw new HttpException('获取模型信息失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 加载模型
   */
  @Post('load')
  @ApiOperation({ summary: '加载模型', description: '加载新的模型到内存中' })
  @ApiResponse({ status: 201, description: '模型加载成功' })
  @ApiResponse({ status: 400, description: '模型配置错误' })
  @ApiResponse({ status: 409, description: '模型已存在' })
  async loadModel(@Body() loadModelDto: LoadModelDto): Promise<{ message: string; modelId: string }> {
    try {
      this.logger.log(`加载模型: ${loadModelDto.id}`);

      // 检查模型是否已存在
      const existingModels = this.inferenceService.getModels();
      if (existingModels.some(model => model.id === loadModelDto.id)) {
        throw new HttpException('模型已存在', HttpStatus.CONFLICT);
      }

      await this.inferenceService.loadModel({
        id: loadModelDto.id,
        name: loadModelDto.name,
        type: loadModelDto.type,
        version: loadModelDto.version,
        path: loadModelDto.path,
        config: loadModelDto.config,
      });

      return {
        message: '模型加载成功',
        modelId: loadModelDto.id,
      };

    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error('加载模型失败:', error);
      throw new HttpException('加载模型失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 上传并加载模型
   */
  @Post('upload')
  @UseInterceptors(FileInterceptor('model'))
  @ApiOperation({ summary: '上传并加载模型', description: '上传模型文件并加载到内存中' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: '模型文件和配置',
    schema: {
      type: 'object',
      properties: {
        model: {
          type: 'string',
          format: 'binary',
        },
        name: {
          type: 'string',
        },
        type: {
          type: 'string',
        },
        version: {
          type: 'string',
        },
        config: {
          type: 'string',
        },
      },
    },
  })
  async uploadModel(
    @UploadedFile() file: Express.Multer.File,
    @Body('name') name: string,
    @Body('type') type: string,
    @Body('version') version: string,
    @Body('config') config?: string,
  ): Promise<{ message: string; modelId: string }> {
    try {
      if (!file) {
        throw new HttpException('未上传模型文件', HttpStatus.BAD_REQUEST);
      }

      this.logger.log(`上传模型: ${name}, 文件大小: ${file.size}`);

      const modelId = `uploaded_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      
      // 保存模型文件
      const savedPath = await this.modelManagementService.saveUploadedModel(file, modelId);

      // 加载模型
      await this.inferenceService.loadModel({
        id: modelId,
        name,
        type,
        version,
        path: savedPath,
        config: config ? JSON.parse(config) : {},
      });

      return {
        message: '模型上传并加载成功',
        modelId,
      };

    } catch (error) {
      this.logger.error('上传模型失败:', error);
      throw new HttpException('上传模型失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 更新模型配置
   */
  @Put(':modelId')
  @ApiOperation({ summary: '更新模型配置', description: '更新指定模型的配置信息' })
  @ApiResponse({ status: 200, description: '模型配置更新成功' })
  @ApiResponse({ status: 404, description: '模型不存在' })
  async updateModel(
    @Param('modelId') modelId: string,
    @Body() updateDto: UpdateModelDto,
  ): Promise<{ message: string }> {
    try {
      this.logger.log(`更新模型配置: ${modelId}`);

      // 这里需要实现更新模型配置的逻辑
      await this.modelManagementService.updateModelConfig(modelId, updateDto);

      return { message: '模型配置更新成功' };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);

      if (errorMessage.includes('不存在')) {
        throw new HttpException('模型不存在', HttpStatus.NOT_FOUND);
      }

      this.logger.error('更新模型配置失败:', error);
      throw new HttpException('更新模型配置失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 卸载模型
   */
  @Delete(':modelId')
  @ApiOperation({ summary: '卸载模型', description: '从内存中卸载指定模型' })
  @ApiResponse({ status: 200, description: '模型卸载成功' })
  @ApiResponse({ status: 404, description: '模型不存在' })
  @ApiResponse({ status: 409, description: '模型正在使用中' })
  async unloadModel(@Param('modelId') modelId: string): Promise<{ message: string }> {
    try {
      this.logger.log(`卸载模型: ${modelId}`);

      await this.inferenceService.unloadModel(modelId);

      return { message: '模型卸载成功' };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);

      if (errorMessage.includes('不存在')) {
        throw new HttpException('模型不存在', HttpStatus.NOT_FOUND);
      } else if (errorMessage.includes('使用中')) {
        throw new HttpException('模型正在使用中，无法卸载', HttpStatus.CONFLICT);
      }

      this.logger.error('卸载模型失败:', error);
      throw new HttpException('卸载模型失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取模型指标
   */
  @Get(':modelId/metrics')
  @ApiOperation({ summary: '获取模型指标', description: '获取指定模型的性能指标' })
  @ApiResponse({ status: 200, description: '模型指标', type: ModelMetricsDto })
  @ApiResponse({ status: 404, description: '模型不存在' })
  async getModelMetrics(@Param('modelId') modelId: string): Promise<ModelMetricsDto> {
    try {
      const metrics = this.inferenceService.getModelMetrics(modelId);

      if (!metrics) {
        throw new HttpException('模型不存在', HttpStatus.NOT_FOUND);
      }

      return {
        modelId,
        totalRequests: metrics.totalRequests,
        successfulRequests: metrics.successfulRequests,
        failedRequests: metrics.failedRequests,
        averageLatency: metrics.averageLatency,
        throughput: metrics.throughput,
        errorRate: metrics.errorRate,
        lastHourRequests: metrics.lastHourRequests,
        peakLatency: metrics.peakLatency,
        minLatency: metrics.minLatency === Infinity ? 0 : metrics.minLatency,
      };

    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error('获取模型指标失败:', error);
      throw new HttpException('获取模型指标失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 重新加载模型
   */
  @Post(':modelId/reload')
  @ApiOperation({ summary: '重新加载模型', description: '重新加载指定模型' })
  @ApiResponse({ status: 200, description: '模型重新加载成功' })
  @ApiResponse({ status: 404, description: '模型不存在' })
  async reloadModel(@Param('modelId') modelId: string): Promise<{ message: string }> {
    try {
      this.logger.log(`重新加载模型: ${modelId}`);

      await this.modelManagementService.reloadModel(modelId);

      return { message: '模型重新加载成功' };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);

      if (errorMessage.includes('不存在')) {
        throw new HttpException('模型不存在', HttpStatus.NOT_FOUND);
      }

      this.logger.error('重新加载模型失败:', error);
      throw new HttpException('重新加载模型失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}

import { Controller, Get, Post, Body, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { EcosystemPlatformService } from './ecosystem-platform.service';

@ApiTags('ecosystem')
@Controller('ecosystem')
export class EcosystemController {
  constructor(private readonly ecosystemService: EcosystemPlatformService) {}

  @Get('statistics')
  @ApiOperation({ summary: '获取生态系统统计信息' })
  @ApiResponse({ status: 200, description: '生态系统统计数据' })
  async getStatistics() {
    return this.ecosystemService.getEcosystemStatistics();
  }

  @Post('partners/register')
  @ApiOperation({ summary: '注册合作伙伴' })
  @ApiResponse({ status: 201, description: '合作伙伴注册成功' })
  async registerPartner(@Body() partnerApplication: any) {
    return this.ecosystemService.registerPartner(partnerApplication);
  }

  @Post('apis/publish')
  @ApiOperation({ summary: '发布API规范' })
  @ApiResponse({ status: 201, description: 'API规范发布成功' })
  async publishAPI(@Body() apiSpec: any) {
    return this.ecosystemService.publishAPISpecification(apiSpec);
  }

  @Post('applications/submit')
  @ApiOperation({ summary: '提交第三方应用' })
  @ApiResponse({ status: 201, description: '应用提交成功' })
  async submitApplication(@Body() appSubmission: any) {
    return this.ecosystemService.submitThirdPartyApplication(appSubmission);
  }

  @Post('standards/create')
  @ApiOperation({ summary: '创建行业标准' })
  @ApiResponse({ status: 201, description: '标准创建成功' })
  async createStandard(@Body() standardSpec: any) {
    return this.ecosystemService.createIndustryStandard(standardSpec);
  }
}

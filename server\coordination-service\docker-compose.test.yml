# 群体协调服务测试环境 Docker Compose 配置

version: '3.8'

services:
  # Redis 测试服务
  redis:
    image: redis:7-alpine
    container_name: coordination-redis-test
    restart: unless-stopped
    ports:
      - "6380:6379"  # 使用不同端口避免冲突
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis-test-data:/data
    networks:
      - coordination-test-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5
      start_period: 10s

  # 测试数据库初始化（可选）
  redis-init:
    image: redis:7-alpine
    container_name: coordination-redis-init-test
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - coordination-test-network
    command: >
      sh -c "
        redis-cli -h redis -p 6379 FLUSHALL &&
        redis-cli -h redis -p 6379 SET test:init 'true' &&
        echo 'Redis 测试数据库初始化完成'
      "
    profiles:
      - init

networks:
  coordination-test-network:
    driver: bridge
    name: coordination-test-network

volumes:
  redis-test-data:
    driver: local
    name: coordination-redis-test-data

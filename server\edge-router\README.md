# 边缘路由服务 (Edge Router)

边缘路由服务是DL引擎生态系统中的智能路由决策微服务，负责为客户端选择最优的边缘节点，提供智能路由决策、流量分发和网络优化功能。

## 🚀 主要功能

### 核心功能
- **智能路由决策**: 基于多种策略为客户端选择最优边缘节点
- **网络质量监控**: 实时监控网络延迟、带宽、丢包率等指标
- **地理感知路由**: 基于地理位置的智能路由选择
- **负载均衡**: 多种负载均衡算法，确保资源合理分配
- **缓存优化**: 高性能缓存机制，提升路由决策速度

### 技术特性
- **微服务架构**: 基于NestJS的模块化设计
- **多层缓存**: 内存缓存 + Redis缓存
- **事件驱动**: 基于事件的松耦合架构
- **RESTful API**: 完整的HTTP API接口
- **微服务通信**: TCP消息模式支持
- **健康检查**: 多层次健康检查机制

## 📋 系统要求

- Node.js >= 18.0.0
- Redis >= 6.0 (可选，用于分布式缓存)
- TypeScript >= 5.0

## 🛠️ 安装和配置

### 1. 安装依赖
```bash
npm install
```

### 2. 环境配置
复制环境配置文件并根据需要修改：
```bash
cp .env.example .env
```

主要配置项：
```env
# 应用配置
PORT=3012
API_PREFIX=api

# 边缘注册中心配置
EDGE_REGISTRY_HOST=localhost
EDGE_REGISTRY_PORT=3011

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379

# 路由权重配置
ROUTING_WEIGHTS_GEOGRAPHIC=0.25
ROUTING_WEIGHTS_LATENCY=0.30
ROUTING_WEIGHTS_LOAD=0.25
ROUTING_WEIGHTS_RELIABILITY=0.20
```

### 3. 启动服务
```bash
# 开发模式
npm run start:dev

# 生产模式
npm run build
npm run start:prod
```

## 🔧 API文档

服务启动后，可以通过以下地址访问API文档：
- Swagger UI: `http://localhost:3012/api/docs`

### 主要API端点

#### 路由决策
- `POST /api/router/decide` - 智能路由决策
- `GET /api/router/network-quality/:nodeId` - 获取网络质量信息
- `GET /api/router/nodes` - 获取可用节点列表
- `GET /api/router/regions` - 获取可用区域列表

#### 统计和监控
- `GET /api/router/stats` - 获取路由统计信息
- `POST /api/router/cache/clear` - 清空缓存

#### 健康检查
- `GET /api/health` - 基础健康检查
- `GET /api/health/detailed` - 详细健康检查
- `GET /api/health/ready` - 就绪检查
- `GET /api/health/live` - 存活检查
- `GET /api/health/dependencies` - 依赖检查
- `GET /api/health/metrics` - 性能指标

## 🧭 路由策略

### 路由决策算法
服务采用多因素加权算法进行路由决策：

1. **地理距离权重 (25%)**: 基于客户端与节点的地理距离
2. **网络延迟权重 (30%)**: 基于实际网络延迟测量
3. **负载权重 (25%)**: 基于节点当前负载情况
4. **可靠性权重 (20%)**: 基于节点历史可靠性

### 缓存策略
- **位置缓存**: 1小时TTL
- **网络质量缓存**: 5分钟TTL
- **内存缓存**: 最大10000条记录
- **Redis缓存**: 分布式缓存支持

## 📊 监控和指标

### 路由指标
- 总路由决策数
- 成功/失败路由数
- 平均决策时间
- 按区域/节点的统计信息

### 性能指标
- 内存使用情况
- 缓存命中率
- 网络质量指标
- 服务运行时间

## 🔌 微服务通信

支持以下微服务消息模式：

- `router.decide` - 路由决策
- `router.get-nodes` - 获取可用节点
- `router.network-quality` - 网络质量测量
- `router.get-stats` - 获取路由统计
- `router.get-regions` - 获取可用区域
- `router.health-check` - 健康检查
- `router.clear-cache` - 清空缓存
- `router.batch-decide` - 批量路由决策

## 🏗️ 项目结构

```
src/
├── controllers/          # 控制器
│   ├── edge-router.controller.ts
│   ├── edge-router-microservice.controller.ts
│   └── health.controller.ts
├── services/            # 服务层
│   ├── intelligent-routing.service.ts
│   ├── routing-cache.service.ts
│   └── edge-registry-client.service.ts
├── dto/                 # 数据传输对象
│   ├── routing-request.dto.ts
│   └── routing-response.dto.ts
├── config/              # 配置文件
│   ├── redis.config.ts
│   └── routing.config.ts
├── edge-router/         # 业务模块
│   └── edge-router.module.ts
├── health/              # 健康检查模块
│   └── health.module.ts
├── app.module.ts        # 主模块
└── main.ts             # 启动文件
```

## 🧪 测试

```bash
# 单元测试
npm run test

# 端到端测试
npm run test:e2e

# 测试覆盖率
npm run test:cov
```

## 📦 部署

### Docker部署
```bash
# 构建镜像
docker build -t edge-router .

# 运行容器
docker run -p 3012:3012 edge-router
```

### 环境变量
生产环境建议设置以下环境变量：
- `NODE_ENV=production`
- `REDIS_ENABLED=true`
- `NETWORK_MONITORING_ENABLED=true`

## 🤝 集成示例

### 路由决策请求
```javascript
const routingRequest = {
  clientInfo: {
    clientId: 'client-12345',
    ipAddress: '*************',
    location: {
      latitude: 39.9042,
      longitude: 116.4074,
      city: '北京',
      country: '中国'
    },
    connectionType: 'wifi',
    deviceType: 'desktop'
  },
  requirements: {
    maxLatency: 100,
    minBandwidth: 10,
    preferredRegion: 'beijing-zone-1',
    requiredFeatures: ['webrtc', 'ai-inference']
  }
};

const response = await fetch('/api/router/decide', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(routingRequest)
});
```

### 网络质量查询
```javascript
const response = await fetch('/api/router/network-quality/edge-node-001?clientIp=*************');
const quality = await response.json();
```

## 🔗 依赖服务

- **边缘注册中心 (Edge Registry)**: 提供边缘节点信息
- **Redis (可选)**: 分布式缓存支持

## 📝 许可证

MIT License

import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { EdgeDevice, DeviceStatus } from './entities/edge-device.entity';

@Injectable()
export class DeviceService {
  private readonly logger = new Logger(DeviceService.name);
  private readonly mockDevices: EdgeDevice[] = [];

  constructor() {
    // 初始化模拟数据
    this.initializeMockData();
  }

  private initializeMockData() {
    this.mockDevices.push(
      {
        id: 'device-001',
        deviceId: 'edge_001',
        name: '工业控制器-001',
        type: 'industrial_pc',
        location: '生产车间A',
        status: 'online',
        capabilities: { cpu: { cores: 4, frequency: 2400 }, memory: { total: 8192 } },
        performance: { cpuUsage: 25.5, memoryUsage: 35.2 },
        networkInfo: { ipAddress: '*************', bandwidth: 1000 },
        deployedModels: [],
        lastHeartbeat: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      } as EdgeDevice,
      {
        id: 'device-002',
        deviceId: 'edge_002',
        name: '智能传感器-002',
        type: 'smart_sensor',
        location: '仓库B区',
        status: 'online',
        capabilities: { cpu: { cores: 2, frequency: 1800 }, memory: { total: 4096 } },
        performance: { cpuUsage: 15.2, memoryUsage: 28.7 },
        networkInfo: { ipAddress: '*************', bandwidth: 100 },
        deployedModels: [],
        lastHeartbeat: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      } as EdgeDevice
    );
  }

  async create(deviceData: Partial<EdgeDevice>): Promise<EdgeDevice> {
    try {
      const newDevice = {
        id: `device-${Date.now()}`,
        deviceId: deviceData.deviceId || `mock-device-${Date.now()}`,
        name: deviceData.name || '模拟设备',
        type: deviceData.type || 'industrial_pc',
        location: deviceData.location || '模拟位置',
        status: deviceData.status || 'online',
        capabilities: deviceData.capabilities || {},
        performance: deviceData.performance || {},
        networkInfo: deviceData.networkInfo || {},
        deployedModels: deviceData.deployedModels || [],
        lastHeartbeat: new Date(),
        description: deviceData.description,
        metadata: deviceData.metadata,
        createdAt: new Date(),
        updatedAt: new Date(),
      } as EdgeDevice;
      
      this.mockDevices.push(newDevice);
      this.logger.log(`设备创建成功: ${newDevice.deviceId}`);
      return newDevice;
    } catch (error) {
      this.logger.error('创建设备失败', error);
      throw error;
    }
  }

  async findAll(filters?: {
    status?: DeviceStatus;
    type?: string;
    location?: string;
  }): Promise<EdgeDevice[]> {
    try {
      // 应用过滤器
      let filteredDevices = [...this.mockDevices];
      if (filters?.status) {
        filteredDevices = filteredDevices.filter(d => d.status === filters.status);
      }
      if (filters?.type) {
        filteredDevices = filteredDevices.filter(d => d.type === filters.type);
      }
      if (filters?.location) {
        filteredDevices = filteredDevices.filter(d => d.location.includes(filters.location));
      }

      return filteredDevices;
    } catch (error) {
      this.logger.error('查询设备列表失败', error);
      throw error;
    }
  }

  async findOne(id: string): Promise<EdgeDevice> {
    try {
      const device = this.mockDevices.find(d => d.id === id);
      
      if (!device) {
        throw new NotFoundException(`设备不存在: ${id}`);
      }

      return device;
    } catch (error) {
      this.logger.error('查询设备失败', error);
      throw error;
    }
  }

  async findByDeviceId(deviceId: string): Promise<EdgeDevice> {
    try {
      const device = this.mockDevices.find(d => d.deviceId === deviceId);
      
      if (!device) {
        throw new NotFoundException(`设备不存在: ${deviceId}`);
      }

      return device;
    } catch (error) {
      this.logger.error('查询设备失败', error);
      throw error;
    }
  }

  async update(id: string, updateData: Partial<EdgeDevice>): Promise<EdgeDevice> {
    try {
      const deviceIndex = this.mockDevices.findIndex(d => d.id === id);
      
      if (deviceIndex === -1) {
        throw new NotFoundException(`设备不存在: ${id}`);
      }

      this.mockDevices[deviceIndex] = {
        ...this.mockDevices[deviceIndex],
        ...updateData,
        updatedAt: new Date(),
      };

      this.logger.log(`设备更新成功: ${id}`);
      return this.mockDevices[deviceIndex];
    } catch (error) {
      this.logger.error('更新设备失败', error);
      throw error;
    }
  }

  async updateStatus(deviceId: string, status: DeviceStatus): Promise<void> {
    try {
      const deviceIndex = this.mockDevices.findIndex(d => d.deviceId === deviceId);
      
      if (deviceIndex !== -1) {
        this.mockDevices[deviceIndex].status = status;
        this.mockDevices[deviceIndex].updatedAt = new Date();
        this.logger.log(`设备状态更新成功: ${deviceId} -> ${status}`);
      }
    } catch (error) {
      this.logger.error('更新设备状态失败', error);
      throw error;
    }
  }

  async updateHeartbeat(deviceId: string): Promise<void> {
    try {
      const deviceIndex = this.mockDevices.findIndex(d => d.deviceId === deviceId);
      
      if (deviceIndex !== -1) {
        this.mockDevices[deviceIndex].lastHeartbeat = new Date();
        this.logger.debug(`设备心跳更新: ${deviceId}`);
      }
    } catch (error) {
      this.logger.error('更新设备心跳失败', error);
      throw error;
    }
  }

  async remove(id: string): Promise<void> {
    try {
      const deviceIndex = this.mockDevices.findIndex(d => d.id === id);
      
      if (deviceIndex === -1) {
        throw new NotFoundException(`设备不存在: ${id}`);
      }

      this.mockDevices.splice(deviceIndex, 1);
      this.logger.log(`设备删除成功: ${id}`);
    } catch (error) {
      this.logger.error('删除设备失败', error);
      throw error;
    }
  }

  async getStatistics(): Promise<any> {
    try {
      const total = this.mockDevices.length;
      const online = this.mockDevices.filter(d => d.status === 'online').length;
      const offline = this.mockDevices.filter(d => d.status === 'offline').length;
      const maintenance = this.mockDevices.filter(d => d.status === 'maintenance').length;

      return {
        total,
        online,
        offline,
        maintenance,
        typeStats: {},
        locationStats: {},
      };
    } catch (error) {
      this.logger.error('获取设备统计失败', error);
      throw error;
    }
  }

  async checkOfflineDevices(): Promise<void> {
    try {
      const now = new Date();
      const offlineThreshold = 5 * 60 * 1000; // 5分钟

      this.mockDevices.forEach(device => {
        if (device.lastHeartbeat && 
            (now.getTime() - device.lastHeartbeat.getTime()) > offlineThreshold &&
            device.status === 'online') {
          device.status = 'offline' as DeviceStatus;
          device.updatedAt = new Date();
          this.logger.warn(`设备离线: ${device.deviceId}`);
        }
      });
    } catch (error) {
      this.logger.error('检查离线设备失败', error);
    }
  }
}

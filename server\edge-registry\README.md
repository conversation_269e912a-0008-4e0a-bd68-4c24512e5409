# 边缘注册中心 (Edge Registry)

边缘注册中心是DL引擎生态系统中的核心微服务，负责边缘节点的注册、发现、负载均衡和健康监控。

## 🚀 主要功能

### 核心功能
- **节点注册与发现**: 边缘节点的注册、注销和查询
- **负载均衡**: 多种负载均衡策略，智能选择最优节点
- **健康监控**: 实时监控节点状态，自动故障检测
- **地理感知**: 基于地理位置的节点选择和路由
- **数据持久化**: 节点信息的数据库存储和缓存

### 技术特性
- **微服务架构**: 基于NestJS的模块化设计
- **双重存储**: 内存缓存 + 数据库持久化
- **事件驱动**: 基于事件的松耦合架构
- **RESTful API**: 完整的HTTP API接口
- **微服务通信**: TCP消息模式支持
- **健康检查**: 多层次健康检查机制

## 📋 系统要求

- Node.js >= 18.0.0
- MySQL >= 8.0
- Redis >= 6.0 (可选)
- TypeScript >= 5.0

## 🛠️ 安装和配置

### 1. 安装依赖
```bash
npm install
```

### 2. 环境配置
复制环境配置文件并根据需要修改：
```bash
cp .env.example .env
```

主要配置项：
```env
# 应用配置
PORT=3011
API_PREFIX=api

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=password
DB_DATABASE=dl_edge_registry

# 微服务配置
MICROSERVICE_PORT=3011
```

### 3. 数据库初始化
确保MySQL服务正在运行，应用启动时会自动创建表结构。

### 4. 启动服务
```bash
# 开发模式
npm run start:dev

# 生产模式
npm run build
npm run start:prod
```

## 🔧 API文档

服务启动后，可以通过以下地址访问API文档：
- Swagger UI: `http://localhost:3011/api/docs`

### 主要API端点

#### 节点管理
- `POST /api/edge/register` - 注册边缘节点
- `DELETE /api/edge/:nodeId` - 注销边缘节点
- `PUT /api/edge/:nodeId/heartbeat` - 更新节点心跳
- `GET /api/edge/nodes` - 获取所有节点
- `GET /api/edge/nodes/:nodeId` - 获取单个节点信息

#### 负载均衡
- `POST /api/edge/select-optimal` - 选择最优节点

#### 统计信息
- `GET /api/edge/stats` - 获取集群统计信息
- `GET /api/edge/regions` - 获取可用区域列表

#### 健康检查
- `GET /api/health` - 基础健康检查
- `GET /api/health/detailed` - 详细健康检查
- `GET /api/health/ready` - 就绪检查
- `GET /api/health/live` - 存活检查

## 🔄 负载均衡策略

支持多种负载均衡策略：

1. **轮询 (Round Robin)**: 按顺序轮流分配请求
2. **最少连接 (Least Connections)**: 选择当前连接数最少的节点
3. **地理就近 (Geographic Proximity)**: 基于地理位置选择最近的节点
4. **资源优先 (Resource Based)**: 基于CPU和内存使用率选择
5. **混合策略 (Hybrid)**: 综合考虑地理位置、资源使用率和连接数

## 📊 监控和指标

### 节点指标
- 当前用户数
- CPU使用率
- 内存使用率
- 网络延迟
- 运行时间

### 集群统计
- 总节点数
- 在线/离线节点数
- 过载节点数
- 总用户数
- 平均资源使用率

## 🔌 微服务通信

支持以下微服务消息模式：

- `edge.register` - 节点注册
- `edge.unregister` - 节点注销
- `edge.heartbeat` - 心跳更新
- `edge.select-optimal` - 选择最优节点
- `edge.get-all-nodes` - 获取所有节点
- `edge.get-node` - 获取单个节点
- `edge.get-stats` - 获取集群统计
- `edge.health-check` - 健康检查

## 🏗️ 项目结构

```
src/
├── controllers/          # 控制器
│   ├── edge-registry.controller.ts
│   ├── edge-registry-microservice.controller.ts
│   └── health.controller.ts
├── services/            # 服务层
│   ├── edge-node-manager.service.ts
│   └── edge-registry.service.ts
├── entities/            # 数据库实体
│   └── edge-node.entity.ts
├── dto/                 # 数据传输对象
│   ├── register-node.dto.ts
│   ├── update-node.dto.ts
│   ├── select-node.dto.ts
│   └── response.dto.ts
├── config/              # 配置文件
│   ├── database.config.ts
│   └── redis.config.ts
├── edge-registry/       # 业务模块
│   └── edge-registry.module.ts
├── health/              # 健康检查模块
│   └── health.module.ts
├── app.module.ts        # 主模块
└── main.ts             # 启动文件
```

## 🧪 测试

```bash
# 单元测试
npm run test

# 端到端测试
npm run test:e2e

# 测试覆盖率
npm run test:cov
```

## 📦 部署

### Docker部署
```bash
# 构建镜像
docker build -t edge-registry .

# 运行容器
docker run -p 3011:3011 edge-registry
```

### 环境变量
生产环境建议设置以下环境变量：
- `NODE_ENV=production`
- `DB_SYNCHRONIZE=false`
- `DB_LOGGING=false`

## 🤝 集成示例

### 注册边缘节点
```javascript
const nodeData = {
  nodeId: 'edge-node-001',
  region: 'beijing-zone-1',
  endpoint: 'http://*************:8080',
  capabilities: {
    maxUsers: 100,
    supportedFeatures: ['webrtc', 'ai-inference'],
    resources: {
      cpu: '2000m',
      memory: '4Gi',
      storage: '20Gi'
    }
  },
  location: {
    latitude: 39.9042,
    longitude: 116.4074,
    city: '北京',
    country: '中国'
  }
};

const response = await fetch('/api/edge/register', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(nodeData)
});
```

### 选择最优节点
```javascript
const selectData = {
  strategy: 'hybrid',
  region: 'beijing-zone-1',
  clientLocation: {
    latitude: 39.9042,
    longitude: 116.4074
  }
};

const response = await fetch('/api/edge/select-optimal', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify(selectData)
});
```

## 📝 许可证

MIT License

import { Injectable, CanActivate, ExecutionContext, HttpException, HttpStatus } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { CacheService } from '../redis/cache.service';
import { RATE_LIMIT_KEY } from '../decorators/rate-limit.decorator';

export interface RateLimitOptions {
  requests: number;
  window: number; // 时间窗口（秒）
  message?: string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

@Injectable()
export class RateLimitGuard implements CanActivate {
  constructor(
    private reflector: Reflector,
    private cacheService: CacheService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const rateLimitOptions = this.reflector.getAllAndOverride<RateLimitOptions>(RATE_LIMIT_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!rateLimitOptions) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const identifier = this.getIdentifier(request);
    const key = this.cacheService.getRateLimitKey(identifier, rateLimitOptions.window.toString());

    const current = await this.cacheService.get<number>(key) || 0;

    if (current >= rateLimitOptions.requests) {
      const ttl = await this.cacheService.ttl(key);
      throw new HttpException(
        {
          statusCode: HttpStatus.TOO_MANY_REQUESTS,
          message: rateLimitOptions.message || 'Too many requests',
          retryAfter: ttl > 0 ? ttl : rateLimitOptions.window,
        },
        HttpStatus.TOO_MANY_REQUESTS,
      );
    }

    // 增加计数器
    if (current === 0) {
      await this.cacheService.set(key, 1, rateLimitOptions.window);
    } else {
      await this.cacheService.incr(key);
    }

    // 设置响应头
    const response = context.switchToHttp().getResponse();
    response.setHeader('X-RateLimit-Limit', rateLimitOptions.requests);
    response.setHeader('X-RateLimit-Remaining', Math.max(0, rateLimitOptions.requests - current - 1));
    response.setHeader('X-RateLimit-Reset', new Date(Date.now() + rateLimitOptions.window * 1000).toISOString());

    return true;
  }

  private getIdentifier(request: any): string {
    // 优先使用用户ID
    if (request.user && request.user.id) {
      return `user:${request.user.id}`;
    }

    // 使用API密钥ID
    if (request.apiKeyInfo && request.apiKeyInfo.keyId) {
      return `apikey:${request.apiKeyInfo.keyId}`;
    }

    // 使用IP地址
    const ip = request.ip || request.connection.remoteAddress || request.socket.remoteAddress;
    return `ip:${ip}`;
  }
}

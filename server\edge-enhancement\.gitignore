# 依赖
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
lib-cov
coverage/
*.lcov
.nyc_output

# 构建输出
dist/
build/
*.tsbuildinfo

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs/
*.log

# 运行时目录
tmp/
temp/

# 编辑器和IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker
.dockerignore

# 测试
.jest/
test-results/
coverage/

# 缓存
.cache/
.parcel-cache/

# 数据库
*.sqlite
*.sqlite3
*.db

# 备份文件
*.bak
*.backup

# 压缩文件
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# 证书和密钥
*.pem
*.key
*.crt
*.csr
ssl/

# 监控数据
prometheus-data/
grafana-data/

# 本地数据
data/
cache/
uploads/

import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { ApiSpecification } from './api-specification.entity';

@Entity('api_usage_records')
@Index(['apiId', 'date'])
@Index(['apiId', 'userId'])
export class ApiUsageRecord {
  @PrimaryGeneratedColumn('uuid')
  recordId: string;

  @Column('uuid')
  @Index()
  apiId: string;

  @Column({ length: 255, nullable: true })
  @Index()
  userId: string; // 调用用户ID

  @Column({ length: 255, nullable: true })
  clientId: string; // 客户端ID

  @Column({ length: 500 })
  endpoint: string;

  @Column({ length: 10 })
  method: string;

  @Column({ type: 'int' })
  statusCode: number;

  @Column({ type: 'int' })
  responseTime: number; // 毫秒

  @Column({ type: 'bigint', nullable: true })
  requestSize: number; // 字节

  @Column({ type: 'bigint', nullable: true })
  responseSize: number; // 字节

  @Column({ length: 45, nullable: true })
  clientIp: string;

  @Column({ length: 500, nullable: true })
  userAgent: string;

  @Column('text', { nullable: true })
  errorMessage: string;

  @Column({ type: 'date' })
  @Index()
  date: Date;

  @CreateDateColumn()
  createdAt: Date;

  // 关联关系
  @ManyToOne(() => ApiSpecification, api => api.usageRecords, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'apiId' })
  apiSpecification: ApiSpecification;
}

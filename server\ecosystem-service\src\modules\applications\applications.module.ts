import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApplicationsController } from './applications.controller';
import { ApplicationsService } from './applications.service';
import { ThirdPartyApplication, ApplicationReview } from '../../entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([ThirdPartyApplication, ApplicationReview]),
  ],
  controllers: [ApplicationsController],
  providers: [ApplicationsService],
  exports: [ApplicationsService],
})
export class ApplicationsModule {}

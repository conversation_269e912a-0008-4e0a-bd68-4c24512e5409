@echo off
chcp 65001 >nul

REM 边缘AI服务开发环境启动脚本（简化版）
REM 此脚本可以在没有数据库和Redis的情况下启动服务

echo 🚀 启动边缘AI计算服务（开发模式）

REM 检查Node.js版本
for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo 📦 Node.js版本: %NODE_VERSION%

REM 检查npm版本
for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
echo 📦 npm版本: %NPM_VERSION%

REM 设置环境变量（开发模式）
set NODE_ENV=development
set PORT=3001
set LOG_LEVEL=debug

REM 数据库配置（可选）
set DB_HOST=localhost
set DB_PORT=3306
set DB_USERNAME=root
set DB_PASSWORD=
set DB_DATABASE=edge_ai_service

REM Redis配置（可选）
set REDIS_HOST=localhost
set REDIS_PORT=6379

REM 跨域配置
set ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001,http://localhost:8080

echo 🔧 环境变量已设置
echo    - NODE_ENV: %NODE_ENV%
echo    - PORT: %PORT%
echo    - 数据库: %DB_HOST%:%DB_PORT%/%DB_DATABASE%
echo    - Redis: %REDIS_HOST%:%REDIS_PORT%

REM 启动服务
echo 🎯 启动服务...
npm run start:dev

echo ✅ 服务启动完成
echo 📖 API文档: http://localhost:%PORT%/api/docs
echo 🔗 服务地址: http://localhost:%PORT%/api/v1

pause

import { Test, TestingModule } from '@nestjs/testing';
import { CacheService } from './cache.service';
import { RedisService } from './redis.service';
import { createMockRedisService } from '../../test-utils/test-helpers';

describe('CacheService', () => {
  let service: CacheService;
  let redisService: jest.Mocked<RedisService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CacheService,
        {
          provide: RedisService,
          useValue: createMockRedisService(),
        },
      ],
    }).compile();

    service = module.get<CacheService>(CacheService);
    redisService = module.get(RedisService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('get', () => {
    it('should get and parse cached data', async () => {
      const key = 'test-key';
      const data = { test: 'data' };
      const serializedData = JSON.stringify(data);

      redisService.get.mockResolvedValue(serializedData);

      const result = await service.get(key);

      expect(redisService.get).toHaveBeenCalledWith(key);
      expect(result).toEqual(data);
    });

    it('should return null when no data found', async () => {
      const key = 'test-key';

      redisService.get.mockResolvedValue(null);

      const result = await service.get(key);

      expect(result).toBeNull();
    });

    it('should return null when JSON parsing fails', async () => {
      const key = 'test-key';

      redisService.get.mockResolvedValue('invalid-json');

      const result = await service.get(key);

      expect(result).toBeNull();
    });
  });

  describe('set', () => {
    it('should serialize and cache data', async () => {
      const key = 'test-key';
      const data = { test: 'data' };
      const ttl = 3600;
      const serializedData = JSON.stringify(data);

      redisService.set.mockResolvedValue(true);

      const result = await service.set(key, data, ttl);

      expect(redisService.set).toHaveBeenCalledWith(key, serializedData, ttl);
      expect(result).toBe(true);
    });

    it('should use default TTL when not specified', async () => {
      const key = 'test-key';
      const data = { test: 'data' };
      const serializedData = JSON.stringify(data);

      redisService.set.mockResolvedValue(true);

      await service.set(key, data);

      expect(redisService.set).toHaveBeenCalledWith(key, serializedData, 3600);
    });

    it('should return false when serialization fails', async () => {
      const key = 'test-key';
      const circularData = {};
      (circularData as any).self = circularData; // Create circular reference

      const result = await service.set(key, circularData);

      expect(result).toBe(false);
    });
  });

  describe('del', () => {
    it('should delete cached data', async () => {
      const key = 'test-key';

      redisService.del.mockResolvedValue(true);

      const result = await service.del(key);

      expect(redisService.del).toHaveBeenCalledWith(key);
      expect(result).toBe(true);
    });
  });

  describe('exists', () => {
    it('should check if key exists', async () => {
      const key = 'test-key';

      redisService.exists.mockResolvedValue(true);

      const result = await service.exists(key);

      expect(redisService.exists).toHaveBeenCalledWith(key);
      expect(result).toBe(true);
    });
  });

  describe('getOrSet', () => {
    it('should return cached data if available', async () => {
      const key = 'test-key';
      const cachedData = { test: 'cached' };
      const callback = jest.fn();

      redisService.get.mockResolvedValue(JSON.stringify(cachedData));

      const result = await service.getOrSet(key, callback);

      expect(result).toEqual(cachedData);
      expect(callback).not.toHaveBeenCalled();
    });

    it('should execute callback and cache result if not cached', async () => {
      const key = 'test-key';
      const callbackData = { test: 'callback' };
      const callback = jest.fn().mockResolvedValue(callbackData);
      const ttl = 1800;

      redisService.get.mockResolvedValue(null);
      redisService.set.mockResolvedValue(true);

      const result = await service.getOrSet(key, callback, ttl);

      expect(callback).toHaveBeenCalled();
      expect(redisService.set).toHaveBeenCalledWith(key, JSON.stringify(callbackData), ttl);
      expect(result).toEqual(callbackData);
    });

    it('should not cache null or undefined values', async () => {
      const key = 'test-key';
      const callback = jest.fn().mockResolvedValue(null);

      redisService.get.mockResolvedValue(null);

      const result = await service.getOrSet(key, callback);

      expect(result).toBeNull();
      expect(redisService.set).not.toHaveBeenCalled();
    });

    it('should execute callback if cache operation fails', async () => {
      const key = 'test-key';
      const callbackData = { test: 'callback' };
      const callback = jest.fn().mockResolvedValue(callbackData);

      redisService.get.mockRejectedValue(new Error('Cache error'));

      const result = await service.getOrSet(key, callback);

      expect(callback).toHaveBeenCalled();
      expect(result).toEqual(callbackData);
    });
  });

  describe('delPattern', () => {
    it('should delete keys matching pattern', async () => {
      const pattern = 'test:*';
      const keys = ['test:1', 'test:2', 'test:3'];

      redisService.keys.mockResolvedValue(keys);
      redisService.del.mockResolvedValue(true);

      const result = await service.delPattern(pattern);

      expect(redisService.keys).toHaveBeenCalledWith(pattern);
      expect(redisService.del).toHaveBeenCalledTimes(3);
      expect(result).toBe(3);
    });

    it('should return 0 when no keys match pattern', async () => {
      const pattern = 'test:*';

      redisService.keys.mockResolvedValue([]);

      const result = await service.delPattern(pattern);

      expect(result).toBe(0);
      expect(redisService.del).not.toHaveBeenCalled();
    });
  });

  describe('generateKey', () => {
    it('should generate cache key with prefix and parts', () => {
      const result = service.generateKey('test', 'part1', 'part2');

      expect(result).toBe('ecosystem:test:part1:part2');
    });

    it('should handle numeric parts', () => {
      const result = service.generateKey('test', 123, 'part2');

      expect(result).toBe('ecosystem:test:123:part2');
    });
  });

  describe('specific key generators', () => {
    it('should generate partner key', () => {
      const partnerId = 'partner-123';
      const result = service.getPartnerKey(partnerId);

      expect(result).toBe('ecosystem:partner:partner-123');
    });

    it('should generate partners list key', () => {
      const filters = { type: 'tech', status: 'active' };
      const result = service.getPartnersListKey(filters);

      expect(result).toBe('ecosystem:partners:list:status:active|type:tech');
    });

    it('should generate partners list key for empty filters', () => {
      const result = service.getPartnersListKey({});

      expect(result).toBe('ecosystem:partners:list:all');
    });

    it('should generate API key', () => {
      const apiId = 'api-123';
      const result = service.getApiKey(apiId);

      expect(result).toBe('ecosystem:api:api-123');
    });

    it('should generate rate limit key', () => {
      const identifier = 'user:123';
      const window = '60';
      const result = service.getRateLimitKey(identifier, window);

      expect(result).toBe('ecosystem:ratelimit:user:123:60');
    });
  });

  describe('cache clearing methods', () => {
    it('should clear all cache', async () => {
      redisService.keys.mockResolvedValue(['key1', 'key2']);
      redisService.del.mockResolvedValue(true);

      const result = await service.clearAll();

      expect(redisService.keys).toHaveBeenCalledWith('ecosystem:*');
      expect(result).toBe(2);
    });

    it('should clear partner cache', async () => {
      redisService.keys.mockResolvedValue(['ecosystem:partner:1']);
      redisService.del.mockResolvedValue(true);

      const result = await service.clearPartnerCache();

      expect(redisService.keys).toHaveBeenCalledWith('ecosystem:partner:*');
      expect(result).toBe(1);
    });
  });
});

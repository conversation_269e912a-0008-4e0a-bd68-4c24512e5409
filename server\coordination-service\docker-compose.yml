# 群体协调服务 Docker Compose 配置

version: '3.8'

services:
  # 群体协调服务
  coordination-service:
    build:
      context: .
      dockerfile: Dockerfile
      target: runner
    container_name: coordination-service
    restart: unless-stopped
    ports:
      - "3010:3010"
      - "3020:3020"  # 微服务端口
    environment:
      - NODE_ENV=production
      - PORT=3010
      - HOST=0.0.0.0
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - MICROSERVICE_HOST=0.0.0.0
      - MICROSERVICE_PORT=3020
      - CORS_ORIGIN=*
      - DEBUG=false
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - coordination-network
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "node", "-e", "require('http').get('http://localhost:3010/api/v1/coordination/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis 服务
  redis:
    image: redis:7-alpine
    container_name: coordination-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis-data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - coordination-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Redis Commander (可选的Redis管理界面)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: coordination-redis-commander
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
      - HTTP_USER=admin
      - HTTP_PASSWORD=admin123
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - coordination-network
    profiles:
      - tools

  # 监控服务 (可选)
  prometheus:
    image: prom/prometheus:latest
    container_name: coordination-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - coordination-network
    profiles:
      - monitoring

  # Grafana 监控面板 (可选)
  grafana:
    image: grafana/grafana:latest
    container_name: coordination-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin123
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
    depends_on:
      - prometheus
    networks:
      - coordination-network
    profiles:
      - monitoring

networks:
  coordination-network:
    driver: bridge
    name: coordination-network

volumes:
  redis-data:
    driver: local
    name: coordination-redis-data
  prometheus-data:
    driver: local
    name: coordination-prometheus-data
  grafana-data:
    driver: local
    name: coordination-grafana-data

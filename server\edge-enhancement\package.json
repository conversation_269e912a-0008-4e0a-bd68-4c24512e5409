{"name": "edge-enhancement-service", "version": "1.0.0", "description": "边缘计算增强服务 - 提供智能调度、预测性缓存和自适应网络传输功能", "author": "DL Engine Team", "private": true, "license": "MIT", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "lint:check": "eslint \"{src,apps,libs,test}/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "clean": "rimraf dist node_modules package-lock.json", "clean:install": "npm run clean && npm install --legacy-peer-deps", "docker:build": "docker build -t edge-enhancement-service .", "docker:run": "docker run -p 3040:3040 edge-enhancement-service", "docker:compose": "docker-compose up -d", "docker:compose:dev": "docker-compose -f docker-compose.dev.yml up -d", "docker:down": "docker-compose down", "migration:generate": "typeorm migration:generate", "migration:run": "typeorm migration:run", "migration:revert": "typeorm migration:revert"}, "dependencies": {"@nestjs/common": "^10.3.0", "@nestjs/core": "^10.3.0", "@nestjs/platform-express": "^10.3.0", "@nestjs/config": "^3.1.1", "@nestjs/swagger": "^7.1.17", "@nestjs/typeorm": "^10.0.1", "@nestjs/event-emitter": "^2.0.3", "@nestjs/schedule": "^4.0.0", "@nestjs/passport": "^10.0.2", "@nestjs/jwt": "^10.2.0", "@nestjs/throttler": "^5.1.1", "typeorm": "^0.3.17", "pg": "^8.11.3", "redis": "^4.6.12", "ioredis": "^5.3.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "bcrypt": "^5.1.1", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "reflect-metadata": "^0.1.14", "rxjs": "^7.8.1", "uuid": "^9.0.1", "dayjs": "^1.11.10", "lodash": "^4.17.21", "compression": "^1.7.4", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "prom-client": "^15.1.0", "swagger-ui-express": "^5.0.0", "multer": "^1.4.5-lts.1", "sharp": "^0.33.1", "node-cron": "^3.0.3"}, "devDependencies": {"@nestjs/cli": "^10.3.0", "@nestjs/schematics": "^10.1.0", "@nestjs/testing": "^10.3.0", "@types/express": "^4.17.21", "@types/jest": "^29.5.11", "@types/node": "^20.10.6", "@types/supertest": "^6.0.2", "@types/bcrypt": "^5.0.2", "@types/uuid": "^9.0.7", "@types/lodash": "^4.14.202", "@types/compression": "^1.7.5", "@types/multer": "^1.4.11", "@types/node-cron": "^3.0.11", "@typescript-eslint/eslint-plugin": "^6.18.1", "@typescript-eslint/parser": "^6.18.1", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.2", "jest": "^29.7.0", "prettier": "^3.1.1", "rimraf": "^5.0.5", "source-map-support": "^0.5.21", "supertest": "^6.3.4", "ts-jest": "^29.1.1", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["edge-computing", "intelligent-scheduling", "predictive-cache", "adaptive-network", "<PERSON><PERSON><PERSON>", "microservice", "dl-engine"]}
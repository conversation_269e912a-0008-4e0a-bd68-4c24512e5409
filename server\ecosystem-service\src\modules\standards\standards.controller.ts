import { Controller, Get, Post, Put, Delete, Body, Param, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { StandardsService } from './standards.service';

@ApiTags('standards')
@Controller('standards')
export class StandardsController {
  constructor(private readonly standardsService: StandardsService) {}

  @Get()
  @ApiOperation({ summary: '获取行业标准列表' })
  @ApiResponse({ status: 200, description: '标准列表' })
  @ApiQuery({ name: 'organization', required: false, description: '标准组织' })
  @ApiQuery({ name: 'status', required: false, description: '标准状态' })
  async getStandards(
    @Query('organization') organization?: string,
    @Query('status') status?: string,
  ) {
    return this.standardsService.getStandards({ organization, status });
  }

  @Get(':id')
  @ApiOperation({ summary: '获取标准详情' })
  @ApiResponse({ status: 200, description: '标准详情' })
  @ApiParam({ name: 'id', description: '标准ID' })
  async getStandard(@Param('id') id: string) {
    return this.standardsService.getStandard(id);
  }

  @Post()
  @ApiOperation({ summary: '创建行业标准' })
  @ApiResponse({ status: 201, description: '标准创建成功' })
  async createStandard(@Body() standard: any) {
    return this.standardsService.createStandard(standard);
  }

  @Put(':id')
  @ApiOperation({ summary: '更新标准信息' })
  @ApiResponse({ status: 200, description: '标准更新成功' })
  @ApiParam({ name: 'id', description: '标准ID' })
  async updateStandard(@Param('id') id: string, @Body() updateData: any) {
    return this.standardsService.updateStandard(id, updateData);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除标准' })
  @ApiResponse({ status: 200, description: '标准删除成功' })
  @ApiParam({ name: 'id', description: '标准ID' })
  async deleteStandard(@Param('id') id: string) {
    return this.standardsService.deleteStandard(id);
  }

  @Get(':id/compliance')
  @ApiOperation({ summary: '获取标准合规信息' })
  @ApiResponse({ status: 200, description: '合规信息' })
  @ApiParam({ name: 'id', description: '标准ID' })
  async getStandardCompliance(@Param('id') id: string) {
    return this.standardsService.getStandardCompliance(id);
  }

  @Post(':id/certification')
  @ApiOperation({ summary: '申请标准认证' })
  @ApiResponse({ status: 201, description: '认证申请成功' })
  @ApiParam({ name: 'id', description: '标准ID' })
  async applyCertification(@Param('id') id: string, @Body() application: any) {
    return this.standardsService.applyCertification(id, application);
  }

  @Get(':id/requirements')
  @ApiOperation({ summary: '获取标准要求' })
  @ApiResponse({ status: 200, description: '标准要求列表' })
  @ApiParam({ name: 'id', description: '标准ID' })
  async getStandardRequirements(@Param('id') id: string) {
    return this.standardsService.getStandardRequirements(id);
  }
}

import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { ThirdPartyApplication } from './third-party-application.entity';

@Entity('application_reviews')
@Index(['appId', 'rating'])
@Index(['appId', 'userId'])
export class ApplicationReview {
  @PrimaryGeneratedColumn('uuid')
  reviewId: string;

  @Column('uuid')
  @Index()
  appId: string;

  @Column({ length: 255 })
  @Index()
  userId: string;

  @Column({ type: 'tinyint', unsigned: true })
  @Index()
  rating: number; // 1-5

  @Column({ length: 255, nullable: true })
  title: string;

  @Column('text', { nullable: true })
  comment: string;

  @Column({ type: 'int', default: 0 })
  helpful_votes: number;

  @Column({ type: 'boolean', default: true })
  isVisible: boolean;

  @Column({ length: 50, nullable: true })
  version: string; // 评价时的应用版本

  @Column('json', { nullable: true })
  metadata: {
    platform?: string;
    device?: string;
    os_version?: string;
    app_version?: string;
  };

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  // 关联关系
  @ManyToOne(() => ThirdPartyApplication, app => app.reviews, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'appId' })
  application: ThirdPartyApplication;
}

import { NestFactory } from '@nestjs/core';
import { <PERSON><PERSON><PERSON>, Controller, Get, Injectable } from '@nestjs/common';

@Injectable()
export class SimpleService {
  getHello(): string {
    return 'Hello from Edge AI Service!';
  }

  getStatus(): any {
    return {
      status: 'running',
      timestamp: new Date().toISOString(),
      service: 'edge-ai-service',
      version: '1.0.0'
    };
  }
}

@Controller()
export class SimpleController {
  constructor(private readonly simpleService: SimpleService) {}

  @Get()
  getHello(): string {
    return this.simpleService.getHello();
  }

  @Get('status')
  getStatus(): any {
    return this.simpleService.getStatus();
  }

  @Get('health')
  getHealth(): any {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
    };
  }
}

@Module({
  imports: [],
  controllers: [SimpleController],
  providers: [SimpleService],
})
export class SimpleAppModule {}

async function bootstrap() {
  console.log('🚀 启动简单测试服务...');
  
  try {
    const app = await NestFactory.create(SimpleAppModule);
    
    // 全局前缀
    app.setGlobalPrefix('api/v1');
    
    const port = process.env.PORT || 3001;
    await app.listen(port);

    console.log(`✅ 简单测试服务已启动`);
    console.log(`🔗 服务地址: http://localhost:${port}/api/v1`);
    console.log(`📊 状态检查: http://localhost:${port}/api/v1/status`);
    console.log(`💚 健康检查: http://localhost:${port}/api/v1/health`);
  } catch (error) {
    console.error('❌ 启动失败:', error);
    process.exit(1);
  }
}

bootstrap();

import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from './redis.service';

@Injectable()
export class CacheService {
  private readonly logger = new Logger(CacheService.name);
  private readonly defaultTTL = 3600; // 1小时

  constructor(private readonly redisService: RedisService) {}

  /**
   * 获取缓存数据
   */
  async get<T>(key: string): Promise<T | null> {
    try {
      const data = await this.redisService.get(key);
      if (data) {
        return JSON.parse(data);
      }
      return null;
    } catch (error) {
      this.logger.error(`Cache GET error for key ${key}:`, error);
      return null;
    }
  }

  /**
   * 设置缓存数据
   */
  async set<T>(key: string, value: T, ttl: number = this.defaultTTL): Promise<boolean> {
    try {
      const data = JSON.stringify(value);
      return await this.redisService.set(key, data, ttl);
    } catch (error) {
      this.logger.error(`Cache SET error for key ${key}:`, error);
      return false;
    }
  }

  /**
   * 删除缓存
   */
  async del(key: string): Promise<boolean> {
    return await this.redisService.del(key);
  }

  /**
   * 检查缓存是否存在
   */
  async exists(key: string): Promise<boolean> {
    return await this.redisService.exists(key);
  }

  /**
   * 获取缓存的TTL
   */
  async ttl(key: string): Promise<number> {
    return await this.redisService.ttl(key);
  }

  /**
   * 递增缓存值
   */
  async incr(key: string): Promise<number | null> {
    return await this.redisService.incr(key);
  }

  /**
   * 获取或设置缓存（如果不存在则执行回调函数获取数据并缓存）
   */
  async getOrSet<T>(
    key: string,
    callback: () => Promise<T>,
    ttl: number = this.defaultTTL,
  ): Promise<T | null> {
    try {
      // 先尝试从缓存获取
      const cached = await this.get<T>(key);
      if (cached !== null) {
        return cached;
      }

      // 缓存不存在，执行回调获取数据
      const data = await callback();
      if (data !== null && data !== undefined) {
        await this.set(key, data, ttl);
      }

      return data;
    } catch (error) {
      this.logger.error(`Cache getOrSet error for key ${key}:`, error);
      // 如果缓存操作失败，直接执行回调
      try {
        return await callback();
      } catch (callbackError) {
        this.logger.error(`Callback error for key ${key}:`, callbackError);
        return null;
      }
    }
  }

  /**
   * 批量删除缓存（支持通配符）
   */
  async delPattern(pattern: string): Promise<number> {
    try {
      const keys = await this.redisService.keys(pattern);
      if (keys.length === 0) {
        return 0;
      }

      let deletedCount = 0;
      for (const key of keys) {
        const success = await this.redisService.del(key);
        if (success) {
          deletedCount++;
        }
      }

      return deletedCount;
    } catch (error) {
      this.logger.error(`Cache delPattern error for pattern ${pattern}:`, error);
      return 0;
    }
  }

  /**
   * 生成缓存键
   */
  generateKey(prefix: string, ...parts: (string | number)[]): string {
    return `ecosystem:${prefix}:${parts.join(':')}`;
  }

  /**
   * 合作伙伴相关缓存键
   */
  getPartnerKey(partnerId: string): string {
    return this.generateKey('partner', partnerId);
  }

  getPartnersListKey(filters: any): string {
    const filterStr = Object.keys(filters)
      .sort()
      .map(key => `${key}:${filters[key]}`)
      .join('|');
    return this.generateKey('partners', 'list', filterStr || 'all');
  }

  /**
   * API相关缓存键
   */
  getApiKey(apiId: string): string {
    return this.generateKey('api', apiId);
  }

  getApiUsageKey(apiId: string, date: string): string {
    return this.generateKey('api', 'usage', apiId, date);
  }

  /**
   * 应用相关缓存键
   */
  getApplicationKey(appId: string): string {
    return this.generateKey('application', appId);
  }

  getApplicationMetricsKey(appId: string): string {
    return this.generateKey('application', 'metrics', appId);
  }

  /**
   * 标准相关缓存键
   */
  getStandardKey(standardId: string): string {
    return this.generateKey('standard', standardId);
  }

  getComplianceKey(standardId: string, entityId: string): string {
    return this.generateKey('compliance', standardId, entityId);
  }

  /**
   * 统计相关缓存键
   */
  getEcosystemStatsKey(): string {
    return this.generateKey('stats', 'ecosystem');
  }

  /**
   * 会话相关缓存键
   */
  getSessionKey(sessionId: string): string {
    return this.generateKey('session', sessionId);
  }

  /**
   * 速率限制相关缓存键
   */
  getRateLimitKey(identifier: string, window: string): string {
    return this.generateKey('ratelimit', identifier, window);
  }

  /**
   * 清除所有相关缓存
   */
  async clearAll(): Promise<number> {
    return await this.delPattern('ecosystem:*');
  }

  /**
   * 清除特定类型的缓存
   */
  async clearPartnerCache(): Promise<number> {
    return await this.delPattern('ecosystem:partner:*');
  }

  async clearApiCache(): Promise<number> {
    return await this.delPattern('ecosystem:api:*');
  }

  async clearApplicationCache(): Promise<number> {
    return await this.delPattern('ecosystem:application:*');
  }

  async clearStandardCache(): Promise<number> {
    return await this.delPattern('ecosystem:standard:*');
  }
}

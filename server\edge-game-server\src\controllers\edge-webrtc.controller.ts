import { <PERSON>, Get, Post, Body, Param, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { EdgeWebRTCService } from '../services/edge-webrtc.service';

/**
 * 边缘WebRTC控制器
 */
@ApiTags('边缘WebRTC')
@Controller('webrtc')
export class EdgeWebRTCController {
  private readonly logger = new Logger(EdgeWebRTCController.name);

  constructor(private readonly webrtcService: EdgeWebRTCService) {}

  /**
   * 创建WebRTC连接
   */
  @Post('connections')
  @ApiOperation({ summary: '创建WebRTC连接' })
  @ApiResponse({ status: 201, description: 'WebRTC连接创建成功' })
  async createConnection(@Body() body: { userId: string; options: any }) {
    try {
      const connection = await this.webrtcService.createConnection(body.userId, body.options);
      
      return {
        success: true,
        data: connection,
        message: 'WebRTC连接创建成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error(`创建WebRTC连接失败: ${error.message}`);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * 获取连接状态
   */
  @Get('connections/:connectionId')
  @ApiOperation({ summary: '获取WebRTC连接状态' })
  @ApiResponse({ status: 200, description: '成功获取连接状态' })
  getConnectionStatus(@Param('connectionId') connectionId: string) {
    try {
      return {
        success: true,
        data: {
          connectionId,
          status: 'connected',
          createdAt: new Date()
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error(`获取连接状态失败: ${error.message}`);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

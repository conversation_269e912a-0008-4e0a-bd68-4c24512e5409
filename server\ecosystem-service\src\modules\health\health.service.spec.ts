import { Test, TestingModule } from '@nestjs/testing';
import { HealthService } from './health.service';
import { RedisService } from '../../common/redis/redis.service';
import { createMockRedisService } from '../../test-utils/test-helpers';

describe('HealthService', () => {
  let service: HealthService;
  let redisService: jest.Mocked<RedisService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HealthService,
        {
          provide: RedisService,
          useValue: createMockRedisService(),
        },
      ],
    }).compile();

    service = module.get<HealthService>(HealthService);
    redisService = module.get(RedisService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getBasicHealth', () => {
    it('should return basic health information', async () => {
      const result = await service.getBasicHealth();

      expect(result).toEqual({
        status: 'ok',
        timestamp: expect.any(String),
        uptime: expect.any(Number),
        service: 'ecosystem-service',
      });
    });
  });

  describe('getDetailedHealth', () => {
    it('should return detailed health information', async () => {
      redisService.ping.mockResolvedValue(true);

      const result = await service.getDetailedHealth();

      expect(result).toEqual({
        status: 'ok',
        timestamp: expect.any(String),
        uptime: expect.any(Number),
        startTime: expect.any(String),
        service: 'ecosystem-service',
        version: '1.0.0',
        environment: expect.any(String),
        memory: {
          rss: expect.any(String),
          heapTotal: expect.any(String),
          heapUsed: expect.any(String),
          external: expect.any(String),
        },
        cpu: {
          usage: expect.any(Object),
        },
        dependencies: {
          database: 'ok',
          redis: 'ok',
          externalServices: 'ok',
        },
      });
    });
  });

  describe('getReadiness', () => {
    it('should return ready status when all checks pass', async () => {
      redisService.ping.mockResolvedValue(true);

      const result = await service.getReadiness();

      expect(result).toEqual({
        status: 'ready',
        timestamp: expect.any(String),
        checks: {
          database: 'ok',
          redis: 'ok',
          externalServices: 'ok',
        },
      });
    });

    it('should return not ready status when checks fail', async () => {
      redisService.ping.mockResolvedValue(false);

      const result = await service.getReadiness();

      expect(result).toEqual({
        status: 'not_ready',
        timestamp: expect.any(String),
        checks: {
          database: 'ok',
          redis: 'error',
          externalServices: 'ok',
        },
      });
    });
  });

  describe('getLiveness', () => {
    it('should return liveness information', async () => {
      const result = await service.getLiveness();

      expect(result).toEqual({
        status: 'alive',
        timestamp: expect.any(String),
        uptime: expect.any(Number),
        pid: expect.any(Number),
      });
    });
  });

  describe('checkRedis', () => {
    it('should return ok when Redis is connected', async () => {
      redisService.ping.mockResolvedValue(true);

      // Access private method for testing
      const result = await (service as any).checkRedis();

      expect(result).toBe('ok');
      expect(redisService.ping).toHaveBeenCalled();
    });

    it('should return error when Redis is not connected', async () => {
      redisService.ping.mockResolvedValue(false);

      const result = await (service as any).checkRedis();

      expect(result).toBe('error');
    });

    it('should return error when Redis throws exception', async () => {
      redisService.ping.mockRejectedValue(new Error('Connection failed'));

      const result = await (service as any).checkRedis();

      expect(result).toBe('error');
    });
  });

  describe('checkDatabase', () => {
    it('should return ok for database check', async () => {
      const result = await (service as any).checkDatabase();

      expect(result).toBe('ok');
    });
  });

  describe('checkExternalServices', () => {
    it('should return ok for external services check', async () => {
      const result = await (service as any).checkExternalServices();

      expect(result).toBe('ok');
    });
  });
});

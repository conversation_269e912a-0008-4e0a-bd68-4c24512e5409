/**
 * 监控服务
 * 
 * 负责系统监控和性能分析，包括：
 * - 系统资源监控
 * - 性能指标收集
 * - 告警管理
 * - 报告生成
 */

import { Injectable, Logger, Inject } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';
import * as os from 'os';
import * as fs from 'fs/promises';

/**
 * 系统指标接口
 */
export interface SystemMetrics {
  timestamp: number;
  cpu: {
    usage: number;
    cores: number;
    loadAverage: number[];
  };
  memory: {
    total: number;
    used: number;
    free: number;
    usage: number;
  };
  disk: {
    total: number;
    used: number;
    free: number;
    usage: number;
  };
  network: {
    bytesIn: number;
    bytesOut: number;
    packetsIn: number;
    packetsOut: number;
  };
  gpu?: {
    usage: number;
    memory: number;
    temperature: number;
  };
}

/**
 * 告警接口
 */
export interface Alert {
  id: string;
  type: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
  message: string;
  timestamp: number;
  resolved: boolean;
  modelId?: string;
  threshold?: number;
  currentValue?: number;
  resolvedAt?: number;
}

/**
 * 性能报告接口
 */
export interface PerformanceReport {
  timeRange: {
    start: number;
    end: number;
  };
  summary: {
    totalRequests: number;
    successRate: number;
    averageLatency: number;
    peakThroughput: number;
    errorCount: number;
  };
  modelPerformance: Array<{
    modelId: string;
    modelName: string;
    requests: number;
    averageLatency: number;
    errorRate: number;
    throughput: number;
  }>;
  systemHealth: {
    cpuUsage: number;
    memoryUsage: number;
    diskUsage: number;
    networkTraffic: number;
  };
  alerts: Alert[];
  recommendations: string[];
}

/**
 * 队列状态接口
 */
export interface QueueStatus {
  totalJobs: number;
  waitingJobs: number;
  activeJobs: number;
  completedJobs: number;
  failedJobs: number;
  averageWaitTime: number;
  healthStatus: 'healthy' | 'degraded' | 'unhealthy';
  lastUpdated: number;
}

/**
 * 监控服务
 */
@Injectable()
export class MonitoringService {
  private readonly logger = new Logger(MonitoringService.name);
  
  private alerts = new Map<string, Alert>();
  private metricsHistory: SystemMetrics[] = [];
  private lastNetworkStats = { bytesIn: 0, bytesOut: 0, packetsIn: 0, packetsOut: 0 };
  
  // 性能阈值
  private readonly thresholds = {
    cpuUsage: 0.8,
    memoryUsage: 0.85,
    diskUsage: 0.9,
    errorRate: 0.1,
    latency: 5000, // 5秒
    queueSize: 500,
  };

  constructor(
    private readonly eventEmitter: EventEmitter2,
    @Inject('MONITORING_CONFIG') private readonly config: any,
  ) {
    this.initializeService();
  }

  /**
   * 初始化服务
   */
  private async initializeService(): Promise<void> {
    try {
      if (this.config.enableMetrics) {
        this.startMetricsCollection();
      }
      
      this.logger.log('监控服务已初始化');
      
    } catch (error) {
      this.logger.error('监控服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 获取系统指标
   */
  public async getSystemMetrics(): Promise<SystemMetrics> {
    try {
      const cpuUsage = await this.getCpuUsage();
      const memoryInfo = this.getMemoryInfo();
      const diskInfo = await this.getDiskInfo();
      const networkInfo = this.getNetworkInfo();
      const gpuInfo = await this.getGpuInfo();

      const metrics: SystemMetrics = {
        timestamp: Date.now(),
        cpu: {
          usage: cpuUsage,
          cores: os.cpus().length,
          loadAverage: os.loadavg(),
        },
        memory: memoryInfo,
        disk: diskInfo,
        network: networkInfo,
        gpu: gpuInfo,
      };

      return metrics;

    } catch (error) {
      this.logger.error('获取系统指标失败:', error);
      throw error;
    }
  }

  /**
   * 生成性能报告
   */
  public async generatePerformanceReport(startTime?: number, endTime?: number): Promise<PerformanceReport> {
    try {
      const now = Date.now();
      const start = startTime || (now - 24 * 60 * 60 * 1000); // 默认24小时前
      const end = endTime || now;

      // 获取时间范围内的指标
      const relevantMetrics = this.metricsHistory.filter(
        metric => metric.timestamp >= start && metric.timestamp <= end
      );

      // 生成报告
      const report: PerformanceReport = {
        timeRange: { start, end },
        summary: {
          totalRequests: 0, // 需要从推理服务获取
          successRate: 0.95, // 模拟值
          averageLatency: 125.5, // 模拟值
          peakThroughput: 200, // 模拟值
          errorCount: 0, // 需要从推理服务获取
        },
        modelPerformance: [], // 需要从推理服务获取
        systemHealth: this.calculateSystemHealth(relevantMetrics),
        alerts: Array.from(this.alerts.values()).filter(
          alert => alert.timestamp >= start && alert.timestamp <= end
        ),
        recommendations: this.generateRecommendations(relevantMetrics),
      };

      return report;

    } catch (error) {
      this.logger.error('生成性能报告失败:', error);
      throw error;
    }
  }

  /**
   * 获取告警信息
   */
  public async getAlerts(severity?: string, limit?: number): Promise<Alert[]> {
    try {
      let alerts = Array.from(this.alerts.values());

      // 按严重程度过滤
      if (severity) {
        alerts = alerts.filter(alert => alert.severity === severity);
      }

      // 按时间排序（最新的在前）
      alerts.sort((a, b) => b.timestamp - a.timestamp);

      // 限制数量
      if (limit) {
        alerts = alerts.slice(0, limit);
      }

      return alerts;

    } catch (error) {
      this.logger.error('获取告警信息失败:', error);
      throw error;
    }
  }

  /**
   * 获取队列状态
   */
  public async getQueueStatus(): Promise<QueueStatus> {
    try {
      // 这里需要从推理服务获取实际的队列状态
      // 暂时返回模拟数据
      const status: QueueStatus = {
        totalJobs: 1000,
        waitingJobs: 10,
        activeJobs: 5,
        completedJobs: 950,
        failedJobs: 35,
        averageWaitTime: 2500,
        healthStatus: 'healthy',
        lastUpdated: Date.now(),
      };

      return status;

    } catch (error) {
      this.logger.error('获取队列状态失败:', error);
      throw error;
    }
  }

  /**
   * 创建告警
   */
  public createAlert(alert: Omit<Alert, 'id' | 'timestamp' | 'resolved'>): void {
    try {
      const alertId = `alert_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      
      const newAlert: Alert = {
        ...alert,
        id: alertId,
        timestamp: Date.now(),
        resolved: false,
      };

      this.alerts.set(alertId, newAlert);

      this.eventEmitter.emit('monitoring.alert.created', newAlert);

      this.logger.warn(`告警创建: ${alert.type} - ${alert.message}`);

    } catch (error) {
      this.logger.error('创建告警失败:', error);
    }
  }

  /**
   * 解决告警
   */
  public resolveAlert(alertId: string): void {
    try {
      const alert = this.alerts.get(alertId);
      
      if (alert) {
        alert.resolved = true;
        alert.resolvedAt = Date.now();
        
        this.eventEmitter.emit('monitoring.alert.resolved', alert);
        
        this.logger.log(`告警已解决: ${alertId}`);
      }

    } catch (error) {
      this.logger.error('解决告警失败:', error);
    }
  }

  /**
   * 启动指标收集
   */
  private startMetricsCollection(): void {
    setInterval(async () => {
      try {
        const metrics = await this.getSystemMetrics();
        this.metricsHistory.push(metrics);
        
        // 保留最近24小时的数据
        const cutoffTime = Date.now() - 24 * 60 * 60 * 1000;
        this.metricsHistory = this.metricsHistory.filter(m => m.timestamp > cutoffTime);
        
        // 检查阈值
        this.checkThresholds(metrics);
        
      } catch (error) {
        this.logger.error('收集指标失败:', error);
      }
    }, this.config.metricsInterval);
  }

  /**
   * 获取CPU使用率
   */
  private async getCpuUsage(): Promise<number> {
    return new Promise((resolve) => {
      const startMeasure = this.cpuAverage();
      
      setTimeout(() => {
        const endMeasure = this.cpuAverage();
        const idleDifference = endMeasure.idle - startMeasure.idle;
        const totalDifference = endMeasure.total - startMeasure.total;
        const usage = 1 - idleDifference / totalDifference;
        
        resolve(Math.max(0, Math.min(1, usage)));
      }, 100);
    });
  }

  /**
   * CPU平均值计算
   */
  private cpuAverage(): { idle: number; total: number } {
    const cpus = os.cpus();
    let idle = 0;
    let total = 0;

    cpus.forEach(cpu => {
      Object.values(cpu.times).forEach(time => {
        total += time;
      });
      idle += cpu.times.idle;
    });

    return { idle: idle / cpus.length, total: total / cpus.length };
  }

  /**
   * 获取内存信息
   */
  private getMemoryInfo(): SystemMetrics['memory'] {
    const total = os.totalmem();
    const free = os.freemem();
    const used = total - free;
    const usage = used / total;

    return {
      total,
      used,
      free,
      usage,
    };
  }

  /**
   * 获取磁盘信息
   */
  private async getDiskInfo(): Promise<SystemMetrics['disk']> {
    try {
      // 简化的磁盘信息获取
      // 实际实现需要使用系统调用或第三方库
      const total = 1073741824000; // 1TB 模拟值
      const used = 536870912000; // 500GB 模拟值
      const free = total - used;
      const usage = used / total;

      return {
        total,
        used,
        free,
        usage,
      };

    } catch (error) {
      this.logger.error('获取磁盘信息失败:', error);
      return {
        total: 0,
        used: 0,
        free: 0,
        usage: 0,
      };
    }
  }

  /**
   * 获取网络信息
   */
  private getNetworkInfo(): SystemMetrics['network'] {
    // 简化的网络信息获取
    // 实际实现需要读取系统网络统计
    const current = {
      bytesIn: Math.floor(Math.random() * 1000000),
      bytesOut: Math.floor(Math.random() * 1000000),
      packetsIn: Math.floor(Math.random() * 10000),
      packetsOut: Math.floor(Math.random() * 10000),
    };

    const result = {
      bytesIn: current.bytesIn - this.lastNetworkStats.bytesIn,
      bytesOut: current.bytesOut - this.lastNetworkStats.bytesOut,
      packetsIn: current.packetsIn - this.lastNetworkStats.packetsIn,
      packetsOut: current.packetsOut - this.lastNetworkStats.packetsOut,
    };

    this.lastNetworkStats = current;

    return result;
  }

  /**
   * 获取GPU信息
   */
  private async getGpuInfo(): Promise<SystemMetrics['gpu'] | undefined> {
    try {
      // 简化的GPU信息获取
      // 实际实现需要使用nvidia-ml-py或类似库
      return {
        usage: Math.random() * 0.5 + 0.3, // 30-80%
        memory: Math.random() * 0.4 + 0.4, // 40-80%
        temperature: Math.random() * 20 + 60, // 60-80°C
      };

    } catch (error) {
      // GPU不可用或获取失败
      return undefined;
    }
  }

  /**
   * 检查阈值
   */
  private checkThresholds(metrics: SystemMetrics): void {
    // CPU使用率检查
    if (metrics.cpu.usage > this.thresholds.cpuUsage) {
      this.createAlert({
        type: 'high_cpu_usage',
        severity: 'warning',
        message: `CPU使用率过高: ${(metrics.cpu.usage * 100).toFixed(1)}%`,
        threshold: this.thresholds.cpuUsage,
        currentValue: metrics.cpu.usage,
      });
    }

    // 内存使用率检查
    if (metrics.memory.usage > this.thresholds.memoryUsage) {
      this.createAlert({
        type: 'high_memory_usage',
        severity: 'warning',
        message: `内存使用率过高: ${(metrics.memory.usage * 100).toFixed(1)}%`,
        threshold: this.thresholds.memoryUsage,
        currentValue: metrics.memory.usage,
      });
    }

    // 磁盘使用率检查
    if (metrics.disk.usage > this.thresholds.diskUsage) {
      this.createAlert({
        type: 'high_disk_usage',
        severity: 'error',
        message: `磁盘使用率过高: ${(metrics.disk.usage * 100).toFixed(1)}%`,
        threshold: this.thresholds.diskUsage,
        currentValue: metrics.disk.usage,
      });
    }
  }

  /**
   * 计算系统健康状况
   */
  private calculateSystemHealth(metrics: SystemMetrics[]): PerformanceReport['systemHealth'] {
    if (metrics.length === 0) {
      return {
        cpuUsage: 0,
        memoryUsage: 0,
        diskUsage: 0,
        networkTraffic: 0,
      };
    }

    const avgCpu = metrics.reduce((sum, m) => sum + m.cpu.usage, 0) / metrics.length;
    const avgMemory = metrics.reduce((sum, m) => sum + m.memory.usage, 0) / metrics.length;
    const avgDisk = metrics.reduce((sum, m) => sum + m.disk.usage, 0) / metrics.length;
    const totalNetwork = metrics.reduce((sum, m) => sum + m.network.bytesIn + m.network.bytesOut, 0);

    return {
      cpuUsage: avgCpu,
      memoryUsage: avgMemory,
      diskUsage: avgDisk,
      networkTraffic: totalNetwork,
    };
  }

  /**
   * 生成建议
   */
  private generateRecommendations(metrics: SystemMetrics[]): string[] {
    const recommendations: string[] = [];

    if (metrics.length === 0) {
      return recommendations;
    }

    const avgCpu = metrics.reduce((sum, m) => sum + m.cpu.usage, 0) / metrics.length;
    const avgMemory = metrics.reduce((sum, m) => sum + m.memory.usage, 0) / metrics.length;
    const avgDisk = metrics.reduce((sum, m) => sum + m.disk.usage, 0) / metrics.length;

    if (avgCpu > 0.7) {
      recommendations.push('考虑增加CPU资源或优化计算密集型任务');
    }

    if (avgMemory > 0.8) {
      recommendations.push('考虑增加内存容量或优化内存使用');
    }

    if (avgDisk > 0.8) {
      recommendations.push('考虑清理磁盘空间或增加存储容量');
    }

    return recommendations;
  }

  /**
   * 定期清理过期数据
   */
  @Cron(CronExpression.EVERY_HOUR)
  private async cleanupExpiredData(): Promise<void> {
    try {
      const cutoffTime = Date.now() - 24 * 60 * 60 * 1000; // 24小时前

      // 清理过期指标
      this.metricsHistory = this.metricsHistory.filter(m => m.timestamp > cutoffTime);

      // 清理已解决的告警（保留7天）
      const alertCutoffTime = Date.now() - 7 * 24 * 60 * 60 * 1000;
      for (const [id, alert] of this.alerts) {
        if (alert.resolved && alert.resolvedAt && alert.resolvedAt < alertCutoffTime) {
          this.alerts.delete(id);
        }
      }

      this.logger.log('过期数据清理完成');

    } catch (error) {
      this.logger.error('清理过期数据失败:', error);
    }
  }
}

import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete, 
  Body, 
  Param, 
  Query, 
  HttpStatus, 
  HttpException,
  Logger 
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { EdgeInstanceService, GameInstanceStatus } from '../services/edge-instance.service';

/**
 * 边缘实例控制器
 * 管理游戏实例和用户会话的API接口
 */
@ApiTags('边缘实例管理')
@Controller('instances')
export class EdgeInstanceController {
  private readonly logger = new Logger(EdgeInstanceController.name);

  constructor(
    private readonly instanceService: EdgeInstanceService,
  ) {}

  /**
   * 创建游戏实例
   */
  @Post('games')
  @ApiOperation({ summary: '创建游戏实例' })
  @ApiResponse({ status: 201, description: '游戏实例创建成功' })
  async createGameInstance(@Body() body: {
    gameId: string;
    roomId: string;
    gameType: string;
    gameMode: string;
    maxPlayers: number;
    settings: Record<string, any>;
  }) {
    try {
      const instance = await this.instanceService.createGameInstance(body);
      
      return {
        success: true,
        data: instance,
        message: '游戏实例创建成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error(`创建游戏实例失败: ${error.message}`);
      throw new HttpException(
        error.message || '创建游戏实例失败',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  /**
   * 启动游戏实例
   */
  @Put('games/:instanceId/start')
  @ApiOperation({ summary: '启动游戏实例' })
  @ApiResponse({ status: 200, description: '游戏实例启动成功' })
  @ApiParam({ name: 'instanceId', description: '实例ID' })
  async startGameInstance(@Param('instanceId') instanceId: string) {
    try {
      await this.instanceService.startGameInstance(instanceId);
      
      return {
        success: true,
        message: '游戏实例启动成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error(`启动游戏实例失败: ${error.message}`);
      throw new HttpException(
        error.message || '启动游戏实例失败',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  /**
   * 停止游戏实例
   */
  @Put('games/:instanceId/stop')
  @ApiOperation({ summary: '停止游戏实例' })
  @ApiResponse({ status: 200, description: '游戏实例停止成功' })
  @ApiParam({ name: 'instanceId', description: '实例ID' })
  async stopGameInstance(@Param('instanceId') instanceId: string) {
    try {
      await this.instanceService.stopGameInstance(instanceId);
      
      return {
        success: true,
        message: '游戏实例停止成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error(`停止游戏实例失败: ${error.message}`);
      throw new HttpException(
        error.message || '停止游戏实例失败',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  /**
   * 删除游戏实例
   */
  @Delete('games/:instanceId')
  @ApiOperation({ summary: '删除游戏实例' })
  @ApiResponse({ status: 200, description: '游戏实例删除成功' })
  @ApiParam({ name: 'instanceId', description: '实例ID' })
  async deleteGameInstance(@Param('instanceId') instanceId: string) {
    try {
      await this.instanceService.deleteGameInstance(instanceId);
      
      return {
        success: true,
        message: '游戏实例删除成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error(`删除游戏实例失败: ${error.message}`);
      throw new HttpException(
        error.message || '删除游戏实例失败',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  /**
   * 获取游戏实例信息
   */
  @Get('games/:instanceId')
  @ApiOperation({ summary: '获取游戏实例信息' })
  @ApiResponse({ status: 200, description: '成功获取游戏实例信息' })
  @ApiParam({ name: 'instanceId', description: '实例ID' })
  getGameInstance(@Param('instanceId') instanceId: string) {
    try {
      const instance = this.instanceService.getGameInstance(instanceId);
      
      if (!instance) {
        throw new HttpException(
          '游戏实例不存在',
          HttpStatus.NOT_FOUND
        );
      }

      return {
        success: true,
        data: instance,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      
      this.logger.error(`获取游戏实例失败: ${error.message}`);
      throw new HttpException(
        '获取游戏实例失败',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取所有游戏实例
   */
  @Get('games')
  @ApiOperation({ summary: '获取所有游戏实例' })
  @ApiResponse({ status: 200, description: '成功获取游戏实例列表' })
  getAllGameInstances(@Query('status') status?: GameInstanceStatus) {
    try {
      let instances = this.instanceService.getAllGameInstances();
      
      if (status) {
        instances = instances.filter(instance => instance.status === status);
      }

      return {
        success: true,
        data: {
          instances,
          count: instances.length,
          filter: status ? { status } : null
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error(`获取游戏实例列表失败: ${error.message}`);
      throw new HttpException(
        '获取游戏实例列表失败',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 添加玩家到实例
   */
  @Post('games/:instanceId/players')
  @ApiOperation({ summary: '添加玩家到游戏实例' })
  @ApiResponse({ status: 200, description: '玩家添加成功' })
  @ApiParam({ name: 'instanceId', description: '实例ID' })
  async addPlayerToInstance(
    @Param('instanceId') instanceId: string,
    @Body() body: { userId: string }
  ) {
    try {
      await this.instanceService.addPlayerToInstance(instanceId, body.userId);
      
      return {
        success: true,
        message: '玩家添加成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error(`添加玩家失败: ${error.message}`);
      throw new HttpException(
        error.message || '添加玩家失败',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  /**
   * 从实例移除玩家
   */
  @Delete('games/:instanceId/players/:userId')
  @ApiOperation({ summary: '从游戏实例移除玩家' })
  @ApiResponse({ status: 200, description: '玩家移除成功' })
  @ApiParam({ name: 'instanceId', description: '实例ID' })
  @ApiParam({ name: 'userId', description: '用户ID' })
  async removePlayerFromInstance(
    @Param('instanceId') instanceId: string,
    @Param('userId') userId: string
  ) {
    try {
      await this.instanceService.removePlayerFromInstance(instanceId, userId);
      
      return {
        success: true,
        message: '玩家移除成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error(`移除玩家失败: ${error.message}`);
      throw new HttpException(
        error.message || '移除玩家失败',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  /**
   * 创建用户会话
   */
  @Post('sessions')
  @ApiOperation({ summary: '创建用户会话' })
  @ApiResponse({ status: 201, description: '用户会话创建成功' })
  createUserSession(@Body() body: {
    userId: string;
    userAgent: string;
    ipAddress: string;
    region: string;
  }) {
    try {
      const session = this.instanceService.createUserSession(body);
      
      return {
        success: true,
        data: session,
        message: '用户会话创建成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error(`创建用户会话失败: ${error.message}`);
      throw new HttpException(
        error.message || '创建用户会话失败',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  /**
   * 删除用户会话
   */
  @Delete('sessions/:userId')
  @ApiOperation({ summary: '删除用户会话' })
  @ApiResponse({ status: 200, description: '用户会话删除成功' })
  @ApiParam({ name: 'userId', description: '用户ID' })
  async deleteUserSession(@Param('userId') userId: string) {
    try {
      await this.instanceService.deleteUserSession(userId);
      
      return {
        success: true,
        message: '用户会话删除成功',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error(`删除用户会话失败: ${error.message}`);
      throw new HttpException(
        error.message || '删除用户会话失败',
        HttpStatus.BAD_REQUEST
      );
    }
  }

  /**
   * 获取实例统计信息
   */
  @Get('stats')
  @ApiOperation({ summary: '获取实例统计信息' })
  @ApiResponse({ status: 200, description: '成功获取统计信息' })
  getInstanceStats() {
    try {
      const stats = this.instanceService.getInstanceStats();
      
      return {
        success: true,
        data: stats,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error(`获取统计信息失败: ${error.message}`);
      throw new HttpException(
        '获取统计信息失败',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}

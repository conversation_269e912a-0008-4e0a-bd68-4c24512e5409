# 群体协调服务 Docker 忽略文件

# 依赖目录
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建输出
dist
build
*.tsbuildinfo

# 环境配置
.env
.env.local
.env.development
.env.test
.env.production

# 日志文件
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage
*.lcov
.nyc_output

# 测试文件
test
tests
**/*.test.ts
**/*.spec.ts
jest.config.js
.jest

# IDE 配置
.vscode
.idea
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore
.gitattributes

# Docker
Dockerfile*
docker-compose*
.dockerignore

# 文档
README.md
CHANGELOG.md
LICENSE
*.md

# 配置文件
.eslintrc*
.prettierrc*
tsconfig*.json
nest-cli.json

# 临时文件
tmp
temp
.tmp
.temp

# 缓存
.cache
.parcel-cache

# 监控和日志
monitoring
grafana
prometheus

# 备份文件
*.bak
*.backup

# 开发工具
.editorconfig
.nvmrc

# 安全文件
*.pem
*.key
*.crt
*.p12

# 数据文件
*.sqlite
*.db
data/

# 其他
.next
.nuxt
.vuepress/dist
.serverless
.fusebox/
.dynamodb/
.tern-port

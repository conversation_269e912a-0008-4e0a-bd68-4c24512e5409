import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { CacheService } from '../common/redis/cache.service';
import { RedisService } from '../common/redis/redis.service';
import { AuthService, User } from '../common/auth/auth.service';

/**
 * 创建模拟的Repository
 */
export const createMockRepository = <T = any>(): Partial<Repository<T>> => ({
  find: jest.fn(),
  findOne: jest.fn(),
  findAndCount: jest.fn(),
  save: jest.fn(),
  create: jest.fn(),
  remove: jest.fn(),
  delete: jest.fn(),
  update: jest.fn(),
  count: jest.fn(),
});

/**
 * 创建模拟的CacheService
 */
export const createMockCacheService = (): Partial<CacheService> => ({
  get: jest.fn(),
  set: jest.fn(),
  del: jest.fn(),
  exists: jest.fn(),
  delPattern: jest.fn(),
  getOrSet: jest.fn(),
  generateKey: jest.fn(),
  getPartnerKey: jest.fn(),
  getPartnersListKey: jest.fn(),
  getApiKey: jest.fn(),
  getApplicationKey: jest.fn(),
  getStandardKey: jest.fn(),
});

/**
 * 创建模拟的RedisService
 */
export const createMockRedisService = (): Partial<RedisService> => ({
  get: jest.fn(),
  set: jest.fn(),
  del: jest.fn(),
  exists: jest.fn(),
  ping: jest.fn().mockResolvedValue(true),
  incr: jest.fn(),
  decr: jest.fn(),
  ttl: jest.fn(),
});

/**
 * 创建模拟的JwtService
 */
export const createMockJwtService = (): Partial<JwtService> => ({
  sign: jest.fn(),
  verify: jest.fn(),
  decode: jest.fn(),
});

/**
 * 创建模拟的ConfigService
 */
export const createMockConfigService = (): Partial<ConfigService> => ({
  get: jest.fn((key: string, defaultValue?: any) => {
    const config = {
      JWT_SECRET: 'test-secret',
      JWT_EXPIRES_IN: '24h',
      REDIS_HOST: 'localhost',
      REDIS_PORT: 6379,
      DB_TYPE: 'mysql',
      DB_HOST: 'localhost',
      DB_PORT: 3306,
      DB_USERNAME: 'test',
      DB_PASSWORD: 'test',
      DB_DATABASE: 'test_ecosystem',
    };
    return config[key] || defaultValue;
  }),
});

/**
 * 创建测试用户
 */
export const createTestUser = (overrides: Partial<User> = {}): User => ({
  id: 'test-user-id',
  username: 'testuser',
  email: '<EMAIL>',
  roles: ['user'],
  permissions: ['partners:read'],
  ...overrides,
});

/**
 * 创建管理员用户
 */
export const createAdminUser = (overrides: Partial<User> = {}): User => ({
  id: 'admin-user-id',
  username: 'admin',
  email: '<EMAIL>',
  roles: ['admin'],
  permissions: ['*'],
  ...overrides,
});

/**
 * 创建合作伙伴用户
 */
export const createPartnerUser = (partnerId: string, overrides: Partial<User> = {}): User => ({
  id: 'partner-user-id',
  username: 'partner',
  email: '<EMAIL>',
  roles: ['partner'],
  permissions: ['partners:read', 'partners:update'],
  partnerId,
  ...overrides,
});

/**
 * 等待指定时间
 */
export const sleep = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * 创建测试模块的通用配置
 */
export const createTestModuleBuilder = () => {
  return Test.createTestingModule({
    providers: [
      {
        provide: ConfigService,
        useValue: createMockConfigService(),
      },
      {
        provide: CacheService,
        useValue: createMockCacheService(),
      },
      {
        provide: RedisService,
        useValue: createMockRedisService(),
      },
      {
        provide: JwtService,
        useValue: createMockJwtService(),
      },
    ],
  });
};

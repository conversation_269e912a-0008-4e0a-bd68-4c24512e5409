{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2020", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@/*": ["src/*"], "@shared/*": ["../shared/src/*"]}, "lib": ["ES2020"], "moduleResolution": "node", "resolveJsonModule": true, "esModuleInterop": true, "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitReturns": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false}, "include": ["src/**/*", "test/**/*"], "exclude": ["node_modules", "dist", "coverage", "**/*.spec.ts", "**/*.test.ts"], "ts-node": {"require": ["tsconfig-paths/register"]}}
version: '3.8'

services:
  # 生态系统服务
  ecosystem-service:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: ecosystem-service
    restart: unless-stopped
    ports:
      - "3030:3030"
    environment:
      - NODE_ENV=production
      - PORT=3030
      - HOST=0.0.0.0
      
      # 数据库配置
      - DB_TYPE=mysql
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=ecosystem_user
      - DB_PASSWORD=ecosystem_password
      - DB_DATABASE=ecosystem_service
      - DB_SYNCHRONIZE=false
      - DB_LOGGING=false
      
      # Redis配置
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=redis_password
      - REDIS_DB=0
      
      # JWT配置
      - JWT_SECRET=${JWT_SECRET:-ecosystem-service-secret-key}
      - JWT_EXPIRES_IN=24h
      
      # 其他配置
      - API_PREFIX=api
      - CORS_ORIGIN=*
      - ENABLE_METRICS=true
      - LOG_LEVEL=info
      - LOG_FORMAT=json
    volumes:
      - ecosystem_storage:/app/storage
      - ecosystem_logs:/app/logs
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - ecosystem-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3030/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: ecosystem-mysql
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=ecosystem_service
      - MYSQL_USER=ecosystem_user
      - MYSQL_PASSWORD=ecosystem_password
      - MYSQL_CHARACTER_SET_SERVER=utf8mb4
      - MYSQL_COLLATION_SERVER=utf8mb4_unicode_ci
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - ecosystem-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-proot_password"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: ecosystem-redis
    restart: unless-stopped
    command: redis-server --requirepass redis_password --appendonly yes
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ecosystem-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: ecosystem-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./docker/nginx/conf.d:/etc/nginx/conf.d:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - ecosystem-service
    networks:
      - ecosystem-network

# 数据卷
volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  ecosystem_storage:
    driver: local
  ecosystem_logs:
    driver: local
  nginx_logs:
    driver: local

# 网络
networks:
  ecosystem-network:
    driver: bridge

import { Injectable, Logger } from '@nestjs/common';
import { InferenceRequest, InferenceStatus } from './entities/inference-request.entity';
import { InferenceResult } from './entities/inference-result.entity';

@Injectable()
export class InferenceService {
  private readonly logger = new Logger(InferenceService.name);
  private readonly mockRequests: InferenceRequest[] = [];
  private readonly mockResults: InferenceResult[] = [];

  constructor() {}

  async createRequest(requestData: Partial<InferenceRequest>): Promise<InferenceRequest> {
    try {
      const newRequest = {
        id: `request-${Date.now()}`,
        requestId: requestData.requestId || `req-${Date.now()}`,
        modelId: requestData.modelId || 'default-model',
        deviceId: requestData.deviceId,
        inputData: requestData.inputData || {},
        priority: requestData.priority || 'medium',
        status: 'pending' as InferenceStatus,
        timeout: requestData.timeout || 5000,
        callback: requestData.callback,
        metadata: requestData.metadata,
        preferredDeviceId: requestData.preferredDeviceId,
        includeDetails: requestData.includeDetails || false,
        createdAt: new Date(),
        updatedAt: new Date(),
      } as InferenceRequest;

      this.mockRequests.push(newRequest);
      this.logger.log(`推理请求创建成功: ${newRequest.requestId}`);
      return newRequest;
    } catch (error) {
      this.logger.error('创建推理请求失败', error);
      throw error;
    }
  }

  async updateRequestStatus(
    requestId: string, 
    status: InferenceStatus, 
    updateData?: Partial<InferenceRequest>
  ): Promise<void> {
    try {
      const requestIndex = this.mockRequests.findIndex(r => r.requestId === requestId);
      
      if (requestIndex !== -1) {
        this.mockRequests[requestIndex] = {
          ...this.mockRequests[requestIndex],
          status,
          ...updateData,
          updatedAt: new Date(),
        };
        this.logger.log(`推理请求状态更新: ${requestId} -> ${status}`);
      }
    } catch (error) {
      this.logger.error('更新推理请求状态失败', error);
      throw error;
    }
  }

  async createResult(resultData: Partial<InferenceResult>): Promise<InferenceResult> {
    try {
      const newResult = {
        id: `result-${Date.now()}`,
        requestId: resultData.requestId || '',
        modelId: resultData.modelId || '',
        deviceId: resultData.deviceId || '',
        result: resultData.result || {},
        confidence: resultData.confidence,
        processingTime: resultData.processingTime || 0,
        metadata: resultData.metadata,
        performance: resultData.performance,
        details: resultData.details,
        createdAt: new Date(),
      } as InferenceResult;

      this.mockResults.push(newResult);
      this.logger.log(`推理结果创建成功: ${newResult.requestId}`);
      return newResult;
    } catch (error) {
      this.logger.error('创建推理结果失败', error);
      throw error;
    }
  }

  async findRequestByRequestId(requestId: string): Promise<InferenceRequest | null> {
    return this.mockRequests.find(r => r.requestId === requestId) || null;
  }

  async findResultByRequestId(requestId: string): Promise<InferenceResult | null> {
    return this.mockResults.find(r => r.requestId === requestId) || null;
  }

  async getStatistics(): Promise<any> {
    try {
      const total = this.mockRequests.length;
      const completed = this.mockRequests.filter(r => r.status === 'completed').length;
      const failed = this.mockRequests.filter(r => r.status === 'failed').length;
      const pending = this.mockRequests.filter(r => r.status === 'pending').length;

      return {
        total,
        completed,
        failed,
        pending,
        avgProcessingTime: 0,
        successRate: total > 0 ? (completed / total) * 100 : 0,
      };
    } catch (error) {
      this.logger.error('获取推理统计失败', error);
      throw error;
    }
  }

  async cleanupExpiredRequests(): Promise<void> {
    try {
      const now = new Date();
      const expiredThreshold = 24 * 60 * 60 * 1000; // 24小时

      const initialLength = this.mockRequests.length;
      
      // 移除过期的请求
      for (let i = this.mockRequests.length - 1; i >= 0; i--) {
        const request = this.mockRequests[i];
        if (request.createdAt && 
            (now.getTime() - request.createdAt.getTime()) > expiredThreshold) {
          this.mockRequests.splice(i, 1);
        }
      }

      const removedCount = initialLength - this.mockRequests.length;
      if (removedCount > 0) {
        this.logger.log(`清理过期推理请求: ${removedCount} 个`);
      }
    } catch (error) {
      this.logger.error('清理过期推理请求失败', error);
    }
  }
}

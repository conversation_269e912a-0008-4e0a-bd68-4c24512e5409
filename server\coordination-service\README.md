# 群体协调服务 (Coordination Service)

## 概述

群体协调服务是一个基于NestJS的微服务，专门用于管理和协调大规模群体行为。该服务提供群体形成、角色分配、冲突解决、资源调度等核心功能，支持复杂的多实体协调场景。

## 主要功能

### 🎯 核心功能
- **群体形成** - 基于空间邻近性、技能匹配等策略自动形成群体
- **角色分配** - 根据实体能力和需求分配合适的社交角色
- **冲突解决** - 通过调解、仲裁等方式解决实体间冲突
- **资源分配** - 公平、高效地分配有限资源
- **任务分发** - 智能分配和调度协调任务
- **通信中继** - 协调实体间的信息传递

### 🔧 技术特性
- **异步处理** - 支持高并发的任务处理
- **事件驱动** - 基于事件的松耦合架构
- **实时监控** - 任务状态和系统性能实时监控
- **容错机制** - 完善的错误处理和重试策略
- **可扩展性** - 支持自定义协调策略和算法

## 技术栈

- **框架**: NestJS 10.x
- **语言**: TypeScript 5.x
- **数据库**: Redis (缓存和消息队列)
- **消息**: EventEmitter2 (事件系统)
- **调度**: @nestjs/schedule (定时任务)
- **验证**: class-validator (数据验证)
- **测试**: Jest (单元测试)

## 快速开始

### 环境要求
- Node.js >= 18.0.0
- Redis >= 6.0.0
- npm >= 8.0.0

### 安装依赖
```bash
npm install
```

### 环境配置
创建 `.env` 文件：
```env
# 服务配置
NODE_ENV=development
PORT=3010
HOST=0.0.0.0

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# 微服务配置
MICROSERVICE_HOST=localhost
MICROSERVICE_PORT=3020

# CORS配置
CORS_ORIGIN=*

# 调试配置
DEBUG=false
```

### 启动服务
```bash
# 开发模式
npm run start:dev

# 生产模式
npm run build
npm run start:prod

# 调试模式
npm run start:debug
```

## API 接口

### 任务管理

#### 创建协调任务
```http
POST /api/v1/coordination/tasks
Content-Type: application/json

{
  "type": "group_formation",
  "entityIds": ["entity1", "entity2", "entity3"],
  "parameters": {
    "maxDistance": 10,
    "minGroupSize": 2
  },
  "priority": 1,
  "deadline": 1640995200000
}
```

#### 获取任务状态
```http
GET /api/v1/coordination/tasks/{taskId}
```

#### 获取任务列表
```http
GET /api/v1/coordination/tasks?status=pending&type=group_formation&limit=10&offset=0
```

### 群体管理

#### 群体形成
```http
POST /api/v1/coordination/groups/formation
Content-Type: application/json

{
  "entityIds": ["entity1", "entity2", "entity3"],
  "strategy": "proximity_grouping",
  "maxDistance": 10,
  "minGroupSize": 2,
  "maxGroupSize": 5,
  "priority": 2
}
```

#### 角色分配
```http
POST /api/v1/coordination/roles/assignment
Content-Type: application/json

{
  "entityIds": ["entity1", "entity2"],
  "groupId": "group123",
  "requiredRoles": ["leader", "participant"],
  "strategy": "capability_based",
  "priority": 2
}
```

### 冲突管理

#### 报告冲突
```http
POST /api/v1/coordination/conflicts/report
Content-Type: application/json

{
  "type": "resource_conflict",
  "participants": ["entity1", "entity2"],
  "severity": 0.8,
  "description": "资源争夺冲突",
  "location": {
    "x": 100,
    "y": 200,
    "z": 0
  }
}
```

#### 获取活跃冲突
```http
GET /api/v1/coordination/conflicts/active
```

### 资源管理

#### 资源分配
```http
POST /api/v1/coordination/resources/allocation
Content-Type: application/json

{
  "entityIds": ["entity1", "entity2", "entity3"],
  "resourceType": "computing_power",
  "amount": 100,
  "strategy": "fair_allocation",
  "priority": 2
}
```

#### 获取可用资源
```http
GET /api/v1/coordination/resources/available?type=computing_power
```

### 监控

#### 健康检查
```http
GET /api/v1/coordination/health
```

## 协调策略

### 群体形成策略
- **proximity_grouping** - 基于空间邻近性分组
- **skill_based** - 基于技能匹配分组
- **random** - 随机分组

### 角色分配策略
- **capability_based** - 基于能力分配角色
- **random** - 随机分配角色
- **rotation** - 轮换分配角色

### 冲突解决策略
- **mediation_resolution** - 调解解决
- **arbitration** - 仲裁解决
- **negotiation** - 协商解决

### 资源分配策略
- **fair_allocation** - 公平分配
- **priority_based** - 基于优先级分配
- **need_based** - 基于需求分配

## 开发指南

### 项目结构
```
src/
├── controllers/          # 控制器
│   ├── coordination.controller.ts
│   └── coordination.controller.spec.ts
├── services/            # 服务层
│   ├── group-coordination.service.ts
│   └── group-coordination.service.spec.ts
├── dto/                 # 数据传输对象
│   └── coordination.dto.ts
├── interfaces/          # 接口定义
│   └── coordination.interface.ts
├── coordination-service.module.ts  # 主模块
└── main.ts             # 应用入口
```

### 添加新的协调策略
1. 在 `GroupCoordinationService` 中实现策略接口
2. 在 `initializeStrategies()` 方法中注册策略
3. 添加相应的测试用例

### 运行测试
```bash
# 单元测试
npm run test

# 测试覆盖率
npm run test:cov

# 端到端测试
npm run test:e2e

# 监听模式
npm run test:watch
```

### 代码规范
```bash
# 代码格式化
npm run format

# 代码检查
npm run lint

# 自动修复
npm run lint:fix
```

## 部署

### Docker部署
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY dist ./dist
EXPOSE 3010
CMD ["node", "dist/main"]
```

### 构建镜像
```bash
npm run build
docker build -t coordination-service .
docker run -p 3010:3010 coordination-service
```

## 监控和日志

### 性能指标
- 任务处理速度
- 内存使用情况
- Redis连接状态
- 错误率统计

### 日志级别
- **INFO** - 一般信息
- **WARN** - 警告信息
- **ERROR** - 错误信息
- **DEBUG** - 调试信息

## 故障排除

### 常见问题
1. **Redis连接失败** - 检查Redis服务状态和配置
2. **任务处理缓慢** - 调整并发任务数量
3. **内存泄漏** - 检查任务清理机制
4. **端口冲突** - 修改服务端口配置

### 调试技巧
- 启用DEBUG模式查看详细日志
- 使用健康检查接口监控服务状态
- 检查Redis中的任务队列状态

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

MIT License

## 联系方式

如有问题或建议，请联系开发团队。

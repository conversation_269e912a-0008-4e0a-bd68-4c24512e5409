import { Injectable, Logger, NotFoundException } from '@nestjs/common';

@Injectable()
export class ApiPlatformService {
  private readonly logger = new Logger(ApiPlatformService.name);
  private apis: Map<string, any> = new Map();
  private apiUsage: Map<string, any> = new Map();

  async getAPIs(filters: any = {}) {
    const allAPIs = Array.from(this.apis.values());
    
    let filteredAPIs = allAPIs;
    
    if (filters.type) {
      filteredAPIs = filteredAPIs.filter(api => api.type === filters.type);
    }
    
    if (filters.status) {
      filteredAPIs = filteredAPIs.filter(api => api.status === filters.status);
    }
    
    return {
      apis: filteredAPIs,
      total: filteredAPIs.length,
      filters
    };
  }

  async getAPI(id: string) {
    const api = this.apis.get(id);
    if (!api) {
      throw new NotFoundException(`API ${id} 不存在`);
    }
    return api;
  }

  async publishAPI(apiSpec: any) {
    const apiId = `api_${Date.now()}`;
    
    const newAPI = {
      ...apiSpec,
      apiId,
      status: 'published',
      createdAt: new Date(),
      updatedAt: new Date(),
      version: apiSpec.version || '1.0.0'
    };
    
    this.apis.set(apiId, newAPI);
    
    // 初始化使用统计
    this.apiUsage.set(apiId, {
      totalCalls: 0,
      successfulCalls: 0,
      errorCalls: 0,
      avgResponseTime: 0,
      lastCall: null
    });
    
    this.logger.log(`API发布成功: ${apiId} - ${apiSpec.name}`);
    
    return {
      apiId,
      status: 'published',
      message: 'API发布成功'
    };
  }

  async updateAPI(id: string, updateData: any) {
    const api = this.apis.get(id);
    if (!api) {
      throw new NotFoundException(`API ${id} 不存在`);
    }
    
    const updatedAPI = {
      ...api,
      ...updateData,
      updatedAt: new Date()
    };
    
    this.apis.set(id, updatedAPI);
    
    this.logger.log(`API更新: ${id}`);
    
    return updatedAPI;
  }

  async deleteAPI(id: string) {
    const api = this.apis.get(id);
    if (!api) {
      throw new NotFoundException(`API ${id} 不存在`);
    }
    
    this.apis.delete(id);
    this.apiUsage.delete(id);
    
    this.logger.log(`API删除: ${id}`);
    
    return {
      message: 'API已删除',
      deletedId: id
    };
  }

  async getAPIUsage(id: string) {
    const api = this.apis.get(id);
    if (!api) {
      throw new NotFoundException(`API ${id} 不存在`);
    }
    
    const usage = this.apiUsage.get(id) || {
      totalCalls: 0,
      successfulCalls: 0,
      errorCalls: 0,
      avgResponseTime: 0,
      lastCall: null
    };
    
    return {
      apiId: id,
      usage,
      metrics: {
        successRate: usage.totalCalls > 0 ? (usage.successfulCalls / usage.totalCalls) * 100 : 0,
        errorRate: usage.totalCalls > 0 ? (usage.errorCalls / usage.totalCalls) * 100 : 0,
        avgResponseTime: usage.avgResponseTime
      }
    };
  }

  async getAPIDocumentation(id: string) {
    const api = this.apis.get(id);
    if (!api) {
      throw new NotFoundException(`API ${id} 不存在`);
    }
    
    return {
      apiId: id,
      name: api.name,
      version: api.version,
      description: api.description,
      documentation: api.documentation || {
        overview: '暂无概述',
        getting_started: '暂无快速开始指南',
        tutorials: [],
        sdk_links: [],
        changelog: []
      },
      endpoints: api.endpoints || [],
      authentication: api.authentication || {},
      rateLimit: api.rateLimit || {}
    };
  }
}

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

export enum LearningAlgorithm {
  FEDERATED_LEARNING = 'federated_learning',
  DISTRIBUTED_TRAINING = 'distributed_training',
  ENSEMBLE_LEARNING = 'ensemble_learning'
}

export enum LearningTaskStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

@Entity('learning_tasks')
@Index(['status'])
@Index(['algorithm'])
@Index(['createdAt'])
export class LearningTask {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ unique: true })
  taskId: string;

  @Column()
  name: string;

  @Column({
    type: 'enum',
    enum: LearningAlgorithm,
    default: LearningAlgorithm.FEDERATED_LEARNING,
  })
  algorithm: LearningAlgorithm;

  @Column({
    type: 'enum',
    enum: LearningTaskStatus,
    default: LearningTaskStatus.PENDING,
  })
  status: LearningTaskStatus;

  @Column({ type: 'json' })
  participants: string[];

  @Column({ type: 'json' })
  modelTemplate: any;

  @Column({ default: 'federated_averaging' })
  aggregationStrategy: string;

  @Column({ type: 'int', default: 10 })
  rounds: number;

  @Column({ type: 'int', default: 0 })
  currentRound: number;

  @Column({ type: 'decimal', precision: 10, scale: 6, nullable: true })
  learningRate: number;

  @Column({ type: 'int', nullable: true })
  batchSize: number;

  @Column({ type: 'int', nullable: true })
  localEpochs: number;

  @Column({ type: 'int', nullable: true })
  minParticipants: number;

  @Column({ type: 'decimal', precision: 10, scale: 6, nullable: true })
  convergenceThreshold: number;

  @Column({ type: 'json', nullable: true })
  privacyConfig: {
    differentialPrivacy?: boolean;
    noiseLevel?: number;
    secureAggregation?: boolean;
  };

  @Column({ type: 'json', nullable: true })
  dataDistribution: {
    iid?: boolean;
    skewness?: number;
    sampleRatio?: number;
  };

  @Column({ type: 'json', nullable: true })
  results: {
    finalAccuracy?: number;
    convergenceRound?: number;
    totalTrainingTime?: number;
    participantResults?: any[];
  };

  @Column({ type: 'json', nullable: true })
  progress: {
    roundResults?: any[];
    aggregationHistory?: any[];
    performanceMetrics?: any[];
  };

  @Column({ type: 'timestamp', nullable: true })
  startTime: Date;

  @Column({ type: 'timestamp', nullable: true })
  endTime: Date;

  @Column({ type: 'text', nullable: true })
  errorMessage: string;

  @Column({ type: 'json', nullable: true })
  metadata: any;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

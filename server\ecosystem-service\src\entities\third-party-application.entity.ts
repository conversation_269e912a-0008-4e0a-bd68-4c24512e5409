import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Index,
} from 'typeorm';
import { ApplicationReview } from './application-review.entity';

export enum ApplicationType {
  WEB_APPLICATION = 'web_application',
  MOBILE_APPLICATION = 'mobile_application',
  DESKTOP_APPLICATION = 'desktop_application',
  IOT_APPLICATION = 'iot_application',
  AI_MODEL = 'ai_model',
  CONNECTOR = 'connector',
  WIDGET = 'widget',
  PLUGIN = 'plugin'
}

export enum ApplicationStatus {
  PUBLISHED = 'published',
  DRAFT = 'draft',
  REVIEW = 'review',
  REJECTED = 'rejected',
  DEPRECATED = 'deprecated'
}

export enum PricingType {
  FREE = 'free',
  FREEMIUM = 'freemium',
  SUBSCRIPTION = 'subscription',
  ONE_TIME = 'one_time',
  USAGE_BASED = 'usage_based'
}

@Entity('third_party_applications')
@Index(['type', 'category', 'status'])
export class ThirdPartyApplication {
  @PrimaryGeneratedColumn('uuid')
  appId: string;

  @Column({ length: 255 })
  @Index()
  name: string;

  @Column({
    type: 'enum',
    enum: ApplicationType,
  })
  type: ApplicationType;

  @Column({ length: 255 })
  developer: string;

  @Column('text')
  description: string;

  @Column({ length: 100 })
  @Index()
  category: string;

  @Column({ length: 50 })
  version: string;

  @Column({
    type: 'enum',
    enum: ApplicationStatus,
    default: ApplicationStatus.DRAFT,
  })
  @Index()
  status: ApplicationStatus;

  // 定价模型 - 存储为JSON
  @Column('json', { nullable: true })
  pricing: {
    type: PricingType;
    price: number;
    currency: string;
    billing_period?: 'monthly' | 'yearly';
    free_tier?: {
      limitations: string[];
      duration?: number; // 天
    };
    usage_tiers?: Array<{
      name: string;
      min_usage: number;
      max_usage: number;
      price_per_unit: number;
    }>;
  };

  // 应用功能 - 存储为JSON
  @Column('json', { nullable: true })
  features: Array<{
    name: string;
    description: string;
    category: string;
    premium: boolean;
  }>;

  // 集成信息 - 存储为JSON
  @Column('json', { nullable: true })
  integrations: Array<{
    system: string;
    type: 'api' | 'webhook' | 'file' | 'database';
    description: string;
    configuration: any;
  }>;

  // 应用指标 - 存储为JSON
  @Column('json', { nullable: true })
  metrics: {
    downloads: number;
    active_users: number;
    average_rating: number;
    total_reviews: number;
    revenue: number;
    support_tickets: number;
    last_updated: Date;
  };

  @Column({ length: 500, nullable: true })
  iconUrl: string;

  @Column('json', { nullable: true })
  screenshots: string[];

  @Column({ length: 500, nullable: true })
  websiteUrl: string;

  @Column({ length: 500, nullable: true })
  supportUrl: string;

  @Column({ length: 500, nullable: true })
  privacyPolicyUrl: string;

  @Column({ length: 500, nullable: true })
  termsOfServiceUrl: string;

  @Column('json', { nullable: true })
  tags: string[];

  @Column({ type: 'timestamp', nullable: true })
  publishedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  lastUpdated: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 关联关系
  @OneToMany(() => ApplicationReview, review => review.application)
  reviews: ApplicationReview[];
}

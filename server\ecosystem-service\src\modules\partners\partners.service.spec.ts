import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NotFoundException } from '@nestjs/common';
import { PartnersService } from './partners.service';
import { Partner, PartnerCertification, PartnerAgreement, PartnerStatus, PartnerType, PartnerTier } from '../../entities';
import { CacheService } from '../../common/redis/cache.service';
import { createMockRepository, createMockCacheService } from '../../test-utils/test-helpers';

describe('PartnersService', () => {
  let service: PartnersService;
  let partnerRepository: jest.Mocked<Repository<Partner>>;
  let certificationRepository: jest.Mocked<Repository<PartnerCertification>>;
  let agreementRepository: jest.Mocked<Repository<PartnerAgreement>>;
  let cacheService: jest.Mocked<CacheService>;

  const mockPartner: Partial<Partner> = {
    partnerId: 'test-partner-id',
    name: 'Test Partner',
    type: PartnerType.TECHNOLOGY_PROVIDER,
    tier: PartnerTier.GOLD,
    status: PartnerStatus.ACTIVE,
    description: 'Test partner description',
    contactInfo: {
      primaryContact: {
        name: 'John Doe',
        email: '<EMAIL>',
        phone: '+**********',
        role: 'Manager',
      },
    },
    capabilities: {
      technologies: ['Node.js', 'React'],
      industries: ['Technology'],
      regions: ['North America'],
      languages: ['English'],
      certifications: ['ISO 9001'],
      specializations: ['Web Development'],
      supportLevels: ['Basic', 'Premium'],
    },
    performance: {
      revenue: 1000000,
      customers: 50,
      projects: 25,
      satisfaction: 95,
      support_rating: 4.5,
      certification_compliance: 100,
      last_evaluation: new Date(),
    },
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PartnersService,
        {
          provide: getRepositoryToken(Partner),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(PartnerCertification),
          useValue: createMockRepository(),
        },
        {
          provide: getRepositoryToken(PartnerAgreement),
          useValue: createMockRepository(),
        },
        {
          provide: CacheService,
          useValue: createMockCacheService(),
        },
      ],
    }).compile();

    service = module.get<PartnersService>(PartnersService);
    partnerRepository = module.get(getRepositoryToken(Partner));
    certificationRepository = module.get(getRepositoryToken(PartnerCertification));
    agreementRepository = module.get(getRepositoryToken(PartnerAgreement));
    cacheService = module.get(CacheService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getPartners', () => {
    it('should return partners from cache if available', async () => {
      const cachedResult = {
        partners: [mockPartner],
        total: 1,
        filters: {},
      };

      cacheService.getPartnersListKey.mockReturnValue('cache-key');
      cacheService.get.mockResolvedValue(cachedResult);

      const result = await service.getPartners({});

      expect(cacheService.get).toHaveBeenCalledWith('cache-key');
      expect(result).toEqual(cachedResult);
      expect(partnerRepository.findAndCount).not.toHaveBeenCalled();
    });

    it('should fetch partners from database and cache result', async () => {
      const filters = { type: PartnerType.TECHNOLOGY_PROVIDER };
      const partners = [mockPartner];
      const total = 1;

      cacheService.getPartnersListKey.mockReturnValue('cache-key');
      cacheService.get.mockResolvedValue(null);
      partnerRepository.findAndCount.mockResolvedValue([partners as Partner[], total]);
      cacheService.set.mockResolvedValue(true);

      const result = await service.getPartners(filters);

      expect(partnerRepository.findAndCount).toHaveBeenCalledWith({
        relations: ['certifications', 'agreements'],
        order: { createdAt: 'DESC' },
        where: { type: PartnerType.TECHNOLOGY_PROVIDER },
      });
      expect(cacheService.set).toHaveBeenCalledWith('cache-key', result, 300);
      expect(result).toEqual({
        partners,
        total,
        filters,
      });
    });

    it('should handle search filter', async () => {
      const filters = { search: 'Test' };

      cacheService.getPartnersListKey.mockReturnValue('cache-key');
      cacheService.get.mockResolvedValue(null);
      partnerRepository.findAndCount.mockResolvedValue([[], 0]);

      await service.getPartners(filters);

      expect(partnerRepository.findAndCount).toHaveBeenCalledWith({
        relations: ['certifications', 'agreements'],
        order: { createdAt: 'DESC' },
        where: { name: expect.objectContaining({}) }, // Like operator
      });
    });
  });

  describe('getPartner', () => {
    it('should return partner from cache if available', async () => {
      const partnerId = 'test-partner-id';

      cacheService.getPartnerKey.mockReturnValue('cache-key');
      cacheService.get.mockResolvedValue(mockPartner);

      const result = await service.getPartner(partnerId);

      expect(cacheService.get).toHaveBeenCalledWith('cache-key');
      expect(result).toEqual(mockPartner);
      expect(partnerRepository.findOne).not.toHaveBeenCalled();
    });

    it('should fetch partner from database and cache result', async () => {
      const partnerId = 'test-partner-id';

      cacheService.getPartnerKey.mockReturnValue('cache-key');
      cacheService.get.mockResolvedValue(null);
      partnerRepository.findOne.mockResolvedValue(mockPartner as Partner);
      cacheService.set.mockResolvedValue(true);

      const result = await service.getPartner(partnerId);

      expect(partnerRepository.findOne).toHaveBeenCalledWith({
        where: { partnerId },
        relations: ['certifications', 'agreements'],
      });
      expect(cacheService.set).toHaveBeenCalledWith('cache-key', mockPartner, 600);
      expect(result).toEqual(mockPartner);
    });

    it('should throw NotFoundException if partner not found', async () => {
      const partnerId = 'non-existent-id';

      cacheService.getPartnerKey.mockReturnValue('cache-key');
      cacheService.get.mockResolvedValue(null);
      partnerRepository.findOne.mockResolvedValue(null);

      await expect(service.getPartner(partnerId)).rejects.toThrow(NotFoundException);
    });
  });

  describe('createApplication', () => {
    it('should create a new partner application', async () => {
      const application = {
        name: 'New Partner',
        type: PartnerType.TECHNOLOGY_PROVIDER,
        description: 'New partner description',
        contactInfo: mockPartner.contactInfo,
      };

      const savedPartner = { ...mockPartner, ...application };
      partnerRepository.create.mockReturnValue(savedPartner as Partner);
      partnerRepository.save.mockResolvedValue(savedPartner as Partner);

      const result = await service.createApplication(application);

      expect(partnerRepository.create).toHaveBeenCalledWith({
        ...application,
        status: PartnerStatus.PENDING,
        joinedAt: expect.any(Date),
        lastActivity: expect.any(Date),
      });
      expect(partnerRepository.save).toHaveBeenCalledWith(savedPartner);
      expect(result).toEqual({
        partnerId: savedPartner.partnerId,
        status: 'pending',
        message: '申请已提交，等待审核',
      });
    });
  });

  describe('updatePartner', () => {
    it('should update partner and clear cache', async () => {
      const partnerId = 'test-partner-id';
      const updateData = { name: 'Updated Partner Name' };
      const existingPartner = { ...mockPartner };
      const updatedPartner = { ...existingPartner, ...updateData };

      partnerRepository.findOne.mockResolvedValue(existingPartner as Partner);
      partnerRepository.save.mockResolvedValue(updatedPartner as Partner);
      cacheService.getPartnerKey.mockReturnValue('partner-cache-key');
      cacheService.del.mockResolvedValue(true);
      cacheService.delPattern.mockResolvedValue(5);

      const result = await service.updatePartner(partnerId, updateData);

      expect(partnerRepository.findOne).toHaveBeenCalledWith({
        where: { partnerId },
      });
      expect(partnerRepository.save).toHaveBeenCalled();
      expect(cacheService.del).toHaveBeenCalledWith('partner-cache-key');
      expect(cacheService.delPattern).toHaveBeenCalledWith('ecosystem:partners:list:*');
      expect(result).toEqual(updatedPartner);
    });

    it('should throw NotFoundException if partner not found', async () => {
      const partnerId = 'non-existent-id';
      const updateData = { name: 'Updated Name' };

      partnerRepository.findOne.mockResolvedValue(null);

      await expect(service.updatePartner(partnerId, updateData)).rejects.toThrow(NotFoundException);
    });
  });

  describe('deletePartner', () => {
    it('should delete partner successfully', async () => {
      const partnerId = 'test-partner-id';

      partnerRepository.findOne.mockResolvedValue(mockPartner as Partner);
      partnerRepository.remove.mockResolvedValue(mockPartner as Partner);

      const result = await service.deletePartner(partnerId);

      expect(partnerRepository.findOne).toHaveBeenCalledWith({
        where: { partnerId },
      });
      expect(partnerRepository.remove).toHaveBeenCalledWith(mockPartner);
      expect(result).toEqual({
        message: '合作伙伴已删除',
        deletedId: partnerId,
      });
    });

    it('should throw NotFoundException if partner not found', async () => {
      const partnerId = 'non-existent-id';

      partnerRepository.findOne.mockResolvedValue(null);

      await expect(service.deletePartner(partnerId)).rejects.toThrow(NotFoundException);
    });
  });

  describe('getPartnerPerformance', () => {
    it('should return partner performance data', async () => {
      const partnerId = 'test-partner-id';

      partnerRepository.findOne.mockResolvedValue(mockPartner as Partner);

      const result = await service.getPartnerPerformance(partnerId);

      expect(result).toEqual({
        partnerId,
        performance: mockPartner.performance,
        trends: {
          revenue_trend: 'stable',
          customer_trend: 'growing',
          satisfaction_trend: 'improving',
        },
      });
    });

    it('should return default performance if not set', async () => {
      const partnerId = 'test-partner-id';
      const partnerWithoutPerformance = { ...mockPartner, performance: undefined };

      partnerRepository.findOne.mockResolvedValue(partnerWithoutPerformance as Partner);

      const result = await service.getPartnerPerformance(partnerId);

      expect(result.performance).toEqual({
        revenue: 0,
        customers: 0,
        projects: 0,
        satisfaction: 0,
        support_rating: 0,
        certification_compliance: 0,
        last_evaluation: expect.any(Date),
      });
    });
  });

  describe('addCertification', () => {
    it('should add certification to partner', async () => {
      const partnerId = 'test-partner-id';
      const certification = {
        name: 'ISO 27001',
        issuer: 'ISO',
        level: 'Standard',
      };
      const savedCertification = {
        certificationId: 'cert-id',
        ...certification,
        partnerId,
      };

      partnerRepository.findOne.mockResolvedValue(mockPartner as Partner);
      certificationRepository.create.mockReturnValue(savedCertification as PartnerCertification);
      certificationRepository.save.mockResolvedValue(savedCertification as PartnerCertification);

      const result = await service.addCertification(partnerId, certification);

      expect(certificationRepository.create).toHaveBeenCalledWith({
        ...certification,
        partnerId,
        validFrom: expect.any(Date),
        validTo: expect.any(Date),
      });
      expect(certificationRepository.save).toHaveBeenCalledWith(savedCertification);
      expect(result).toEqual({
        certificationId: savedCertification.certificationId,
        message: '认证添加成功',
      });
    });

    it('should throw NotFoundException if partner not found', async () => {
      const partnerId = 'non-existent-id';
      const certification = { name: 'Test Cert' };

      partnerRepository.findOne.mockResolvedValue(null);

      await expect(service.addCertification(partnerId, certification)).rejects.toThrow(NotFoundException);
    });
  });
});

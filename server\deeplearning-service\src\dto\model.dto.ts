/**
 * 模型相关数据传输对象
 */

import { IsString, IsNumber, IsOptional, IsObject, IsEnum, IsUrl, Min } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * 加载模型DTO
 */
export class LoadModelDto {
  @ApiProperty({ description: '模型ID', example: 'model_001' })
  @IsString()
  id: string;

  @ApiProperty({ description: '模型名称', example: 'Text Classifier v1.0' })
  @IsString()
  name: string;

  @ApiProperty({ 
    description: '模型类型', 
    example: 'decision_making',
    enum: ['decision_making', 'perception', 'language', 'emotion', 'classification', 'regression']
  })
  @IsEnum(['decision_making', 'perception', 'language', 'emotion', 'classification', 'regression'])
  type: string;

  @ApiProperty({ description: '模型版本', example: '1.0.0' })
  @IsString()
  version: string;

  @ApiPropertyOptional({ description: '模型文件路径', example: '/models/text_classifier.onnx' })
  @IsOptional()
  @IsString()
  path?: string;

  @ApiPropertyOptional({ description: '模型下载URL', example: 'https://example.com/models/text_classifier.onnx' })
  @IsOptional()
  @IsUrl()
  url?: string;

  @ApiPropertyOptional({ description: '模型配置', example: { inputShape: [1, 512], outputShape: [1, 10] } })
  @IsOptional()
  @IsObject()
  config?: any;

  @ApiPropertyOptional({ description: '模型描述', example: '用于文本分类的BERT模型' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: '模型标签', example: ['nlp', 'classification', 'bert'] })
  @IsOptional()
  tags?: string[];
}

/**
 * 更新模型DTO
 */
export class UpdateModelDto {
  @ApiPropertyOptional({ description: '模型名称', example: 'Text Classifier v1.1' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: '模型描述', example: '更新的文本分类模型' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: '模型配置', example: { batchSize: 32 } })
  @IsOptional()
  @IsObject()
  config?: any;

  @ApiPropertyOptional({ description: '模型标签', example: ['nlp', 'classification', 'updated'] })
  @IsOptional()
  tags?: string[];

  @ApiPropertyOptional({ description: '是否启用', example: true })
  @IsOptional()
  enabled?: boolean;
}

/**
 * 模型信息DTO
 */
export class ModelInfoDto {
  @ApiProperty({ description: '模型ID', example: 'model_001' })
  id: string;

  @ApiProperty({ description: '模型名称', example: 'Text Classifier v1.0' })
  name: string;

  @ApiProperty({ description: '模型类型', example: 'decision_making' })
  type: string;

  @ApiProperty({ description: '模型版本', example: '1.0.0' })
  version: string;

  @ApiProperty({ 
    description: '模型状态', 
    example: 'ready',
    enum: ['loading', 'ready', 'error', 'unloaded']
  })
  status: 'loading' | 'ready' | 'error' | 'unloaded';

  @ApiProperty({ description: '加载时间 (毫秒)', example: 2500 })
  loadTime: number;

  @ApiProperty({ description: '最后使用时间', example: 1640995200000 })
  lastUsed: number;

  @ApiProperty({ description: '使用次数', example: 150 })
  usageCount: number;

  @ApiProperty({ description: '内存使用 (MB)', example: 256.5 })
  memoryUsage: number;

  @ApiProperty({ description: '模型配置', example: { inputShape: [1, 512] } })
  config: any;

  @ApiPropertyOptional({ description: '模型描述', example: '用于文本分类的BERT模型' })
  description?: string;

  @ApiPropertyOptional({ description: '模型标签', example: ['nlp', 'classification'] })
  tags?: string[];

  @ApiPropertyOptional({ description: '创建时间', example: 1640995200000 })
  createdAt?: number;

  @ApiPropertyOptional({ description: '更新时间', example: 1640995200000 })
  updatedAt?: number;
}

/**
 * 模型指标DTO
 */
export class ModelMetricsDto {
  @ApiProperty({ description: '模型ID', example: 'model_001' })
  modelId: string;

  @ApiProperty({ description: '总请求数', example: 1000 })
  totalRequests: number;

  @ApiProperty({ description: '成功请求数', example: 950 })
  successfulRequests: number;

  @ApiProperty({ description: '失败请求数', example: 50 })
  failedRequests: number;

  @ApiProperty({ description: '平均延迟 (毫秒)', example: 125.5 })
  averageLatency: number;

  @ApiProperty({ description: '吞吐量 (请求/分钟)', example: 120.5 })
  throughput: number;

  @ApiProperty({ description: '错误率', example: 0.05 })
  errorRate: number;

  @ApiProperty({ description: '最近一小时请求数', example: 120 })
  lastHourRequests: number;

  @ApiProperty({ description: '峰值延迟 (毫秒)', example: 500 })
  peakLatency: number;

  @ApiProperty({ description: '最小延迟 (毫秒)', example: 50 })
  minLatency: number;

  @ApiPropertyOptional({ description: '最后更新时间', example: 1640995200000 })
  lastUpdated?: number;
}

/**
 * 模型列表查询DTO
 */
export class ModelListQueryDto {
  @ApiPropertyOptional({ description: '状态过滤', example: 'ready' })
  @IsOptional()
  @IsEnum(['loading', 'ready', 'error', 'unloaded'])
  status?: 'loading' | 'ready' | 'error' | 'unloaded';

  @ApiPropertyOptional({ description: '类型过滤', example: 'decision_making' })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiPropertyOptional({ description: '搜索关键词', example: 'classifier' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ description: '标签过滤', example: 'nlp' })
  @IsOptional()
  @IsString()
  tag?: string;

  @ApiPropertyOptional({ description: '排序字段', example: 'lastUsed' })
  @IsOptional()
  @IsEnum(['name', 'type', 'lastUsed', 'usageCount', 'createdAt'])
  sortBy?: 'name' | 'type' | 'lastUsed' | 'usageCount' | 'createdAt';

  @ApiPropertyOptional({ description: '排序方向', example: 'desc' })
  @IsOptional()
  @IsEnum(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc';

  @ApiPropertyOptional({ description: '限制数量', example: 10, minimum: 1, maximum: 100 })
  @IsOptional()
  @IsNumber()
  @Min(1)
  limit?: number;

  @ApiPropertyOptional({ description: '偏移量', example: 0, minimum: 0 })
  @IsOptional()
  @IsNumber()
  @Min(0)
  offset?: number;
}

/**
 * 模型性能DTO
 */
export class ModelPerformanceDto {
  @ApiProperty({ description: '模型ID', example: 'model_001' })
  modelId: string;

  @ApiProperty({ description: '模型名称', example: 'Text Classifier' })
  modelName: string;

  @ApiProperty({ description: '时间范围开始', example: 1640995200000 })
  timeRangeStart: number;

  @ApiProperty({ description: '时间范围结束', example: 1640995200000 })
  timeRangeEnd: number;

  @ApiProperty({ description: '请求统计', example: { total: 1000, success: 950, failed: 50 } })
  requestStats: {
    total: number;
    success: number;
    failed: number;
  };

  @ApiProperty({ description: '延迟统计 (毫秒)', example: { avg: 125, min: 50, max: 500, p95: 200 } })
  latencyStats: {
    avg: number;
    min: number;
    max: number;
    p95: number;
  };

  @ApiProperty({ description: '吞吐量统计', example: { avg: 120, peak: 200 } })
  throughputStats: {
    avg: number;
    peak: number;
  };

  @ApiProperty({ description: '资源使用统计', example: { avgMemory: 256, peakMemory: 300 } })
  resourceStats: {
    avgMemory: number;
    peakMemory: number;
    avgCpu?: number;
    peakCpu?: number;
  };
}

/**
 * 模型版本DTO
 */
export class ModelVersionDto {
  @ApiProperty({ description: '版本号', example: '1.0.0' })
  version: string;

  @ApiProperty({ description: '发布时间', example: 1640995200000 })
  releaseDate: number;

  @ApiProperty({ description: '版本描述', example: '初始版本' })
  description: string;

  @ApiProperty({ description: '文件大小 (字节)', example: 104857600 })
  fileSize: number;

  @ApiProperty({ description: '文件哈希', example: 'sha256:abc123...' })
  fileHash: string;

  @ApiProperty({ description: '是否当前版本', example: true })
  isCurrent: boolean;

  @ApiPropertyOptional({ description: '变更日志', example: ['添加新功能', '修复bug'] })
  changelog?: string[];
}

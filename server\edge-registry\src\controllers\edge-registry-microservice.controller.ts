import { Controller, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';

import { EdgeRegistryService } from '../services/edge-registry.service';
import { RegisterNodeDto } from '../dto/register-node.dto';
import { UpdateHeartbeatDto } from '../dto/update-node.dto';
import { SelectOptimalNodeDto } from '../dto/select-node.dto';

/**
 * 边缘注册中心微服务控制器
 * 处理来自其他微服务的消息
 */
@Controller()
export class EdgeRegistryMicroserviceController {
  private readonly logger = new Logger(EdgeRegistryMicroserviceController.name);

  constructor(
    private readonly edgeRegistryService: EdgeRegistryService,
  ) {}

  /**
   * 处理节点注册消息
   */
  @MessagePattern('edge.register')
  async handleRegisterNode(@Payload() data: RegisterNodeDto) {
    try {
      this.logger.log(`收到节点注册消息: ${data.nodeId}`);
      const node = await this.edgeRegistryService.registerNode(data);
      return {
        success: true,
        data: node,
        message: '节点注册成功',
      };
    } catch (error) {
      this.logger.error(`处理节点注册消息失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        message: '节点注册失败',
      };
    }
  }

  /**
   * 处理节点注销消息
   */
  @MessagePattern('edge.unregister')
  async handleUnregisterNode(@Payload() data: { nodeId: string }) {
    try {
      this.logger.log(`收到节点注销消息: ${data.nodeId}`);
      const success = await this.edgeRegistryService.unregisterNode(data.nodeId);
      return {
        success,
        message: success ? '节点注销成功' : '节点不存在',
      };
    } catch (error) {
      this.logger.error(`处理节点注销消息失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        message: '节点注销失败',
      };
    }
  }

  /**
   * 处理心跳更新消息
   */
  @MessagePattern('edge.heartbeat')
  async handleHeartbeat(@Payload() data: { nodeId: string; metrics?: any }) {
    try {
      const success = await this.edgeRegistryService.updateHeartbeat(data.nodeId, data.metrics);
      return {
        success,
        message: success ? '心跳更新成功' : '节点不存在',
      };
    } catch (error) {
      this.logger.error(`处理心跳消息失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        message: '心跳更新失败',
      };
    }
  }

  /**
   * 处理选择最优节点消息
   */
  @MessagePattern('edge.select-optimal')
  async handleSelectOptimalNode(@Payload() data: SelectOptimalNodeDto) {
    try {
      this.logger.log(`收到选择最优节点消息`);
      const node = await this.edgeRegistryService.getOptimalNode(
        data.clientLocation,
        data.strategy,
        data.region,
      );
      
      return {
        success: !!node,
        data: node,
        message: node ? '选择最优节点成功' : '没有可用节点',
      };
    } catch (error) {
      this.logger.error(`处理选择最优节点消息失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        message: '选择最优节点失败',
      };
    }
  }

  /**
   * 处理获取所有节点消息
   */
  @MessagePattern('edge.get-all-nodes')
  async handleGetAllNodes(@Payload() data: { region?: string }) {
    try {
      const nodes = data.region 
        ? this.edgeRegistryService.getNodesByRegion(data.region)
        : this.edgeRegistryService.getAllNodes();
      
      return {
        success: true,
        data: nodes,
        message: '获取节点列表成功',
      };
    } catch (error) {
      this.logger.error(`处理获取节点列表消息失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        message: '获取节点列表失败',
      };
    }
  }

  /**
   * 处理获取节点信息消息
   */
  @MessagePattern('edge.get-node')
  async handleGetNode(@Payload() data: { nodeId: string }) {
    try {
      const node = this.edgeRegistryService.getNode(data.nodeId);
      return {
        success: !!node,
        data: node,
        message: node ? '获取节点信息成功' : '节点不存在',
      };
    } catch (error) {
      this.logger.error(`处理获取节点信息消息失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        message: '获取节点信息失败',
      };
    }
  }

  /**
   * 处理获取集群统计消息
   */
  @MessagePattern('edge.get-stats')
  async handleGetStats() {
    try {
      const stats = this.edgeRegistryService.getClusterStats();
      const availableRegions = this.edgeRegistryService.getAvailableRegions();
      
      return {
        success: true,
        data: {
          ...stats,
          availableRegions,
        },
        message: '获取集群统计成功',
      };
    } catch (error) {
      this.logger.error(`处理获取集群统计消息失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        message: '获取集群统计失败',
      };
    }
  }

  /**
   * 处理健康检查消息
   */
  @MessagePattern('edge.health-check')
  async handleHealthCheck() {
    try {
      const stats = this.edgeRegistryService.getClusterStats();
      return {
        success: true,
        data: {
          status: 'healthy',
          timestamp: new Date().toISOString(),
          totalNodes: stats.totalNodes,
          onlineNodes: stats.onlineNodes,
        },
        message: '边缘注册中心健康',
      };
    } catch (error) {
      this.logger.error(`处理健康检查消息失败: ${error.message}`, error.stack);
      return {
        success: false,
        error: error.message,
        message: '边缘注册中心不健康',
      };
    }
  }
}

import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-custom';
import { AuthService, User } from '../auth.service';

@Injectable()
export class ApiKeyStrategy extends PassportStrategy(Strategy, 'api-key') {
  constructor(private readonly authService: AuthService) {
    super();
  }

  async validate(req: any): Promise<User> {
    const apiKey = this.extractApiKey(req);
    
    if (!apiKey) {
      throw new UnauthorizedException('No API key provided');
    }

    const apiKeyInfo = await this.authService.validateApiKey(apiKey);
    
    if (!apiKeyInfo) {
      throw new UnauthorizedException('Invalid API key');
    }

    // 构造用户对象
    const user: User = {
      id: apiKeyInfo.userId,
      username: `api-key-${apiKeyInfo.keyId}`,
      email: '',
      roles: apiKeyInfo.roles,
      permissions: apiKeyInfo.permissions,
    };

    // 将API密钥信息附加到请求对象
    req.apiKeyInfo = apiKeyInfo;

    return user;
  }

  private extractApiKey(req: any): string | null {
    // 从多个位置尝试提取API密钥
    
    // 1. 从 Authorization 头中提取 (Bearer token 格式)
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    // 2. 从 X-API-Key 头中提取
    const apiKeyHeader = req.headers['x-api-key'];
    if (apiKeyHeader) {
      return apiKeyHeader;
    }

    // 3. 从查询参数中提取
    const apiKeyQuery = req.query.api_key;
    if (apiKeyQuery) {
      return apiKeyQuery;
    }

    return null;
  }
}

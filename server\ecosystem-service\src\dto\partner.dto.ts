import { IsString, <PERSON><PERSON><PERSON>, Is<PERSON><PERSON>al, IsEmail, IsUrl, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum PartnerType {
  TECHNOLOGY_PROVIDER = 'technology_provider',
  SYSTEM_INTEGRATOR = 'system_integrator',
  SOLUTION_PROVIDER = 'solution_provider',
  HARDWARE_VENDOR = 'hardware_vendor',
  SOFTWARE_VENDOR = 'software_vendor',
  CONSULTING_FIRM = 'consulting_firm',
  RESEARCH_INSTITUTE = 'research_institute',
  INDUSTRY_ASSOCIATION = 'industry_association'
}

export enum PartnerTier {
  PLATINUM = 'platinum',
  GOLD = 'gold',
  SILVER = 'silver',
  BRONZE = 'bronze',
  CERTIFIED = 'certified'
}

export class ContactDto {
  @ApiProperty({ description: '联系人姓名' })
  @IsString()
  name: string;

  @ApiProperty({ description: '邮箱地址' })
  @IsEmail()
  email: string;

  @ApiProperty({ description: '电话号码' })
  @IsString()
  phone: string;

  @ApiProperty({ description: '职位角色' })
  @IsString()
  role: string;
}

export class ContactInfoDto {
  @ApiProperty({ description: '主要联系人', type: ContactDto })
  @ValidateNested()
  @Type(() => ContactDto)
  primaryContact: ContactDto;

  @ApiPropertyOptional({ description: '技术联系人', type: ContactDto })
  @ValidateNested()
  @Type(() => ContactDto)
  @IsOptional()
  technicalContact?: ContactDto;

  @ApiPropertyOptional({ description: '商务联系人', type: ContactDto })
  @ValidateNested()
  @Type(() => ContactDto)
  @IsOptional()
  businessContact?: ContactDto;

  @ApiPropertyOptional({ description: '支持联系人', type: ContactDto })
  @ValidateNested()
  @Type(() => ContactDto)
  @IsOptional()
  supportContact?: ContactDto;
}

export class PartnerCapabilitiesDto {
  @ApiPropertyOptional({ description: '技术栈', type: [String] })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  technologies?: string[];

  @ApiPropertyOptional({ description: '行业领域', type: [String] })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  industries?: string[];

  @ApiPropertyOptional({ description: '服务区域', type: [String] })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  regions?: string[];

  @ApiPropertyOptional({ description: '支持语言', type: [String] })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  languages?: string[];

  @ApiPropertyOptional({ description: '认证资质', type: [String] })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  certifications?: string[];

  @ApiPropertyOptional({ description: '专业领域', type: [String] })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  specializations?: string[];

  @ApiPropertyOptional({ description: '支持级别', type: [String] })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  supportLevels?: string[];
}

export class CreatePartnerApplicationDto {
  @ApiProperty({ description: '合作伙伴名称' })
  @IsString()
  name: string;

  @ApiProperty({ description: '合作伙伴类型', enum: PartnerType })
  @IsEnum(PartnerType)
  type: PartnerType;

  @ApiProperty({ description: '合作伙伴描述' })
  @IsString()
  description: string;

  @ApiPropertyOptional({ description: '官方网站' })
  @IsUrl()
  @IsOptional()
  website?: string;

  @ApiProperty({ description: '联系信息', type: ContactInfoDto })
  @ValidateNested()
  @Type(() => ContactInfoDto)
  contactInfo: ContactInfoDto;

  @ApiPropertyOptional({ description: '合作伙伴能力', type: PartnerCapabilitiesDto })
  @ValidateNested()
  @Type(() => PartnerCapabilitiesDto)
  @IsOptional()
  capabilities?: PartnerCapabilitiesDto;
}

export class UpdatePartnerDto {
  @ApiPropertyOptional({ description: '合作伙伴名称' })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({ description: '合作伙伴描述' })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({ description: '官方网站' })
  @IsUrl()
  @IsOptional()
  website?: string;

  @ApiPropertyOptional({ description: '合作伙伴等级', enum: PartnerTier })
  @IsEnum(PartnerTier)
  @IsOptional()
  tier?: PartnerTier;

  @ApiPropertyOptional({ description: '联系信息', type: ContactInfoDto })
  @ValidateNested()
  @Type(() => ContactInfoDto)
  @IsOptional()
  contactInfo?: ContactInfoDto;

  @ApiPropertyOptional({ description: '合作伙伴能力', type: PartnerCapabilitiesDto })
  @ValidateNested()
  @Type(() => PartnerCapabilitiesDto)
  @IsOptional()
  capabilities?: PartnerCapabilitiesDto;
}

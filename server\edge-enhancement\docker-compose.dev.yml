version: '3.8'

services:
  # 边缘计算增强服务 - 开发模式
  edge-enhancement-dev:
    build:
      context: .
      dockerfile: Dockerfile
      target: development
    container_name: edge-enhancement-dev
    hostname: edge-enhancement-dev
    restart: unless-stopped
    ports:
      - "${EDGE_ENHANCEMENT_PORT:-3040}:3040"
      - "9229:9229" # 调试端口
    environment:
      - NODE_ENV=development
      - PORT=3040
      - LOG_LEVEL=debug
      - DATABASE_URL=******************************************************/edge_enhancement
      - REDIS_URL=redis://redis-dev:6379
    volumes:
      - ./src:/app/src
      - ./test:/app/test
      - ./package.json:/app/package.json
      - ./tsconfig.json:/app/tsconfig.json
      - ./nest-cli.json:/app/nest-cli.json
      - edge-enhancement-dev-logs:/app/logs
    networks:
      - edge-dev-network
    depends_on:
      - redis-dev
      - postgres-dev
    command: npm run start:debug

  # Redis开发环境
  redis-dev:
    image: redis:7-alpine
    container_name: edge-enhancement-redis-dev
    restart: unless-stopped
    ports:
      - "6380:6379"
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    volumes:
      - redis-dev-data:/data
    networks:
      - edge-dev-network

  # PostgreSQL开发环境
  postgres-dev:
    image: postgres:15-alpine
    container_name: edge-enhancement-postgres-dev
    restart: unless-stopped
    ports:
      - "5433:5432"
    environment:
      - POSTGRES_DB=edge_enhancement
      - POSTGRES_USER=edge_user
      - POSTGRES_PASSWORD=edge_password
    volumes:
      - postgres-dev-data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - edge-dev-network

  # 数据库管理工具
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: edge-enhancement-pgadmin
    restart: unless-stopped
    ports:
      - "5050:80"
    environment:
      - PGADMIN_DEFAULT_EMAIL=<EMAIL>
      - PGADMIN_DEFAULT_PASSWORD=admin
    volumes:
      - pgadmin-data:/var/lib/pgadmin
    networks:
      - edge-dev-network
    depends_on:
      - postgres-dev

  # Redis管理工具
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: edge-enhancement-redis-commander
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis-dev:6379
    networks:
      - edge-dev-network
    depends_on:
      - redis-dev

# 开发网络
networks:
  edge-dev-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 开发数据卷
volumes:
  edge-enhancement-dev-logs:
    driver: local
  redis-dev-data:
    driver: local
  postgres-dev-data:
    driver: local
  pgadmin-data:
    driver: local

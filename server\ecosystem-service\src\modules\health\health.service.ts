import { Injectable, Logger } from '@nestjs/common';
import { RedisService } from '../../common/redis/redis.service';

@Injectable()
export class HealthService {
  private readonly logger = new Logger(HealthService.name);
  private readonly startTime = new Date();

  constructor(private readonly redisService: RedisService) {}

  async getBasicHealth() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      service: 'ecosystem-service'
    };
  }

  async getDetailedHealth() {
    const memoryUsage = process.memoryUsage();
    
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      startTime: this.startTime.toISOString(),
      service: 'ecosystem-service',
      version: '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      memory: {
        rss: Math.round(memoryUsage.rss / 1024 / 1024) + ' MB',
        heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024) + ' MB',
        heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024) + ' MB',
        external: Math.round(memoryUsage.external / 1024 / 1024) + ' MB'
      },
      cpu: {
        usage: process.cpuUsage()
      },
      dependencies: {
        database: await this.checkDatabase(),
        redis: await this.checkRedis(),
        externalServices: await this.checkExternalServices()
      }
    };
  }

  async getReadiness() {
    const checks = await Promise.allSettled([
      this.checkDatabase(),
      this.checkRedis(),
      this.checkExternalServices()
    ]);

    const allReady = checks.every(check => 
      check.status === 'fulfilled' && check.value === 'ok'
    );

    return {
      status: allReady ? 'ready' : 'not_ready',
      timestamp: new Date().toISOString(),
      checks: {
        database: checks[0].status === 'fulfilled' ? checks[0].value : 'error',
        redis: checks[1].status === 'fulfilled' ? checks[1].value : 'error',
        externalServices: checks[2].status === 'fulfilled' ? checks[2].value : 'error'
      }
    };
  }

  async getLiveness() {
    return {
      status: 'alive',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      pid: process.pid
    };
  }

  private async checkDatabase(): Promise<string> {
    try {
      // TODO: 实现数据库连接检查
      return 'ok';
    } catch (error) {
      this.logger.error('数据库健康检查失败', error);
      return 'error';
    }
  }

  private async checkRedis(): Promise<string> {
    try {
      const isConnected = await this.redisService.ping();
      return isConnected ? 'ok' : 'error';
    } catch (error) {
      this.logger.error('Redis健康检查失败', error);
      return 'error';
    }
  }

  private async checkExternalServices(): Promise<string> {
    try {
      // TODO: 实现外部服务检查
      return 'ok';
    } catch (error) {
      this.logger.error('外部服务健康检查失败', error);
      return 'error';
    }
  }
}

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Index,
} from 'typeorm';
import { ApiUsageRecord } from './api-usage-record.entity';

export enum APIType {
  REST = 'rest',
  GRAPHQL = 'graphql',
  WEBSOCKET = 'websocket',
  GRPC = 'grpc',
  WEBHOOK = 'webhook'
}

export enum APIStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  DEPRECATED = 'deprecated',
  RETIRED = 'retired'
}

@Entity('api_specifications')
@Index(['type', 'status'])
export class ApiSpecification {
  @PrimaryGeneratedColumn('uuid')
  apiId: string;

  @Column({ length: 255 })
  @Index()
  name: string;

  @Column({ length: 50 })
  version: string;

  @Column({
    type: 'enum',
    enum: APIType,
  })
  type: APIType;

  @Column('text')
  description: string;

  @Column({
    type: 'enum',
    enum: APIStatus,
    default: APIStatus.DRAFT,
  })
  @Index()
  status: APIStatus;

  // API端点 - 存储为JSON
  @Column('json', { nullable: true })
  endpoints: Array<{
    path: string;
    method: string;
    description: string;
    parameters: Array<{
      name: string;
      type: string;
      required: boolean;
      description: string;
      example: any;
    }>;
    requestBody?: {
      contentType: string;
      schema: any;
      examples: Array<{
        name: string;
        description: string;
        value: any;
      }>;
    };
    responses: Array<{
      statusCode: number;
      description: string;
      schema: any;
      examples: Array<{
        name: string;
        description: string;
        value: any;
      }>;
    }>;
    examples: Array<{
      name: string;
      description: string;
      value: any;
    }>;
  }>;

  // 认证规范 - 存储为JSON
  @Column('json', { nullable: true })
  authentication: {
    type: 'api_key' | 'oauth2' | 'jwt' | 'basic';
    description: string;
    parameters: any;
  };

  // 速率限制规范 - 存储为JSON
  @Column('json', { nullable: true })
  rateLimit: {
    requests_per_minute: number;
    requests_per_hour: number;
    requests_per_day: number;
    burst_limit: number;
  };

  // 文档规范 - 存储为JSON
  @Column('json', { nullable: true })
  documentation: {
    overview: string;
    getting_started: string;
    tutorials: Array<{
      title: string;
      description: string;
      difficulty: 'beginner' | 'intermediate' | 'advanced';
      duration: number; // 分钟
      content: string;
      code_samples: Array<{
        language: string;
        code: string;
        description: string;
      }>;
    }>;
    sdk_links: Array<{
      language: string;
      name: string;
      version: string;
      download_url: string;
      documentation_url: string;
    }>;
    changelog: Array<{
      version: string;
      date: Date;
      changes: string[];
      breaking_changes: string[];
    }>;
  };

  @Column({ length: 255, nullable: true })
  publisherId: string; // 发布者ID

  @Column({ length: 500, nullable: true })
  documentationUrl: string;

  @Column({ length: 500, nullable: true })
  repositoryUrl: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 关联关系
  @OneToMany(() => ApiUsageRecord, usage => usage.apiSpecification)
  usageRecords: ApiUsageRecord[];
}

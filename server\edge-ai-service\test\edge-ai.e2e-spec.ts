import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';

describe('EdgeAI Service (e2e)', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('/api/v1/edge (POST)', () => {
    it('should register edge device', () => {
      const deviceData = {
        name: '测试设备',
        type: 'industrial_pc',
        location: '测试位置',
        capabilities: {
          cpu: { cores: 4, frequency: 2400, architecture: 'x86_64' },
          memory: { total: 8192, available: 6144 },
          storage: { total: 256, available: 200 },
          connectivity: ['ethernet', 'wifi']
        }
      };

      return request(app.getHttpServer())
        .post('/api/v1/edge/devices/register')
        .send(deviceData)
        .expect(201)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data.deviceId).toBeDefined();
        });
    });
  });

  describe('/api/v1/edge/statistics (GET)', () => {
    it('should get edge AI statistics', () => {
      return request(app.getHttpServer())
        .get('/api/v1/edge/statistics')
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data).toBeDefined();
          expect(res.body.data.devices).toBeDefined();
          expect(res.body.data.models).toBeDefined();
          expect(res.body.data.performance).toBeDefined();
        });
    });
  });

  describe('/api/v1/edge/inference (POST)', () => {
    it('should perform edge inference', () => {
      const inferenceData = {
        requestId: 'test-request-001',
        modelId: 'anomaly_detection_v1',
        inputData: { image: 'base64_encoded_image_data' },
        priority: 'medium',
        timeout: 5000
      };

      return request(app.getHttpServer())
        .post('/api/v1/edge/inference')
        .send(inferenceData)
        .expect(201)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data.result).toBeDefined();
          expect(res.body.data.confidence).toBeDefined();
          expect(res.body.data.processingTime).toBeDefined();
        });
    });
  });

  describe('/api/v1/devices (GET)', () => {
    it('should get device list', () => {
      return request(app.getHttpServer())
        .get('/api/v1/devices')
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(Array.isArray(res.body.data)).toBe(true);
        });
    });
  });

  describe('/api/v1/inference/statistics (GET)', () => {
    it('should get inference statistics', () => {
      return request(app.getHttpServer())
        .get('/api/v1/inference/statistics')
        .expect(200)
        .expect((res) => {
          expect(res.body.success).toBe(true);
          expect(res.body.data.total).toBeDefined();
          expect(res.body.data.completed).toBeDefined();
          expect(res.body.data.successRate).toBeDefined();
        });
    });
  });
});

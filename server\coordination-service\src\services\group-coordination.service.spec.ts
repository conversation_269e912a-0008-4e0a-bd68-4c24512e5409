/**
 * 群体协调服务测试
 */

import { Test, TestingModule } from '@nestjs/testing';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { GroupCoordinationService, CoordinationTaskType, SocialRole } from './group-coordination.service';

describe('GroupCoordinationService', () => {
  let service: GroupCoordinationService;
  let eventEmitter: EventEmitter2;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        GroupCoordinationService,
        {
          provide: EventEmitter2,
          useValue: {
            emit: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<GroupCoordinationService>(GroupCoordinationService);
    eventEmitter = module.get<EventEmitter2>(EventEmitter2);
  });

  afterEach(async () => {
    // 清理服务
    if (service) {
      await service.shutdown();
    }
  });

  it('应该被定义', () => {
    expect(service).toBeDefined();
  });

  describe('createCoordinationTask', () => {
    it('应该创建群体形成任务', async () => {
      const entityIds = ['entity1', 'entity2', 'entity3'];
      const parameters = {
        maxDistance: 10,
        minGroupSize: 2
      };

      const taskId = await service.createCoordinationTask(
        CoordinationTaskType.GROUP_FORMATION,
        entityIds,
        parameters,
        1
      );

      expect(taskId).toBeDefined();
      expect(typeof taskId).toBe('string');
      expect(taskId).toMatch(/^coord_/);
    });

    it('应该创建角色分配任务', async () => {
      const entityIds = ['entity1', 'entity2'];
      const parameters = {
        requiredRoles: [SocialRole.LEADER, SocialRole.PARTICIPANT]
      };

      const taskId = await service.createCoordinationTask(
        CoordinationTaskType.ROLE_ASSIGNMENT,
        entityIds,
        parameters,
        2
      );

      expect(taskId).toBeDefined();
      expect(typeof taskId).toBe('string');
    });

    it('应该创建冲突解决任务', async () => {
      const entityIds = ['entity1', 'entity2'];
      const parameters = {
        conflictId: 'conflict123'
      };

      const taskId = await service.createCoordinationTask(
        CoordinationTaskType.CONFLICT_RESOLUTION,
        entityIds,
        parameters,
        3
      );

      expect(taskId).toBeDefined();
      expect(typeof taskId).toBe('string');
    });

    it('应该创建资源分配任务', async () => {
      const entityIds = ['entity1', 'entity2', 'entity3'];
      const parameters = {
        resourceType: 'computing_power',
        amount: 100
      };

      const taskId = await service.createCoordinationTask(
        CoordinationTaskType.RESOURCE_ALLOCATION,
        entityIds,
        parameters,
        2
      );

      expect(taskId).toBeDefined();
      expect(typeof taskId).toBe('string');
    });
  });

  describe('getTaskStatus', () => {
    it('应该返回任务状态', async () => {
      const entityIds = ['entity1', 'entity2'];
      const parameters = { test: true };

      const taskId = await service.createCoordinationTask(
        CoordinationTaskType.GROUP_FORMATION,
        entityIds,
        parameters
      );

      const task = service.getTaskStatus(taskId);

      expect(task).toBeDefined();
      expect(task.id).toBe(taskId);
      expect(task.type).toBe(CoordinationTaskType.GROUP_FORMATION);
      expect(task.status).toBe('pending');
      expect(task.entityIds).toEqual(entityIds);
      expect(task.parameters).toEqual(parameters);
    });

    it('应该返回null对于不存在的任务', () => {
      const task = service.getTaskStatus('nonexistent');
      expect(task).toBeNull();
    });
  });

  describe('getActiveConflicts', () => {
    it('应该返回活跃冲突列表', () => {
      const conflicts = service.getActiveConflicts();
      expect(Array.isArray(conflicts)).toBe(true);
    });
  });

  describe('getAvailableResources', () => {
    it('应该返回可用资源列表', () => {
      const resources = service.getAvailableResources();
      expect(Array.isArray(resources)).toBe(true);
    });
  });

  describe('事件发射', () => {
    it('应该在创建任务时发射事件', async () => {
      const entityIds = ['entity1'];
      const parameters = {};

      await service.createCoordinationTask(
        CoordinationTaskType.GROUP_FORMATION,
        entityIds,
        parameters
      );

      expect(eventEmitter.emit).toHaveBeenCalledWith(
        'coordination.task.created',
        expect.objectContaining({
          type: CoordinationTaskType.GROUP_FORMATION,
          entityCount: 1
        })
      );
    });
  });

  describe('任务优先级', () => {
    it('应该按优先级排序任务', async () => {
      const entityIds = ['entity1'];
      const parameters = {};

      // 创建不同优先级的任务
      const lowPriorityTask = await service.createCoordinationTask(
        CoordinationTaskType.GROUP_FORMATION,
        entityIds,
        parameters,
        1
      );

      const highPriorityTask = await service.createCoordinationTask(
        CoordinationTaskType.ROLE_ASSIGNMENT,
        entityIds,
        parameters,
        3
      );

      // 验证任务已创建
      expect(lowPriorityTask).toBeDefined();
      expect(highPriorityTask).toBeDefined();
    });
  });

  describe('任务截止时间', () => {
    it('应该支持设置任务截止时间', async () => {
      const entityIds = ['entity1'];
      const parameters = {};
      const deadline = Date.now() + 60000; // 1分钟后

      const taskId = await service.createCoordinationTask(
        CoordinationTaskType.GROUP_FORMATION,
        entityIds,
        parameters,
        1,
        deadline
      );

      const task = service.getTaskStatus(taskId);
      expect(task.deadline).toBe(deadline);
    });
  });

  describe('错误处理', () => {
    it('应该处理无效的任务类型', async () => {
      const entityIds = ['entity1'];
      const parameters = {};

      try {
        await service.createCoordinationTask(
          'INVALID_TYPE' as CoordinationTaskType,
          entityIds,
          parameters
        );
      } catch (error) {
        expect(error).toBeDefined();
      }
    });

    it('应该处理空的实体ID列表', async () => {
      const entityIds: string[] = [];
      const parameters = {};

      const taskId = await service.createCoordinationTask(
        CoordinationTaskType.GROUP_FORMATION,
        entityIds,
        parameters
      );

      expect(taskId).toBeDefined();
    });
  });

  describe('服务关闭', () => {
    it('应该正确关闭服务', async () => {
      await expect(service.shutdown()).resolves.not.toThrow();
    });
  });
});

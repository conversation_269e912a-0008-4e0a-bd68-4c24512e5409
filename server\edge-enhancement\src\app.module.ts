import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';
import { ThrottlerModule } from '@nestjs/throttler';

// 导入边缘计算增强模块
import { EdgeEnhancementModule } from './edge-enhancement.module';

// 导入配置
import { databaseConfig } from './config/database.config';
import { redisConfig } from './config/redis.config';

/**
 * 边缘计算增强服务根模块
 * 整合所有功能模块和基础设施配置
 */
@Module({
  imports: [
    // 配置模块 - 全局配置管理
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
      load: [databaseConfig, redisConfig],
      cache: true,
      expandVariables: true,
    }),

    // 数据库模块 - PostgreSQL连接配置
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        type: 'postgres',
        host: configService.get<string>('database.host', 'localhost'),
        port: configService.get<number>('database.port', 5432),
        username: configService.get<string>('database.username', 'edge_user'),
        password: configService.get<string>('database.password', 'edge_password'),
        database: configService.get<string>('database.name', 'edge_enhancement'),
        entities: [__dirname + '/**/*.entity{.ts,.js}'],
        migrations: [__dirname + '/migrations/*{.ts,.js}'],
        synchronize: configService.get<string>('NODE_ENV') !== 'production',
        logging: configService.get<string>('NODE_ENV') === 'development',
        retryAttempts: 3,
        retryDelay: 3000,
        autoLoadEntities: true,
        keepConnectionAlive: true,
        ssl: configService.get<string>('NODE_ENV') === 'production' ? {
          rejectUnauthorized: false
        } : false,
        extra: {
          max: 20,
          min: 5,
          acquire: 30000,
          idle: 10000,
        },
      }),
      inject: [ConfigService],
    }),

    // 事件发射器模块 - 事件驱动架构
    EventEmitterModule.forRoot({
      wildcard: false,
      delimiter: '.',
      newListener: false,
      removeListener: false,
      maxListeners: 20,
      verboseMemoryLeak: false,
      ignoreErrors: false,
    }),

    // 定时任务模块 - 定时任务调度
    ScheduleModule.forRoot(),

    // 限流模块 - API请求限流
    ThrottlerModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        throttlers: [{
          ttl: configService.get<number>('THROTTLE_TTL', 60) * 1000, // 转换为毫秒
          limit: configService.get<number>('THROTTLE_LIMIT', 100),
        }]
      }),
      inject: [ConfigService],
    }),

    // 边缘计算增强核心模块
    EdgeEnhancementModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {
  constructor(private configService: ConfigService) {
    // 启动时日志
    const nodeEnv = this.configService.get<string>('NODE_ENV', 'development');
    const port = this.configService.get<number>('PORT', 3040);
    
    console.log(`
    ╔══════════════════════════════════════════════════════════════╗
    ║                   边缘计算增强服务                           ║
    ║                Edge Enhancement Service                      ║
    ╠══════════════════════════════════════════════════════════════╣
    ║  🚀 服务端口: ${port.toString().padEnd(47)} ║
    ║  🔧 运行环境: ${nodeEnv.padEnd(47)} ║
    ║  📊 数据库: PostgreSQL + Redis                              ║
    ║  🧠 智能调度: 机器学习负载预测                               ║
    ║  🚀 预测缓存: 多层缓存架构                                   ║
    ║  🌐 自适应网络: 动态传输优化                                 ║
    ╚══════════════════════════════════════════════════════════════╝
    `);
  }
}

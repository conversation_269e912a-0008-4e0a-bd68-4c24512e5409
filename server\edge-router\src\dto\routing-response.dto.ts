import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * 通用API响应DTO
 */
export class ApiResponseDto<T = any> {
  @ApiProperty({ description: '是否成功', example: true })
  success: boolean;

  @ApiProperty({ description: '响应消息', example: '操作成功' })
  message: string;

  @ApiPropertyOptional({ description: '响应数据' })
  data?: T;

  @ApiPropertyOptional({ description: '错误代码', example: 'ROUTING_ERROR' })
  errorCode?: string;

  @ApiProperty({ description: '时间戳', example: '2024-01-01T00:00:00.000Z' })
  timestamp: string;

  constructor(success: boolean, message: string, data?: T, errorCode?: string) {
    this.success = success;
    this.message = message;
    this.data = data;
    this.errorCode = errorCode;
    this.timestamp = new Date().toISOString();
  }

  static success<T>(message: string = '操作成功', data?: T): ApiResponseDto<T> {
    return new ApiResponseDto(true, message, data);
  }

  static error(message: string, errorCode?: string): ApiResponseDto {
    return new ApiResponseDto(false, message, undefined, errorCode);
  }
}

/**
 * 边缘节点响应DTO
 */
export class EdgeNodeResponseDto {
  @ApiProperty({ description: '节点ID', example: 'edge-node-001' })
  nodeId: string;

  @ApiProperty({ description: '节点区域', example: 'beijing-zone-1' })
  region: string;

  @ApiProperty({ description: '节点端点', example: 'http://*************:8080' })
  endpoint: string;

  @ApiProperty({ description: '节点状态', example: 'online' })
  status: string;

  @ApiPropertyOptional({ description: '节点位置' })
  location?: {
    latitude: number;
    longitude: number;
    city: string;
    country: string;
  };

  @ApiPropertyOptional({ description: '节点指标' })
  metrics?: {
    currentUsers: number;
    cpuUsage: number;
    memoryUsage: number;
    networkLatency: number;
    uptime: number;
  };

  @ApiPropertyOptional({ description: '节点能力' })
  capabilities?: {
    maxUsers: number;
    supportedFeatures: string[];
    resources: {
      cpu: string;
      memory: string;
      storage: string;
    };
  };
}

/**
 * 路由决策响应DTO
 */
export class RoutingDecisionResponseDto {
  @ApiProperty({ description: '选中的边缘节点', type: EdgeNodeResponseDto })
  selectedNode: EdgeNodeResponseDto;

  @ApiProperty({ description: '决策分数', example: 0.85 })
  score: number;

  @ApiProperty({ description: '决策原因', example: '基于地理位置和负载情况选择' })
  reason: string;

  @ApiProperty({ description: '决策时间(ms)', example: 15 })
  decisionTime: number;

  @ApiProperty({ description: '路由策略', example: 'hybrid' })
  strategy: string;

  @ApiPropertyOptional({ description: '备选节点列表', type: [EdgeNodeResponseDto] })
  alternativeNodes?: EdgeNodeResponseDto[];

  @ApiPropertyOptional({ description: '路由元数据' })
  metadata?: {
    totalCandidates: number;
    filterCriteria: string[];
    weights: Record<string, number>;
    clientDistance?: number;
    estimatedLatency?: number;
  };
}

/**
 * 网络质量响应DTO
 */
export class NetworkQualityResponseDto {
  @ApiProperty({ description: '目标节点ID', example: 'edge-node-001' })
  nodeId: string;

  @ApiProperty({ description: '延迟(ms)', example: 25 })
  latency: number;

  @ApiProperty({ description: '丢包率(%)', example: 0.1 })
  packetLoss: number;

  @ApiProperty({ description: '带宽(Mbps)', example: 100 })
  bandwidth: number;

  @ApiProperty({ description: '抖动(ms)', example: 2 })
  jitter: number;

  @ApiProperty({ description: '质量分数(0-1)', example: 0.92 })
  qualityScore: number;

  @ApiProperty({ description: '测量时间', example: '2024-01-01T00:00:00.000Z' })
  measuredAt: string;
}

/**
 * 路由统计响应DTO
 */
export class RoutingStatsResponseDto {
  @ApiProperty({ description: '总路由决策数', example: 1000 })
  totalDecisions: number;

  @ApiProperty({ description: '成功路由数', example: 980 })
  successfulRoutes: number;

  @ApiProperty({ description: '失败路由数', example: 20 })
  failedRoutes: number;

  @ApiProperty({ description: '平均决策时间(ms)', example: 12.5 })
  averageDecisionTime: number;

  @ApiProperty({ description: '最常用的路由策略', example: 'hybrid' })
  mostUsedStrategy: string;

  @ApiProperty({ description: '按区域分组的统计' })
  regionStats: Record<string, {
    decisions: number;
    averageLatency: number;
    successRate: number;
  }>;

  @ApiProperty({ description: '按节点分组的统计' })
  nodeStats: Record<string, {
    selections: number;
    averageScore: number;
    lastSelected: string;
  }>;

  @ApiProperty({ description: '缓存统计' })
  cacheStats: {
    memoryCacheSize: number;
    maxMemoryCacheSize: number;
    redisConnected: boolean;
    hitRate: number;
  };
}

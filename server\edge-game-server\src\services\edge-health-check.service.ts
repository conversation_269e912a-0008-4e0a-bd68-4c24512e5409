import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Cron, CronExpression } from '@nestjs/schedule';
import * as os from 'os';
import * as fs from 'fs';
import * as path from 'path';

/**
 * 健康检查状态枚举
 */
export enum HealthStatus {
  HEALTHY = 'healthy',
  DEGRADED = 'degraded',
  UNHEALTHY = 'unhealthy',
  CRITICAL = 'critical'
}

/**
 * 健康检查结果接口
 */
export interface HealthCheckResult {
  status: HealthStatus;
  timestamp: Date;
  checks: {
    cpu: {
      status: HealthStatus;
      usage: number;
      threshold: number;
    };
    memory: {
      status: HealthStatus;
      usage: number;
      available: number;
      threshold: number;
    };
    disk: {
      status: HealthStatus;
      usage: number;
      available: number;
      threshold: number;
    };
    network: {
      status: HealthStatus;
      latency: number;
      threshold: number;
    };
    services: {
      status: HealthStatus;
      activeServices: string[];
      failedServices: string[];
    };
  };
  uptime: number;
  version: string;
}

/**
 * 边缘健康检查服务
 * 监控边缘节点的系统健康状态
 */
@Injectable()
export class EdgeHealthCheckService {
  private readonly logger = new Logger(EdgeHealthCheckService.name);
  private healthCheckResults: HealthCheckResult[] = [];
  private readonly maxHistorySize = 100;
  private isHealthy = true;
  private startTime = Date.now();

  // 健康检查阈值
  private readonly thresholds = {
    cpu: this.configService.get<number>('HEALTH_CPU_THRESHOLD', 80),
    memory: this.configService.get<number>('HEALTH_MEMORY_THRESHOLD', 85),
    disk: this.configService.get<number>('HEALTH_DISK_THRESHOLD', 90),
    network: this.configService.get<number>('HEALTH_NETWORK_THRESHOLD', 200),
  };

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * 启动健康检查
   */
  async startHealthChecks(): Promise<void> {
    this.logger.log('启动边缘节点健康检查');
    await this.performHealthCheck();
  }

  /**
   * 定期健康检查
   */
  @Cron(CronExpression.EVERY_30_SECONDS)
  async performHealthCheck(): Promise<HealthCheckResult> {
    try {
      const result = await this.runAllChecks();
      
      // 保存检查结果
      this.saveHealthCheckResult(result);
      
      // 检查状态变化
      this.checkStatusChange(result);
      
      // 触发健康检查事件
      this.eventEmitter.emit('edge.health.checked', result);
      
      return result;
    } catch (error) {
      this.logger.error(`健康检查失败: ${error.message}`);
      
      const criticalResult: HealthCheckResult = {
        status: HealthStatus.CRITICAL,
        timestamp: new Date(),
        checks: {
          cpu: { status: HealthStatus.CRITICAL, usage: 0, threshold: this.thresholds.cpu },
          memory: { status: HealthStatus.CRITICAL, usage: 0, available: 0, threshold: this.thresholds.memory },
          disk: { status: HealthStatus.CRITICAL, usage: 0, available: 0, threshold: this.thresholds.disk },
          network: { status: HealthStatus.CRITICAL, latency: 0, threshold: this.thresholds.network },
          services: { status: HealthStatus.CRITICAL, activeServices: [], failedServices: ['health-check'] }
        },
        uptime: this.getUptime(),
        version: this.getVersion()
      };
      
      this.saveHealthCheckResult(criticalResult);
      return criticalResult;
    }
  }

  /**
   * 执行所有健康检查
   */
  private async runAllChecks(): Promise<HealthCheckResult> {
    const [cpuCheck, memoryCheck, diskCheck, networkCheck, servicesCheck] = await Promise.all([
      this.checkCPU(),
      this.checkMemory(),
      this.checkDisk(),
      this.checkNetwork(),
      this.checkServices()
    ]);

    // 计算整体状态
    const overallStatus = this.calculateOverallStatus([
      cpuCheck.status,
      memoryCheck.status,
      diskCheck.status,
      networkCheck.status,
      servicesCheck.status
    ]);

    return {
      status: overallStatus,
      timestamp: new Date(),
      checks: {
        cpu: cpuCheck,
        memory: memoryCheck,
        disk: diskCheck,
        network: networkCheck,
        services: servicesCheck
      },
      uptime: this.getUptime(),
      version: this.getVersion()
    };
  }

  /**
   * CPU健康检查
   */
  private async checkCPU(): Promise<{ status: HealthStatus; usage: number; threshold: number }> {
    const cpus = os.cpus();
    let totalIdle = 0;
    let totalTick = 0;

    cpus.forEach(cpu => {
      for (const type in cpu.times) {
        totalTick += cpu.times[type];
      }
      totalIdle += cpu.times.idle;
    });

    const usage = 100 - (totalIdle / totalTick * 100);
    const status = this.getStatusByThreshold(usage, this.thresholds.cpu);

    return {
      status,
      usage: Math.round(usage * 100) / 100,
      threshold: this.thresholds.cpu
    };
  }

  /**
   * 内存健康检查
   */
  private async checkMemory(): Promise<{ 
    status: HealthStatus; 
    usage: number; 
    available: number; 
    threshold: number 
  }> {
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    const usage = (usedMemory / totalMemory) * 100;
    const status = this.getStatusByThreshold(usage, this.thresholds.memory);

    return {
      status,
      usage: Math.round(usage * 100) / 100,
      available: Math.round(freeMemory / 1024 / 1024), // MB
      threshold: this.thresholds.memory
    };
  }

  /**
   * 磁盘健康检查
   */
  private async checkDisk(): Promise<{ 
    status: HealthStatus; 
    usage: number; 
    available: number; 
    threshold: number 
  }> {
    try {
      const stats = await fs.promises.statfs(process.cwd());
      const total = stats.blocks * stats.bsize;
      const free = stats.bavail * stats.bsize;
      const used = total - free;
      const usage = (used / total) * 100;
      const status = this.getStatusByThreshold(usage, this.thresholds.disk);

      return {
        status,
        usage: Math.round(usage * 100) / 100,
        available: Math.round(free / 1024 / 1024 / 1024), // GB
        threshold: this.thresholds.disk
      };
    } catch (error) {
      // 如果无法获取磁盘信息，返回警告状态
      return {
        status: HealthStatus.DEGRADED,
        usage: 0,
        available: 0,
        threshold: this.thresholds.disk
      };
    }
  }

  /**
   * 网络健康检查
   */
  private async checkNetwork(): Promise<{ status: HealthStatus; latency: number; threshold: number }> {
    try {
      const start = Date.now();
      
      // 简单的网络延迟检查（ping本地回环）
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => reject(new Error('Network timeout')), 5000);
        
        // 模拟网络检查
        setTimeout(() => {
          clearTimeout(timeout);
          resolve(true);
        }, Math.random() * 100);
      });
      
      const latency = Date.now() - start;
      const status = this.getStatusByThreshold(latency, this.thresholds.network);

      return {
        status,
        latency,
        threshold: this.thresholds.network
      };
    } catch (error) {
      return {
        status: HealthStatus.UNHEALTHY,
        latency: this.thresholds.network + 1,
        threshold: this.thresholds.network
      };
    }
  }

  /**
   * 服务健康检查
   */
  private async checkServices(): Promise<{ 
    status: HealthStatus; 
    activeServices: string[]; 
    failedServices: string[] 
  }> {
    const services = ['edge-game-server', 'edge-cache', 'edge-sync', 'edge-webrtc'];
    const activeServices: string[] = [];
    const failedServices: string[] = [];

    // 这里应该检查实际的服务状态
    // 暂时模拟所有服务都正常
    services.forEach(service => {
      if (Math.random() > 0.1) { // 90%概率服务正常
        activeServices.push(service);
      } else {
        failedServices.push(service);
      }
    });

    const status = failedServices.length === 0 
      ? HealthStatus.HEALTHY 
      : failedServices.length < services.length / 2 
        ? HealthStatus.DEGRADED 
        : HealthStatus.UNHEALTHY;

    return {
      status,
      activeServices,
      failedServices
    };
  }

  /**
   * 根据阈值获取状态
   */
  private getStatusByThreshold(value: number, threshold: number): HealthStatus {
    if (value < threshold * 0.7) {
      return HealthStatus.HEALTHY;
    } else if (value < threshold * 0.85) {
      return HealthStatus.DEGRADED;
    } else if (value < threshold) {
      return HealthStatus.UNHEALTHY;
    } else {
      return HealthStatus.CRITICAL;
    }
  }

  /**
   * 计算整体状态
   */
  private calculateOverallStatus(statuses: HealthStatus[]): HealthStatus {
    if (statuses.includes(HealthStatus.CRITICAL)) {
      return HealthStatus.CRITICAL;
    } else if (statuses.includes(HealthStatus.UNHEALTHY)) {
      return HealthStatus.UNHEALTHY;
    } else if (statuses.includes(HealthStatus.DEGRADED)) {
      return HealthStatus.DEGRADED;
    } else {
      return HealthStatus.HEALTHY;
    }
  }

  /**
   * 保存健康检查结果
   */
  private saveHealthCheckResult(result: HealthCheckResult): void {
    this.healthCheckResults.push(result);
    
    // 保持历史记录大小限制
    if (this.healthCheckResults.length > this.maxHistorySize) {
      this.healthCheckResults.shift();
    }
  }

  /**
   * 检查状态变化
   */
  private checkStatusChange(result: HealthCheckResult): void {
    const wasHealthy = this.isHealthy;
    this.isHealthy = result.status === HealthStatus.HEALTHY;

    if (wasHealthy !== this.isHealthy) {
      this.eventEmitter.emit('edge.health.status.changed', {
        from: wasHealthy ? HealthStatus.HEALTHY : HealthStatus.UNHEALTHY,
        to: result.status,
        timestamp: result.timestamp
      });

      if (!this.isHealthy) {
        this.logger.warn(`边缘节点健康状态变为: ${result.status}`);
      } else {
        this.logger.log('边缘节点健康状态恢复正常');
      }
    }
  }

  /**
   * 获取运行时间
   */
  private getUptime(): number {
    return Date.now() - this.startTime;
  }

  /**
   * 获取版本信息
   */
  private getVersion(): string {
    try {
      const packagePath = path.join(process.cwd(), 'package.json');
      const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
      return packageJson.version || '1.0.0';
    } catch (error) {
      return '1.0.0';
    }
  }

  /**
   * 获取最新健康检查结果
   */
  getLatestHealthCheck(): HealthCheckResult | null {
    return this.healthCheckResults.length > 0 
      ? this.healthCheckResults[this.healthCheckResults.length - 1] 
      : null;
  }

  /**
   * 获取健康检查历史
   */
  getHealthCheckHistory(limit?: number): HealthCheckResult[] {
    const results = [...this.healthCheckResults];
    return limit ? results.slice(-limit) : results;
  }

  /**
   * 检查节点是否健康
   */
  isNodeHealthy(): boolean {
    return this.isHealthy;
  }

  /**
   * 获取健康统计信息
   */
  getHealthStats(): {
    currentStatus: HealthStatus;
    uptime: number;
    totalChecks: number;
    healthyChecks: number;
    unhealthyChecks: number;
    lastCheck: Date | null;
  } {
    const latest = this.getLatestHealthCheck();
    const healthyCount = this.healthCheckResults.filter(
      result => result.status === HealthStatus.HEALTHY
    ).length;

    return {
      currentStatus: latest?.status || HealthStatus.UNHEALTHY,
      uptime: this.getUptime(),
      totalChecks: this.healthCheckResults.length,
      healthyChecks: healthyCount,
      unhealthyChecks: this.healthCheckResults.length - healthyCount,
      lastCheck: latest?.timestamp || null
    };
  }
}

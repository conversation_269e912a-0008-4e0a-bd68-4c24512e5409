import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { createClient, RedisClientType } from 'redis';

/**
 * 缓存项接口
 */
interface CacheItem<T> {
  data: T;
  timestamp: number;
  ttl: number;
}

/**
 * 路由缓存服务
 * 提供高性能的缓存功能，支持内存缓存和Redis缓存
 */
@Injectable()
export class RoutingCacheService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(RoutingCacheService.name);
  private redisClient: RedisClientType;
  
  // 内存缓存
  private readonly memoryCache: Map<string, CacheItem<any>> = new Map();
  private readonly maxMemoryCacheSize: number;
  
  // 配置
  private readonly defaultTtl: number;
  private readonly enableRedis: boolean;

  constructor(private readonly configService: ConfigService) {
    this.maxMemoryCacheSize = this.configService.get<number>('routing.cache.maxSize', 10000);
    this.defaultTtl = this.configService.get<number>('routing.cache.networkQualityTtl', 300000);
    this.enableRedis = this.configService.get<boolean>('REDIS_ENABLED', true);
  }

  async onModuleInit() {
    this.logger.log('路由缓存服务初始化');
    
    if (this.enableRedis) {
      await this.initRedisClient();
    }
    
    // 启动缓存清理任务
    this.startCacheCleanup();
  }

  async onModuleDestroy() {
    if (this.redisClient) {
      await this.redisClient.quit();
    }
  }

  /**
   * 初始化Redis客户端
   */
  private async initRedisClient(): Promise<void> {
    try {
      const redisConfig = this.configService.get('redis');
      
      this.redisClient = createClient({
        socket: {
          host: redisConfig.host,
          port: redisConfig.port,
        },
        password: redisConfig.password,
        database: redisConfig.db,
      });

      this.redisClient.on('error', (error) => {
        this.logger.error(`Redis连接错误: ${error.message}`);
      });

      this.redisClient.on('connect', () => {
        this.logger.log('Redis连接成功');
      });

      await this.redisClient.connect();
    } catch (error) {
      this.logger.error(`Redis初始化失败: ${error.message}`);
      this.enableRedis && (this.enableRedis as any) = false;
    }
  }

  /**
   * 设置缓存
   */
  async set<T>(key: string, value: T, ttl?: number): Promise<void> {
    const actualTtl = ttl || this.defaultTtl;
    const cacheItem: CacheItem<T> = {
      data: value,
      timestamp: Date.now(),
      ttl: actualTtl,
    };

    // 内存缓存
    this.setMemoryCache(key, cacheItem);

    // Redis缓存
    if (this.enableRedis && this.redisClient) {
      try {
        await this.redisClient.setEx(
          key,
          Math.ceil(actualTtl / 1000),
          JSON.stringify(cacheItem)
        );
      } catch (error) {
        this.logger.warn(`Redis设置缓存失败: ${error.message}`);
      }
    }
  }

  /**
   * 获取缓存
   */
  async get<T>(key: string): Promise<T | null> {
    // 先检查内存缓存
    const memoryResult = this.getMemoryCache<T>(key);
    if (memoryResult !== null) {
      return memoryResult;
    }

    // 检查Redis缓存
    if (this.enableRedis && this.redisClient) {
      try {
        const redisResult = await this.redisClient.get(key);
        if (redisResult) {
          const cacheItem: CacheItem<T> = JSON.parse(redisResult);
          
          // 检查是否过期
          if (Date.now() - cacheItem.timestamp < cacheItem.ttl) {
            // 回写到内存缓存
            this.setMemoryCache(key, cacheItem);
            return cacheItem.data;
          } else {
            // 删除过期的Redis缓存
            await this.redisClient.del(key);
          }
        }
      } catch (error) {
        this.logger.warn(`Redis获取缓存失败: ${error.message}`);
      }
    }

    return null;
  }

  /**
   * 删除缓存
   */
  async del(key: string): Promise<void> {
    // 删除内存缓存
    this.memoryCache.delete(key);

    // 删除Redis缓存
    if (this.enableRedis && this.redisClient) {
      try {
        await this.redisClient.del(key);
      } catch (error) {
        this.logger.warn(`Redis删除缓存失败: ${error.message}`);
      }
    }
  }

  /**
   * 检查缓存是否存在
   */
  async exists(key: string): Promise<boolean> {
    // 检查内存缓存
    if (this.memoryCache.has(key)) {
      const item = this.memoryCache.get(key)!;
      if (Date.now() - item.timestamp < item.ttl) {
        return true;
      } else {
        this.memoryCache.delete(key);
      }
    }

    // 检查Redis缓存
    if (this.enableRedis && this.redisClient) {
      try {
        return (await this.redisClient.exists(key)) === 1;
      } catch (error) {
        this.logger.warn(`Redis检查缓存存在性失败: ${error.message}`);
      }
    }

    return false;
  }

  /**
   * 设置内存缓存
   */
  private setMemoryCache<T>(key: string, cacheItem: CacheItem<T>): void {
    // 检查缓存大小限制
    if (this.memoryCache.size >= this.maxMemoryCacheSize) {
      // 删除最旧的缓存项
      const oldestKey = this.memoryCache.keys().next().value;
      if (oldestKey) {
        this.memoryCache.delete(oldestKey);
      }
    }

    this.memoryCache.set(key, cacheItem);
  }

  /**
   * 获取内存缓存
   */
  private getMemoryCache<T>(key: string): T | null {
    const item = this.memoryCache.get(key);
    if (!item) {
      return null;
    }

    // 检查是否过期
    if (Date.now() - item.timestamp >= item.ttl) {
      this.memoryCache.delete(key);
      return null;
    }

    return item.data;
  }

  /**
   * 启动缓存清理任务
   */
  private startCacheCleanup(): void {
    setInterval(() => {
      this.cleanupMemoryCache();
    }, 60000); // 每分钟清理一次
  }

  /**
   * 清理过期的内存缓存
   */
  private cleanupMemoryCache(): void {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, item] of this.memoryCache.entries()) {
      if (now - item.timestamp >= item.ttl) {
        this.memoryCache.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      this.logger.debug(`清理了 ${cleanedCount} 个过期的内存缓存项`);
    }
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): {
    memoryCacheSize: number;
    maxMemoryCacheSize: number;
    redisConnected: boolean;
  } {
    return {
      memoryCacheSize: this.memoryCache.size,
      maxMemoryCacheSize: this.maxMemoryCacheSize,
      redisConnected: this.enableRedis && this.redisClient?.isReady || false,
    };
  }

  /**
   * 清空所有缓存
   */
  async clear(): Promise<void> {
    // 清空内存缓存
    this.memoryCache.clear();

    // 清空Redis缓存（仅清空当前前缀的键）
    if (this.enableRedis && this.redisClient) {
      try {
        const prefix = this.configService.get('redis.keyPrefix', 'edge-router:');
        const keys = await this.redisClient.keys(`${prefix}*`);
        if (keys.length > 0) {
          await this.redisClient.del(keys);
        }
      } catch (error) {
        this.logger.warn(`清空Redis缓存失败: ${error.message}`);
      }
    }

    this.logger.log('所有缓存已清空');
  }
}

#!/bin/bash

echo "正在清理node_modules目录..."

# 删除node_modules目录
if [ -d "node_modules" ]; then
    echo "删除现有的node_modules目录..."
    rm -rf node_modules
fi

# 删除package-lock.json
if [ -f "package-lock.json" ]; then
    echo "删除package-lock.json..."
    rm package-lock.json
fi

# 清理npm缓存
echo "清理npm缓存..."
npm cache clean --force

# 设置npm配置
echo "配置npm设置..."
npm config set fund false
npm config set audit false

# 安装依赖
echo "开始安装依赖..."
npm install --no-optional --legacy-peer-deps

echo "安装完成！"

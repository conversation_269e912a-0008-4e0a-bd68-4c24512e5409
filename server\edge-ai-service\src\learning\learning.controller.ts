import { Controller, Get } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { LearningService } from './learning.service';

@ApiTags('learning')
@Controller('learning')
export class LearningController {
  constructor(private readonly learningService: LearningService) {}

  @Get()
  async findAll() {
    return {
      success: true,
      data: await this.learningService.findAll(),
      message: '学习任务列表获取成功',
    };
  }
}

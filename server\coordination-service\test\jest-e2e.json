{"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testEnvironment": "node", "testRegex": ".e2e-spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["src/**/*.(t|j)s", "!src/**/*.spec.ts", "!src/**/*.interface.ts", "!src/**/*.dto.ts"], "coverageDirectory": "./coverage-e2e", "testTimeout": 60000, "setupFilesAfterEnv": ["<rootDir>/setup-e2e.ts"], "moduleNameMapping": {"^@/(.*)$": "<rootDir>/../src/$1"}}
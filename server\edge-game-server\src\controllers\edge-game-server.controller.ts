import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Delete, 
  Body, 
  Param, 
  Query, 
  HttpStatus, 
  HttpException,
  Logger 
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { EdgeNodeRegistrationService } from '../services/edge-node-registration.service';
import { EdgeLoadBalancerService } from '../services/edge-load-balancer.service';
import { EdgeHealthCheckService } from '../services/edge-health-check.service';

/**
 * 边缘游戏服务器主控制器
 * 提供边缘节点的核心API接口
 */
@ApiTags('边缘游戏服务器')
@Controller('edge')
export class EdgeGameServerController {
  private readonly logger = new Logger(EdgeGameServerController.name);

  constructor(
    private readonly registrationService: EdgeNodeRegistrationService,
    private readonly loadBalancerService: EdgeLoadBalancerService,
    private readonly healthCheckService: EdgeHealthCheckService,
  ) {}

  /**
   * 获取边缘节点信息
   */
  @Get('info')
  @ApiOperation({ summary: '获取边缘节点信息' })
  @ApiResponse({ status: 200, description: '成功获取节点信息' })
  getNodeInfo() {
    try {
      const nodeInfo = this.registrationService.getNodeInfo();
      const healthStats = this.healthCheckService.getHealthStats();
      const loadStats = this.loadBalancerService.getLoadBalanceStats();

      return {
        success: true,
        data: {
          node: nodeInfo,
          health: healthStats,
          load: loadStats,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      this.logger.error(`获取节点信息失败: ${error.message}`);
      throw new HttpException(
        '获取节点信息失败',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 更新节点状态
   */
  @Put('status')
  @ApiOperation({ summary: '更新节点状态' })
  @ApiResponse({ status: 200, description: '状态更新成功' })
  async updateNodeStatus(@Body() body: { status: 'online' | 'offline' | 'maintenance' }) {
    try {
      await this.registrationService.updateNodeStatus(body.status);
      
      return {
        success: true,
        message: `节点状态已更新为: ${body.status}`,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error(`更新节点状态失败: ${error.message}`);
      throw new HttpException(
        '更新节点状态失败',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取负载均衡信息
   */
  @Get('load-balance')
  @ApiOperation({ summary: '获取负载均衡信息' })
  @ApiResponse({ status: 200, description: '成功获取负载均衡信息' })
  getLoadBalanceInfo() {
    try {
      const stats = this.loadBalancerService.getLoadBalanceStats();
      const allLoads = this.loadBalancerService.getAllNodeLoads();

      return {
        success: true,
        data: {
          stats,
          nodeLoads: Object.fromEntries(allLoads),
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      this.logger.error(`获取负载均衡信息失败: ${error.message}`);
      throw new HttpException(
        '获取负载均衡信息失败',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 更新节点负载
   */
  @Put('load')
  @ApiOperation({ summary: '更新节点负载信息' })
  @ApiResponse({ status: 200, description: '负载信息更新成功' })
  updateNodeLoad(@Body() body: {
    nodeId: string;
    currentUsers?: number;
    cpuUsage?: number;
    memoryUsage?: number;
    networkLatency?: number;
  }) {
    try {
      this.loadBalancerService.updateNodeLoad(body.nodeId, {
        currentUsers: body.currentUsers,
        cpuUsage: body.cpuUsage,
        memoryUsage: body.memoryUsage,
        networkLatency: body.networkLatency,
      });

      return {
        success: true,
        message: '节点负载信息已更新',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error(`更新节点负载失败: ${error.message}`);
      throw new HttpException(
        '更新节点负载失败',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 选择最佳节点
   */
  @Post('select-node')
  @ApiOperation({ summary: '选择最佳边缘节点' })
  @ApiResponse({ status: 200, description: '成功选择节点' })
  selectBestNode(@Body() body: { availableNodes: string[] }) {
    try {
      const selectedNode = this.loadBalancerService.selectBestNode(body.availableNodes);

      if (!selectedNode) {
        throw new HttpException(
          '没有可用的边缘节点',
          HttpStatus.SERVICE_UNAVAILABLE
        );
      }

      return {
        success: true,
        data: {
          selectedNode,
          nodeLoad: this.loadBalancerService.getNodeLoad(selectedNode),
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      this.logger.error(`选择节点失败: ${error.message}`);
      throw new HttpException(
        error.message || '选择节点失败',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取可用节点列表
   */
  @Get('available-nodes')
  @ApiOperation({ summary: '获取可用节点列表' })
  @ApiResponse({ status: 200, description: '成功获取可用节点列表' })
  @ApiQuery({ name: 'allNodes', required: false, description: '所有节点列表（逗号分隔）' })
  getAvailableNodes(@Query('allNodes') allNodes?: string) {
    try {
      const nodeList = allNodes ? allNodes.split(',') : [];
      const availableNodes = this.loadBalancerService.getAvailableNodes(nodeList);

      return {
        success: true,
        data: {
          availableNodes,
          totalNodes: nodeList.length,
          availableCount: availableNodes.length,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      this.logger.error(`获取可用节点失败: ${error.message}`);
      throw new HttpException(
        '获取可用节点失败',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 检查节点是否过载
   */
  @Get('overload-check/:nodeId')
  @ApiOperation({ summary: '检查节点是否过载' })
  @ApiResponse({ status: 200, description: '成功检查节点负载状态' })
  @ApiParam({ name: 'nodeId', description: '节点ID' })
  checkNodeOverload(@Param('nodeId') nodeId: string) {
    try {
      const isOverloaded = this.loadBalancerService.isNodeOverloaded(nodeId);
      const nodeLoad = this.loadBalancerService.getNodeLoad(nodeId);

      return {
        success: true,
        data: {
          nodeId,
          isOverloaded,
          nodeLoad,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      this.logger.error(`检查节点负载失败: ${error.message}`);
      throw new HttpException(
        '检查节点负载失败',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 获取节点统计信息
   */
  @Get('stats')
  @ApiOperation({ summary: '获取边缘节点统计信息' })
  @ApiResponse({ status: 200, description: '成功获取统计信息' })
  getNodeStats() {
    try {
      const healthStats = this.healthCheckService.getHealthStats();
      const loadStats = this.loadBalancerService.getLoadBalanceStats();
      const nodeInfo = this.registrationService.getNodeInfo();
      const isRegistered = this.registrationService.isNodeRegistered();

      return {
        success: true,
        data: {
          registration: {
            isRegistered,
            nodeInfo
          },
          health: healthStats,
          load: loadStats,
          timestamp: new Date().toISOString()
        }
      };
    } catch (error) {
      this.logger.error(`获取统计信息失败: ${error.message}`);
      throw new HttpException(
        '获取统计信息失败',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * 重置统计信息
   */
  @Post('reset-stats')
  @ApiOperation({ summary: '重置统计信息' })
  @ApiResponse({ status: 200, description: '统计信息重置成功' })
  resetStats() {
    try {
      // 这里可以添加重置逻辑
      this.logger.log('统计信息重置请求');

      return {
        success: true,
        message: '统计信息已重置',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error(`重置统计信息失败: ${error.message}`);
      throw new HttpException(
        '重置统计信息失败',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}

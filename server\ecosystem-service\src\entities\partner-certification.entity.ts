import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { Partner } from './partner.entity';

export enum CertificationStatus {
  VALID = 'valid',
  EXPIRED = 'expired',
  REVOKED = 'revoked',
  PENDING = 'pending'
}

@Entity('partner_certifications')
@Index(['partnerId', 'status'])
export class PartnerCertification {
  @PrimaryGeneratedColumn('uuid')
  certificationId: string;

  @Column('uuid')
  @Index()
  partnerId: string;

  @Column({ length: 255 })
  name: string;

  @Column({ length: 255 })
  issuer: string;

  @Column({ length: 100 })
  level: string;

  @Column({ type: 'date' })
  validFrom: Date;

  @Column({ type: 'date' })
  validTo: Date;

  @Column({
    type: 'enum',
    enum: CertificationStatus,
    default: CertificationStatus.PENDING,
  })
  @Index()
  status: CertificationStatus;

  @Column('json', { nullable: true })
  requirements: string[];

  @Column('text', { nullable: true })
  description: string;

  @Column({ length: 500, nullable: true })
  certificateUrl: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => Partner, partner => partner.certifications, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'partnerId' })
  partner: Partner;
}

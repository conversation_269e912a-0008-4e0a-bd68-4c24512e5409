// Jest测试环境设置

// 设置测试超时时间
jest.setTimeout(30000);

// 模拟环境变量
process.env.NODE_ENV = 'test';
process.env.DB_HOST = 'localhost';
process.env.DB_PORT = '3306';
process.env.DB_USERNAME = 'test';
process.env.DB_PASSWORD = 'test';
process.env.DB_DATABASE = 'edge_ai_service_test';
process.env.REDIS_HOST = 'localhost';
process.env.REDIS_PORT = '6379';

// 全局测试钩子
beforeAll(async () => {
  console.log('🧪 开始运行边缘AI服务测试套件');
});

afterAll(async () => {
  console.log('✅ 边缘AI服务测试套件运行完成');
});

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
});

process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
});

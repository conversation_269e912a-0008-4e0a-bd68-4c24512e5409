import { <PERSON>, Post, Body } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { OptimizationService } from './optimization.service';

@ApiTags('optimization')
@Controller('optimization')
export class OptimizationController {
  constructor(private readonly optimizationService: OptimizationService) {}

  @Post()
  async optimize(@Body() data: { context: any; constraints: any }) {
    return {
      success: true,
      data: await this.optimizationService.optimize(data.context, data.constraints),
      message: '优化完成',
    };
  }
}

/**
 * 端到端测试设置文件
 */

import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { CoordinationServiceModule } from '../src/coordination-service.module';

// 全局应用实例
let app: INestApplication;

// 测试前设置
beforeAll(async () => {
  // 设置测试环境变量
  process.env.NODE_ENV = 'test';
  process.env.REDIS_HOST = 'localhost';
  process.env.REDIS_PORT = '6379';
  process.env.REDIS_DB = '2'; // 使用E2E测试数据库
  process.env.PORT = '3011'; // 使用不同端口避免冲突

  const moduleFixture: TestingModule = await Test.createTestingModule({
    imports: [CoordinationServiceModule],
  }).compile();

  app = moduleFixture.createNestApplication();
  await app.init();
});

// 测试后清理
afterAll(async () => {
  if (app) {
    await app.close();
  }
});

// 导出应用实例供测试使用
export const getApp = () => app;

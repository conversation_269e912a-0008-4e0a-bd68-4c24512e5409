import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
} from '@nestjs/swagger';
import { InferenceService } from './inference.service';

@ApiTags('inference')
@Controller('inference')
export class InferenceController {
  private readonly logger = new Logger(InferenceController.name);

  constructor(private readonly inferenceService: InferenceService) {}

  @Get('statistics')
  @ApiOperation({ summary: '获取推理统计信息' })
  @ApiResponse({ status: 200, description: '统计信息获取成功' })
  async getStatistics() {
    try {
      const statistics = await this.inferenceService.getStatistics();
      return {
        success: true,
        data: statistics,
        message: '推理统计信息获取成功',
      };
    } catch (error) {
      this.logger.error('获取推理统计信息失败', error);
      throw new HttpException(
        {
          success: false,
          message: '获取推理统计信息失败',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('requests/:requestId')
  @ApiOperation({ summary: '获取推理请求详情' })
  @ApiParam({ name: 'requestId', description: '请求ID' })
  @ApiResponse({ status: 200, description: '请求详情获取成功' })
  @ApiResponse({ status: 404, description: '请求不存在' })
  async getRequest(@Param('requestId') requestId: string) {
    try {
      const request = await this.inferenceService.findRequestByRequestId(requestId);
      if (!request) {
        throw new HttpException('推理请求不存在', HttpStatus.NOT_FOUND);
      }

      return {
        success: true,
        data: request,
        message: '推理请求详情获取成功',
      };
    } catch (error) {
      this.logger.error('获取推理请求详情失败', error);
      throw new HttpException(
        {
          success: false,
          message: '获取推理请求详情失败',
          error: error.message,
        },
        HttpStatus.NOT_FOUND,
      );
    }
  }

  @Get('results/:requestId')
  @ApiOperation({ summary: '获取推理结果' })
  @ApiParam({ name: 'requestId', description: '请求ID' })
  @ApiResponse({ status: 200, description: '推理结果获取成功' })
  @ApiResponse({ status: 404, description: '结果不存在' })
  async getResult(@Param('requestId') requestId: string) {
    try {
      const result = await this.inferenceService.findResultByRequestId(requestId);
      if (!result) {
        throw new HttpException('推理结果不存在', HttpStatus.NOT_FOUND);
      }

      return {
        success: true,
        data: result,
        message: '推理结果获取成功',
      };
    } catch (error) {
      this.logger.error('获取推理结果失败', error);
      throw new HttpException(
        {
          success: false,
          message: '获取推理结果失败',
          error: error.message,
        },
        HttpStatus.NOT_FOUND,
      );
    }
  }
}

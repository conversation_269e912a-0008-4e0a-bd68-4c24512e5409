# Prometheus 配置文件
# 群体协调服务监控配置

global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus 自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # 群体协调服务监控
  - job_name: 'coordination-service'
    static_configs:
      - targets: ['coordination-service:3010']
    metrics_path: '/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s

  # Redis 监控
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    metrics_path: '/metrics'
    scrape_interval: 15s

  # Node.js 应用监控
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 15s

# 告警规则配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093

# 远程写入配置（可选）
# remote_write:
#   - url: "http://remote-storage:9201/write"

# 远程读取配置（可选）
# remote_read:
#   - url: "http://remote-storage:9201/read"

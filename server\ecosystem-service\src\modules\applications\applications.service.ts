import { Injectable, Logger, NotFoundException } from '@nestjs/common';

@Injectable()
export class ApplicationsService {
  private readonly logger = new Logger(ApplicationsService.name);
  private applications: Map<string, any> = new Map();
  private submissions: Map<string, any> = new Map();
  private reviews: Map<string, any[]> = new Map();

  async getApplications(filters: any = {}) {
    const allApplications = Array.from(this.applications.values());
    
    let filteredApplications = allApplications;
    
    if (filters.type) {
      filteredApplications = filteredApplications.filter(app => app.type === filters.type);
    }
    
    if (filters.category) {
      filteredApplications = filteredApplications.filter(app => app.category === filters.category);
    }
    
    if (filters.status) {
      filteredApplications = filteredApplications.filter(app => app.status === filters.status);
    }
    
    return {
      applications: filteredApplications,
      total: filteredApplications.length,
      filters
    };
  }

  async getApplication(id: string) {
    const application = this.applications.get(id);
    if (!application) {
      throw new NotFoundException(`应用 ${id} 不存在`);
    }
    return application;
  }

  async submitApplication(application: any) {
    const submissionId = `sub_${Date.now()}`;
    
    const newSubmission = {
      ...application,
      submissionId,
      status: 'review',
      submittedAt: new Date(),
      reviewedAt: null,
      publishedAt: null
    };
    
    this.submissions.set(submissionId, newSubmission);
    
    this.logger.log(`应用提交: ${submissionId} - ${application.name}`);
    
    return {
      submissionId,
      status: 'review',
      message: '应用已提交，等待审核'
    };
  }

  async updateApplication(id: string, updateData: any) {
    const application = this.applications.get(id);
    if (!application) {
      throw new NotFoundException(`应用 ${id} 不存在`);
    }
    
    const updatedApplication = {
      ...application,
      ...updateData,
      lastUpdated: new Date()
    };
    
    this.applications.set(id, updatedApplication);
    
    this.logger.log(`应用更新: ${id}`);
    
    return updatedApplication;
  }

  async deleteApplication(id: string) {
    const application = this.applications.get(id);
    if (!application) {
      throw new NotFoundException(`应用 ${id} 不存在`);
    }
    
    this.applications.delete(id);
    this.reviews.delete(id);
    
    this.logger.log(`应用删除: ${id}`);
    
    return {
      message: '应用已删除',
      deletedId: id
    };
  }

  async getApplicationMetrics(id: string) {
    const application = this.applications.get(id);
    if (!application) {
      throw new NotFoundException(`应用 ${id} 不存在`);
    }
    
    return {
      appId: id,
      metrics: application.metrics || {
        downloads: 0,
        active_users: 0,
        average_rating: 0,
        total_reviews: 0,
        revenue: 0,
        support_tickets: 0,
        last_updated: new Date()
      },
      trends: {
        download_trend: 'stable',
        user_trend: 'growing',
        rating_trend: 'improving'
      }
    };
  }

  async getApplicationReviews(id: string) {
    const application = this.applications.get(id);
    if (!application) {
      throw new NotFoundException(`应用 ${id} 不存在`);
    }
    
    const reviews = this.reviews.get(id) || [];
    
    return {
      appId: id,
      reviews,
      total: reviews.length,
      averageRating: reviews.length > 0 
        ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length 
        : 0
    };
  }

  async addApplicationReview(id: string, review: any) {
    const application = this.applications.get(id);
    if (!application) {
      throw new NotFoundException(`应用 ${id} 不存在`);
    }
    
    const reviewId = `review_${Date.now()}`;
    const newReview = {
      ...review,
      reviewId,
      appId: id,
      created_at: new Date(),
      helpful_votes: 0
    };
    
    const appReviews = this.reviews.get(id) || [];
    appReviews.push(newReview);
    this.reviews.set(id, appReviews);
    
    this.logger.log(`评价添加: ${id} - 评分: ${review.rating}`);
    
    return {
      reviewId,
      message: '评价添加成功'
    };
  }
}

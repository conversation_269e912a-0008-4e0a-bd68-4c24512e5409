#!/bin/bash

# 群体协调服务测试脚本
# 用于运行各种测试

set -e

echo "🧪 群体协调服务测试套件"

# 解析命令行参数
TEST_TYPE=${1:-all}
COVERAGE=${2:-false}

case $TEST_TYPE in
    "unit")
        echo "🔬 运行单元测试..."
        if [ "$COVERAGE" = "true" ]; then
            npm run test:cov
        else
            npm run test
        fi
        ;;
    "e2e")
        echo "🌐 运行端到端测试..."
        # 确保测试环境干净
        echo "🧹 清理测试环境..."
        docker-compose -f docker-compose.test.yml down || true
        
        # 启动测试依赖服务
        echo "🚀 启动测试依赖服务..."
        docker-compose -f docker-compose.test.yml up -d redis
        
        # 等待 Redis 启动
        echo "⏳ 等待 Redis 启动..."
        sleep 5
        
        # 运行 E2E 测试
        npm run test:e2e
        
        # 清理测试环境
        echo "🧹 清理测试环境..."
        docker-compose -f docker-compose.test.yml down
        ;;
    "watch")
        echo "👀 运行监听模式测试..."
        npm run test:watch
        ;;
    "debug")
        echo "🐛 运行调试模式测试..."
        npm run test:debug
        ;;
    "lint")
        echo "📏 运行代码检查..."
        npm run lint
        ;;
    "format")
        echo "💅 运行代码格式化..."
        npm run format
        ;;
    "all")
        echo "🎯 运行完整测试套件..."
        
        # 代码检查
        echo "📏 1. 代码检查..."
        npm run lint
        
        # 代码格式化检查
        echo "💅 2. 代码格式化检查..."
        npm run format
        
        # 单元测试
        echo "🔬 3. 单元测试..."
        npm run test:cov
        
        # 端到端测试
        echo "🌐 4. 端到端测试..."
        # 确保测试环境干净
        docker-compose -f docker-compose.test.yml down || true
        
        # 启动测试依赖服务
        docker-compose -f docker-compose.test.yml up -d redis
        
        # 等待 Redis 启动
        sleep 5
        
        # 运行 E2E 测试
        npm run test:e2e
        
        # 清理测试环境
        docker-compose -f docker-compose.test.yml down
        
        echo "✅ 所有测试通过!"
        ;;
    *)
        echo "❌ 未知的测试类型: $TEST_TYPE"
        echo "📋 可用的测试类型:"
        echo "  unit    - 单元测试"
        echo "  e2e     - 端到端测试"
        echo "  watch   - 监听模式测试"
        echo "  debug   - 调试模式测试"
        echo "  lint    - 代码检查"
        echo "  format  - 代码格式化"
        echo "  all     - 完整测试套件"
        echo ""
        echo "📋 使用方法:"
        echo "  ./scripts/test.sh [测试类型] [是否生成覆盖率报告]"
        echo "  例如: ./scripts/test.sh unit true"
        exit 1
        ;;
esac

echo "🎉 测试完成!"

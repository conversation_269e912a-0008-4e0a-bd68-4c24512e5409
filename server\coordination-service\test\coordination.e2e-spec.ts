/**
 * 群体协调服务端到端测试
 */

import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import * as request from 'supertest';
import { CoordinationServiceModule } from '../src/coordination-service.module';
import { CoordinationTaskType } from '../src/services/group-coordination.service';

describe('CoordinationController (e2e)', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [CoordinationServiceModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();
  });

  afterEach(async () => {
    await app.close();
  });

  describe('/coordination/health (GET)', () => {
    it('应该返回健康状态', () => {
      return request(app.getHttpServer())
        .get('/coordination/health')
        .expect(200)
        .expect((res) => {
          expect(res.body.status).toBe('ok');
          expect(res.body.timestamp).toBeDefined();
        });
    });
  });

  describe('/coordination/tasks (POST)', () => {
    it('应该创建群体形成任务', () => {
      const createTaskDto = {
        type: CoordinationTaskType.GROUP_FORMATION,
        entityIds: ['entity1', 'entity2', 'entity3'],
        parameters: {
          maxDistance: 10,
          minGroupSize: 2,
        },
        priority: 1,
      };

      return request(app.getHttpServer())
        .post('/coordination/tasks')
        .send(createTaskDto)
        .expect(201)
        .expect((res) => {
          expect(res.body.taskId).toBeDefined();
          expect(typeof res.body.taskId).toBe('string');
        });
    });

    it('应该验证请求数据', () => {
      const invalidDto = {
        type: 'invalid_type',
        entityIds: [],
        parameters: {},
      };

      return request(app.getHttpServer())
        .post('/coordination/tasks')
        .send(invalidDto)
        .expect(400);
    });
  });

  describe('/coordination/groups/formation (POST)', () => {
    it('应该创建群体形成任务', () => {
      const formationDto = {
        entityIds: ['entity1', 'entity2'],
        strategy: 'proximity_grouping',
        maxDistance: 10,
        minGroupSize: 2,
      };

      return request(app.getHttpServer())
        .post('/coordination/groups/formation')
        .send(formationDto)
        .expect(201)
        .expect((res) => {
          expect(res.body.taskId).toBeDefined();
        });
    });
  });

  describe('/coordination/conflicts/report (POST)', () => {
    it('应该报告冲突', () => {
      const conflictDto = {
        type: 'resource_conflict',
        participants: ['entity1', 'entity2'],
        severity: 0.8,
        description: '资源争夺冲突',
      };

      return request(app.getHttpServer())
        .post('/coordination/conflicts/report')
        .send(conflictDto)
        .expect(201)
        .expect((res) => {
          expect(res.body.message).toBe('冲突报告已提交');
        });
    });
  });

  describe('/coordination/resources/allocation (POST)', () => {
    it('应该创建资源分配任务', () => {
      const allocationDto = {
        entityIds: ['entity1', 'entity2'],
        resourceType: 'computing_power',
        amount: 100,
        strategy: 'fair_allocation',
      };

      return request(app.getHttpServer())
        .post('/coordination/resources/allocation')
        .send(allocationDto)
        .expect(201)
        .expect((res) => {
          expect(res.body.taskId).toBeDefined();
        });
    });
  });

  describe('/coordination/conflicts/active (GET)', () => {
    it('应该返回活跃冲突列表', () => {
      return request(app.getHttpServer())
        .get('/coordination/conflicts/active')
        .expect(200)
        .expect((res) => {
          expect(Array.isArray(res.body.conflicts)).toBe(true);
        });
    });
  });

  describe('/coordination/resources/available (GET)', () => {
    it('应该返回可用资源列表', () => {
      return request(app.getHttpServer())
        .get('/coordination/resources/available')
        .expect(200)
        .expect((res) => {
          expect(Array.isArray(res.body.resources)).toBe(true);
        });
    });

    it('应该支持资源类型过滤', () => {
      return request(app.getHttpServer())
        .get('/coordination/resources/available?type=computing_power')
        .expect(200)
        .expect((res) => {
          expect(Array.isArray(res.body.resources)).toBe(true);
        });
    });
  });
});

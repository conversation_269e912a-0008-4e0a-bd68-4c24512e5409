import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import * as request from 'supertest';
import { AppModule } from '../src/app.module';
import { AuthService } from '../src/common/auth/auth.service';
import { createTestUser, createAdminUser } from '../src/test-utils/test-helpers';

describe('AppController (e2e)', () => {
  let app: INestApplication;
  let authService: AuthService;
  let userToken: string;
  let adminToken: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        AppModule,
        // 使用内存数据库进行测试
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: [__dirname + '/../src/**/*.entity{.ts,.js}'],
          synchronize: true,
          logging: false,
        }),
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    authService = moduleFixture.get<AuthService>(AuthService);

    // 生成测试用户令牌
    const testUser = createTestUser();
    const adminUser = createAdminUser();
    
    userToken = await authService.generateToken(testUser);
    adminToken = await authService.generateToken(adminUser);

    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('Public endpoints', () => {
    it('/ (GET) - should return service info', () => {
      return request(app.getHttpServer())
        .get('/')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('name', 'ecosystem-service');
          expect(res.body).toHaveProperty('status', 'running');
          expect(res.body).toHaveProperty('features');
        });
    });

    it('/version (GET) - should return version info', () => {
      return request(app.getHttpServer())
        .get('/version')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('version', '1.0.0');
          expect(res.body).toHaveProperty('nodeVersion');
          expect(res.body).toHaveProperty('environment');
        });
    });

    it('/health (GET) - should return health status', () => {
      return request(app.getHttpServer())
        .get('/health')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('status', 'ok');
          expect(res.body).toHaveProperty('service', 'ecosystem-service');
        });
    });

    it('/health/detailed (GET) - should return detailed health', () => {
      return request(app.getHttpServer())
        .get('/health/detailed')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('status', 'ok');
          expect(res.body).toHaveProperty('memory');
          expect(res.body).toHaveProperty('dependencies');
        });
    });

    it('/health/ready (GET) - should return readiness status', () => {
      return request(app.getHttpServer())
        .get('/health/ready')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('status');
          expect(res.body).toHaveProperty('checks');
        });
    });

    it('/health/live (GET) - should return liveness status', () => {
      return request(app.getHttpServer())
        .get('/health/live')
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('status', 'alive');
          expect(res.body).toHaveProperty('pid');
        });
    });
  });

  describe('Protected endpoints', () => {
    it('/partners (GET) - should require authentication', () => {
      return request(app.getHttpServer())
        .get('/partners')
        .expect(401);
    });

    it('/partners (GET) - should work with valid token', () => {
      return request(app.getHttpServer())
        .get('/partners')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('partners');
          expect(res.body).toHaveProperty('total');
          expect(res.body).toHaveProperty('filters');
        });
    });

    it('/partners (POST) - should require proper permissions', () => {
      const partnerData = {
        name: 'Test Partner',
        type: 'technology_provider',
        description: 'Test description',
        contactInfo: {
          primaryContact: {
            name: 'John Doe',
            email: '<EMAIL>',
            phone: '+**********',
            role: 'Manager',
          },
        },
      };

      return request(app.getHttpServer())
        .post('/partners')
        .set('Authorization', `Bearer ${userToken}`)
        .send(partnerData)
        .expect(403); // User doesn't have partners:create permission
    });

    it('/partners (POST) - should work with admin token', () => {
      const partnerData = {
        name: 'Test Partner',
        type: 'technology_provider',
        description: 'Test description',
        contactInfo: {
          primaryContact: {
            name: 'John Doe',
            email: '<EMAIL>',
            phone: '+**********',
            role: 'Manager',
          },
        },
      };

      return request(app.getHttpServer())
        .post('/partners')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(partnerData)
        .expect(201)
        .expect((res) => {
          expect(res.body).toHaveProperty('partnerId');
          expect(res.body).toHaveProperty('status', 'pending');
        });
    });
  });

  describe('Rate limiting', () => {
    it('should enforce rate limits', async () => {
      // 这个测试需要根据实际的速率限制配置来调整
      const requests = [];
      
      // 发送多个请求来触发速率限制
      for (let i = 0; i < 10; i++) {
        requests.push(
          request(app.getHttpServer())
            .get('/partners')
            .set('Authorization', `Bearer ${userToken}`)
        );
      }

      const responses = await Promise.all(requests);
      
      // 检查是否有请求被速率限制
      const rateLimitedResponses = responses.filter(res => res.status === 429);
      
      // 根据配置，可能会有一些请求被限制
      // 这里只是验证速率限制功能是否工作
      expect(responses.length).toBe(10);
    });
  });

  describe('Error handling', () => {
    it('should handle 404 for non-existent endpoints', () => {
      return request(app.getHttpServer())
        .get('/non-existent-endpoint')
        .expect(404);
    });

    it('should handle invalid JSON in request body', () => {
      return request(app.getHttpServer())
        .post('/partners')
        .set('Authorization', `Bearer ${adminToken}`)
        .set('Content-Type', 'application/json')
        .send('invalid json')
        .expect(400);
    });

    it('should handle missing required fields', () => {
      return request(app.getHttpServer())
        .post('/partners')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({}) // Empty body
        .expect(400);
    });
  });

  describe('CORS', () => {
    it('should include CORS headers', () => {
      return request(app.getHttpServer())
        .options('/')
        .expect(200)
        .expect((res) => {
          expect(res.headers).toHaveProperty('access-control-allow-origin');
        });
    });
  });
});

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Index,
} from 'typeorm';
import { StandardCertification } from './standard-certification.entity';
import { ComplianceRecord } from './compliance-record.entity';

export enum StandardStatus {
  DRAFT = 'draft',
  PUBLISHED = 'published',
  DEPRECATED = 'deprecated',
  RETIRED = 'retired'
}

@Entity('industry_standards')
@Index(['organization', 'status'])
export class IndustryStandard {
  @PrimaryGeneratedColumn('uuid')
  standardId: string;

  @Column({ length: 255 })
  @Index()
  name: string;

  @Column({ length: 255 })
  @Index()
  organization: string;

  @Column({ length: 50 })
  version: string;

  @Column('text')
  description: string;

  @Column('json', { nullable: true })
  scope: string[];

  @Column({
    type: 'enum',
    enum: StandardStatus,
    default: StandardStatus.DRAFT,
  })
  @Index()
  status: StandardStatus;

  // 标准要求 - 存储为JSON
  @Column('json', { nullable: true })
  requirements: Array<{
    requirementId: string;
    category: string;
    description: string;
    mandatory: boolean;
    verification_method: string;
    acceptance_criteria: string[];
  }>;

  // 合规级别 - 存储为JSON
  @Column('json', { nullable: true })
  compliance_levels: Array<{
    level: string;
    description: string;
    requirements: string[];
    benefits: string[];
  }>;

  // 认证流程 - 存储为JSON
  @Column('json', { nullable: true })
  certification_process: {
    steps: Array<{
      step: number;
      name: string;
      description: string;
      deliverables: string[];
      duration: number; // 天
    }>;
    duration: number; // 天
    cost: number;
    validity_period: number; // 年
    renewal_requirements: string[];
  };

  @Column({ length: 500, nullable: true })
  documentUrl: string;

  @Column({ length: 500, nullable: true })
  websiteUrl: string;

  @Column('json', { nullable: true })
  tags: string[];

  @Column({ type: 'timestamp' })
  published_at: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 关联关系
  @OneToMany(() => StandardCertification, certification => certification.standard)
  certifications: StandardCertification[];

  @OneToMany(() => ComplianceRecord, record => record.standard)
  complianceRecords: ComplianceRecord[];
}

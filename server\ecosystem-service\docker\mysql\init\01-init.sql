-- 生态系统服务数据库初始化脚本

-- 设置字符集
SET NAMES utf8mb4;
SET CHARACTER SET utf8mb4;

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS ecosystem_service 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE ecosystem_service;

-- 创建用户（如果不存在）
CREATE USER IF NOT EXISTS 'ecosystem_user'@'%' IDENTIFIED BY 'ecosystem_password';

-- 授权
GRANT ALL PRIVILEGES ON ecosystem_service.* TO 'ecosystem_user'@'%';
GRANT SELECT, INSERT, UPDATE, DELETE, CREATE, DROP, INDEX, ALTER ON ecosystem_service.* TO 'ecosystem_user'@'%';

-- 刷新权限
FLUSH PRIVILEGES;

-- 创建一些基础索引（TypeORM会创建表结构）
-- 这里只是预留，实际的表结构由TypeORM管理

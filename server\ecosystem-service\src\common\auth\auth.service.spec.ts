import { Test, TestingModule } from '@nestjs/testing';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { UnauthorizedException } from '@nestjs/common';
import { AuthService, User, JwtPayload } from './auth.service';
import { CacheService } from '../redis/cache.service';
import { createMockJwtService, createMockConfigService, createMockCacheService, createTestUser } from '../../test-utils/test-helpers';

describe('AuthService', () => {
  let service: AuthService;
  let jwtService: jest.Mocked<JwtService>;
  let configService: jest.Mocked<ConfigService>;
  let cacheService: jest.Mocked<CacheService>;

  const testUser: User = createTestUser();

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        {
          provide: JwtService,
          useValue: createMockJwtService(),
        },
        {
          provide: ConfigService,
          useValue: createMockConfigService(),
        },
        {
          provide: CacheService,
          useValue: createMockCacheService(),
        },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
    jwtService = module.get(JwtService);
    configService = module.get(ConfigService);
    cacheService = module.get(CacheService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('generateToken', () => {
    it('should generate a JWT token', async () => {
      const expectedToken = 'jwt-token';
      jwtService.sign.mockReturnValue(expectedToken);

      const result = await service.generateToken(testUser);

      expect(jwtService.sign).toHaveBeenCalledWith({
        sub: testUser.id,
        username: testUser.username,
        email: testUser.email,
        roles: testUser.roles,
        permissions: testUser.permissions,
        partnerId: testUser.partnerId,
        organizationId: testUser.organizationId,
      });
      expect(result).toBe(expectedToken);
    });
  });

  describe('validateToken', () => {
    it('should validate a valid token', async () => {
      const token = 'valid-token';
      const payload: JwtPayload = {
        sub: testUser.id,
        username: testUser.username,
        email: testUser.email,
        roles: testUser.roles,
        permissions: testUser.permissions,
      };

      jwtService.verify.mockReturnValue(payload);
      cacheService.exists.mockResolvedValue(false); // Not blacklisted

      const result = await service.validateToken(token);

      expect(jwtService.verify).toHaveBeenCalledWith(token);
      expect(result).toEqual({
        id: payload.sub,
        username: payload.username,
        email: payload.email,
        roles: payload.roles,
        permissions: payload.permissions,
        partnerId: payload.partnerId,
        organizationId: payload.organizationId,
      });
    });

    it('should return null for blacklisted token', async () => {
      const token = 'blacklisted-token';
      const payload: JwtPayload = {
        sub: testUser.id,
        username: testUser.username,
        email: testUser.email,
        roles: testUser.roles,
        permissions: testUser.permissions,
      };

      jwtService.verify.mockReturnValue(payload);
      cacheService.exists.mockResolvedValue(true); // Blacklisted

      await expect(service.validateToken(token)).rejects.toThrow(UnauthorizedException);
    });

    it('should return null for invalid token', async () => {
      const token = 'invalid-token';

      jwtService.verify.mockImplementation(() => {
        throw new Error('Invalid token');
      });

      const result = await service.validateToken(token);

      expect(result).toBeNull();
    });
  });

  describe('generateApiKey', () => {
    it('should generate an API key', async () => {
      const userId = 'user-id';
      const name = 'Test API Key';
      const options = {
        roles: ['user'],
        permissions: ['read'],
        rateLimit: { requests: 100, window: 60 },
        expiresIn: 30,
      };

      cacheService.generateKey.mockReturnValue('cache-key');
      cacheService.set.mockResolvedValue(true);
      cacheService.get.mockResolvedValue([]);

      const result = await service.generateApiKey(userId, name, options);

      expect(result).toHaveProperty('keyId');
      expect(result).toHaveProperty('apiKey');
      expect(typeof result.keyId).toBe('string');
      expect(typeof result.apiKey).toBe('string');
      expect(cacheService.set).toHaveBeenCalledTimes(2); // API key info and user keys list
    });
  });

  describe('validateApiKey', () => {
    it('should validate a valid API key', async () => {
      const apiKey = 'valid-api-key';
      const apiKeyInfo = {
        keyId: 'key-id',
        name: 'Test Key',
        userId: 'user-id',
        roles: ['user'],
        permissions: ['read'],
        isActive: true,
      };

      cacheService.generateKey.mockReturnValue('cache-key');
      cacheService.get.mockResolvedValue(apiKeyInfo);

      const result = await service.validateApiKey(apiKey);

      expect(result).toEqual(apiKeyInfo);
    });

    it('should return null for inactive API key', async () => {
      const apiKey = 'inactive-api-key';
      const apiKeyInfo = {
        keyId: 'key-id',
        name: 'Test Key',
        userId: 'user-id',
        roles: ['user'],
        permissions: ['read'],
        isActive: false,
      };

      cacheService.generateKey.mockReturnValue('cache-key');
      cacheService.get.mockResolvedValue(apiKeyInfo);

      const result = await service.validateApiKey(apiKey);

      expect(result).toBeNull();
    });

    it('should return null for expired API key', async () => {
      const apiKey = 'expired-api-key';
      const apiKeyInfo = {
        keyId: 'key-id',
        name: 'Test Key',
        userId: 'user-id',
        roles: ['user'],
        permissions: ['read'],
        isActive: true,
        expiresAt: new Date(Date.now() - 1000), // Expired
      };

      cacheService.generateKey.mockReturnValue('cache-key');
      cacheService.get.mockResolvedValue(apiKeyInfo);
      cacheService.del.mockResolvedValue(true);

      const result = await service.validateApiKey(apiKey);

      expect(result).toBeNull();
      expect(cacheService.del).toHaveBeenCalled(); // Should revoke expired key
    });

    it('should return null for non-existent API key', async () => {
      const apiKey = 'non-existent-key';

      cacheService.generateKey.mockReturnValue('cache-key');
      cacheService.get.mockResolvedValue(null);

      const result = await service.validateApiKey(apiKey);

      expect(result).toBeNull();
    });
  });

  describe('revokeToken', () => {
    it('should revoke a valid token', async () => {
      const token = 'valid-token';
      const payload = {
        sub: 'user-id',
        exp: Math.floor(Date.now() / 1000) + 3600, // Expires in 1 hour
      };

      jwtService.decode.mockReturnValue(payload);
      cacheService.generateKey.mockReturnValue('blacklist-key');
      cacheService.set.mockResolvedValue(true);

      const result = await service.revokeToken(token);

      expect(result).toBe(true);
      expect(cacheService.set).toHaveBeenCalledWith('blacklist-key', true, expect.any(Number));
    });

    it('should return false for invalid token', async () => {
      const token = 'invalid-token';

      jwtService.decode.mockReturnValue(null);

      const result = await service.revokeToken(token);

      expect(result).toBe(false);
    });
  });

  describe('hasPermission', () => {
    it('should return true for user with specific permission', () => {
      const user = createTestUser({ permissions: ['partners:read'] });

      const result = service.hasPermission(user, 'partners:read');

      expect(result).toBe(true);
    });

    it('should return true for user with wildcard permission', () => {
      const user = createTestUser({ permissions: ['*'] });

      const result = service.hasPermission(user, 'any:permission');

      expect(result).toBe(true);
    });

    it('should return false for user without permission', () => {
      const user = createTestUser({ permissions: ['partners:read'] });

      const result = service.hasPermission(user, 'partners:write');

      expect(result).toBe(false);
    });
  });

  describe('hasRole', () => {
    it('should return true for user with specific role', () => {
      const user = createTestUser({ roles: ['user'] });

      const result = service.hasRole(user, 'user');

      expect(result).toBe(true);
    });

    it('should return true for admin user', () => {
      const user = createTestUser({ roles: ['admin'] });

      const result = service.hasRole(user, 'any-role');

      expect(result).toBe(true);
    });

    it('should return false for user without role', () => {
      const user = createTestUser({ roles: ['user'] });

      const result = service.hasRole(user, 'admin');

      expect(result).toBe(false);
    });
  });

  describe('hasAnyRole', () => {
    it('should return true if user has any of the specified roles', () => {
      const user = createTestUser({ roles: ['user'] });

      const result = service.hasAnyRole(user, ['admin', 'user', 'manager']);

      expect(result).toBe(true);
    });

    it('should return false if user has none of the specified roles', () => {
      const user = createTestUser({ roles: ['guest'] });

      const result = service.hasAnyRole(user, ['admin', 'user', 'manager']);

      expect(result).toBe(false);
    });
  });
});

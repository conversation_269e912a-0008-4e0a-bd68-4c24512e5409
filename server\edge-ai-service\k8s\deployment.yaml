apiVersion: apps/v1
kind: Deployment
metadata:
  name: edge-ai-service
  labels:
    app: edge-ai-service
    version: v1
spec:
  replicas: 3
  selector:
    matchLabels:
      app: edge-ai-service
  template:
    metadata:
      labels:
        app: edge-ai-service
        version: v1
    spec:
      containers:
      - name: edge-ai-service
        image: edge-ai-service:latest
        ports:
        - containerPort: 3006
        env:
        - name: NODE_ENV
          value: "production"
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: edge-ai-secrets
              key: db-host
        - name: DB_USERNAME
          valueFrom:
            secretKeyRef:
              name: edge-ai-secrets
              key: db-username
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: edge-ai-secrets
              key: db-password
        - name: DB_DATABASE
          valueFrom:
            configMapKeyRef:
              name: edge-ai-config
              key: db-database
        - name: REDIS_HOST
          valueFrom:
            configMapKeyRef:
              name: edge-ai-config
              key: redis-host
        - name: REDIS_PORT
          valueFrom:
            configMapKeyRef:
              name: edge-ai-config
              key: redis-port
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/v1/edge/statistics
            port: 3006
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/v1/edge/statistics
            port: 3006
          initialDelaySeconds: 5
          periodSeconds: 5
        volumeMounts:
        - name: logs
          mountPath: /app/logs
        - name: models
          mountPath: /app/models
      volumes:
      - name: logs
        persistentVolumeClaim:
          claimName: edge-ai-logs-pvc
      - name: models
        persistentVolumeClaim:
          claimName: edge-ai-models-pvc

---
apiVersion: v1
kind: Service
metadata:
  name: edge-ai-service
  labels:
    app: edge-ai-service
spec:
  selector:
    app: edge-ai-service
  ports:
  - name: http
    port: 80
    targetPort: 3006
    protocol: TCP
  type: ClusterIP

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: edge-ai-config
data:
  db-database: "edge_ai_service"
  redis-host: "redis-service"
  redis-port: "6379"
  log-level: "info"

---
apiVersion: v1
kind: Secret
metadata:
  name: edge-ai-secrets
type: Opaque
data:
  db-host: bXlzcWwtc2VydmljZQ== # mysql-service (base64)
  db-username: ZWRnZV9haQ== # edge_ai (base64)
  db-password: ZWRnZV9haV9wYXNzd29yZA== # edge_ai_password (base64)

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: edge-ai-logs-pvc
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: edge-ai-models-pvc
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 50Gi

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: edge-ai-ingress
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "false"
spec:
  rules:
  - host: edge-ai.local
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: edge-ai-service
            port:
              number: 80

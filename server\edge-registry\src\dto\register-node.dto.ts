import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, IsArray, IsObject, ValidateNested, Min, Max, IsEnum } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * 节点位置信息DTO
 */
export class NodeLocationDto {
  @ApiProperty({ description: '纬度', example: 39.9042 })
  @IsNumber()
  @Min(-90)
  @Max(90)
  latitude: number;

  @ApiProperty({ description: '经度', example: 116.4074 })
  @IsNumber()
  @Min(-180)
  @Max(180)
  longitude: number;

  @ApiProperty({ description: '城市', example: '北京' })
  @IsString()
  city: string;

  @ApiProperty({ description: '国家', example: '中国' })
  @IsString()
  country: string;
}

/**
 * 节点资源信息DTO
 */
export class NodeResourcesDto {
  @ApiProperty({ description: 'CPU资源', example: '2000m' })
  @IsString()
  cpu: string;

  @ApiProperty({ description: '内存资源', example: '4Gi' })
  @IsString()
  memory: string;

  @ApiProperty({ description: '存储资源', example: '20Gi' })
  @IsString()
  storage: string;
}

/**
 * 节点能力信息DTO
 */
export class NodeCapabilitiesDto {
  @ApiProperty({ description: '最大用户数', example: 100 })
  @IsNumber()
  @Min(1)
  maxUsers: number;

  @ApiProperty({ 
    description: '支持的功能列表', 
    example: ['webrtc', 'realtime-sync', 'ai-inference'] 
  })
  @IsArray()
  @IsString({ each: true })
  supportedFeatures: string[];

  @ApiProperty({ description: '资源配置', type: NodeResourcesDto })
  @ValidateNested()
  @Type(() => NodeResourcesDto)
  resources: NodeResourcesDto;
}

/**
 * 注册边缘节点DTO
 */
export class RegisterNodeDto {
  @ApiProperty({ description: '节点ID', example: 'edge-node-001' })
  @IsString()
  nodeId: string;

  @ApiProperty({ description: '节点区域', example: 'beijing-zone-1' })
  @IsString()
  region: string;

  @ApiProperty({ description: '节点端点地址', example: 'http://*************:8080' })
  @IsString()
  endpoint: string;

  @ApiProperty({ description: '节点能力配置', type: NodeCapabilitiesDto })
  @ValidateNested()
  @Type(() => NodeCapabilitiesDto)
  capabilities: NodeCapabilitiesDto;

  @ApiPropertyOptional({ description: '节点位置信息', type: NodeLocationDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => NodeLocationDto)
  location?: NodeLocationDto;

  @ApiPropertyOptional({ description: '节点版本', example: '1.0.0' })
  @IsOptional()
  @IsString()
  version?: string;

  @ApiPropertyOptional({ description: '节点元数据', example: { environment: 'production' } })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}

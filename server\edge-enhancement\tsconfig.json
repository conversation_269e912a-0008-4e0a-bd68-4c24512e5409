{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2020", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": false, "noImplicitAny": false, "strictBindCallApply": false, "forceConsistentCasingInFileNames": false, "noFallthroughCasesInSwitch": false, "paths": {"@/*": ["src/*"], "@/common/*": ["src/common/*"], "@/config/*": ["src/config/*"], "@/dto/*": ["src/dto/*"], "@/entities/*": ["src/entities/*"], "@/modules/*": ["src/modules/*"], "@/services/*": ["src/services/*"], "@/utils/*": ["src/utils/*"]}, "lib": ["ES2020"], "esModuleInterop": true, "resolveJsonModule": true, "moduleResolution": "node"}, "include": ["src/**/*", "test/**/*"], "exclude": ["node_modules", "dist", "coverage", "**/*.spec.ts", "**/*.e2e-spec.ts"]}
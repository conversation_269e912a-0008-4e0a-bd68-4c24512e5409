{"name": "deeplearning-service", "version": "1.0.0", "description": "深度学习推理服务 - 提供AI模型推理、管理和监控功能", "author": "DL Engine Team", "private": true, "license": "MIT", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "lint:check": "eslint \"{src,apps,libs,test}/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:e2e:watch": "jest --config ./test/jest-e2e.json --watch", "test:e2e:cov": "jest --config ./test/jest-e2e.json --coverage", "dev": "chmod +x scripts/dev.sh && ./scripts/dev.sh", "deploy": "chmod +x scripts/deploy.sh && ./scripts/deploy.sh", "test:all": "chmod +x scripts/test.sh && ./scripts/test.sh all", "docker:build": "docker build -t deeplearning-service .", "docker:run": "docker run -p 3020:3020 deeplearning-service", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f deeplearning-service", "clean": "rm -rf dist coverage .jest-cache", "precommit": "npm run lint:check && npm run format:check && npm run test", "prepare": "npm run build"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^2.0.0", "@nestjs/microservices": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^3.0.0", "@nestjs/swagger": "^7.0.0", "bullmq": "^4.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "cors": "^2.8.5", "dotenv": "^16.0.0", "eventemitter2": "^6.4.7", "express": "^4.18.2", "ioredis": "^5.3.0", "luxon": "^3.3.0", "multer": "^1.4.5-lts.1", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "swagger-ui-express": "^5.0.0", "uuid": "^9.0.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/luxon": "^3.3.0", "@types/multer": "^1.4.7", "@types/node": "^20.3.1", "@types/supertest": "^6.0.0", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "jest-watch-typeahead": "^2.2.2", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.1.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "keywords": ["<PERSON><PERSON><PERSON>", "deep-learning", "ai", "machine-learning", "inference", "model-serving", "microservice", "typescript"], "repository": {"type": "git", "url": "https://github.com/your-org/deeplearning-service.git"}, "bugs": {"url": "https://github.com/your-org/deeplearning-service/issues"}, "homepage": "https://github.com/your-org/deeplearning-service#readme"}
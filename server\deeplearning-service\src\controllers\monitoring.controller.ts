/**
 * 监控控制器
 * 
 * 处理监控和统计相关的HTTP请求
 */

import {
  Controller,
  Get,
  Query,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { ModelInferenceService } from '../services/model-inference.service';
import { MonitoringService } from '../services/monitoring.service';
import {
  InferenceStatsDto,
  SystemMetricsDto,
  ModelUtilizationDto,
  PerformanceReportDto,
} from '../dto/monitoring.dto';

/**
 * 监控控制器
 */
@ApiTags('monitoring')
@Controller('monitoring')
export class MonitoringController {
  private readonly logger = new Logger(MonitoringController.name);

  constructor(
    private readonly inferenceService: ModelInferenceService,
    private readonly monitoringService: MonitoringService,
  ) {}

  /**
   * 获取推理统计信息
   */
  @Get('stats')
  @ApiOperation({ summary: '获取推理统计', description: '获取系统推理统计信息' })
  @ApiResponse({ status: 200, description: '推理统计信息', type: InferenceStatsDto })
  async getInferenceStats(): Promise<InferenceStatsDto> {
    try {
      const stats = this.inferenceService.getInferenceStats();

      return {
        totalRequests: stats.totalRequests,
        activeRequests: stats.activeRequests,
        queuedRequests: stats.queuedRequests,
        completedRequests: stats.completedRequests,
        failedRequests: stats.failedRequests,
        averageLatency: stats.averageLatency,
        throughput: stats.throughput,
        errorRate: stats.errorRate,
        modelUtilization: stats.modelUtilization,
        resourceUsage: stats.resourceUsage,
      };

    } catch (error) {
      this.logger.error('获取推理统计失败:', error);
      throw new HttpException('获取推理统计失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取系统指标
   */
  @Get('metrics')
  @ApiOperation({ summary: '获取系统指标', description: '获取系统性能指标' })
  @ApiResponse({ status: 200, description: '系统指标', type: SystemMetricsDto })
  async getSystemMetrics(): Promise<SystemMetricsDto> {
    try {
      const metrics = await this.monitoringService.getSystemMetrics();

      return {
        timestamp: Date.now(),
        cpu: {
          usage: metrics.cpu.usage,
          cores: metrics.cpu.cores,
          loadAverage: metrics.cpu.loadAverage,
        },
        memory: {
          total: metrics.memory.total,
          used: metrics.memory.used,
          free: metrics.memory.free,
          usage: metrics.memory.usage,
        },
        disk: {
          total: metrics.disk.total,
          used: metrics.disk.used,
          free: metrics.disk.free,
          usage: metrics.disk.usage,
        },
        network: {
          bytesIn: metrics.network.bytesIn,
          bytesOut: metrics.network.bytesOut,
          packetsIn: metrics.network.packetsIn,
          packetsOut: metrics.network.packetsOut,
        },
        gpu: metrics.gpu ? {
          usage: metrics.gpu.usage,
          memory: metrics.gpu.memory,
          temperature: metrics.gpu.temperature,
        } : undefined,
      };

    } catch (error) {
      this.logger.error('获取系统指标失败:', error);
      throw new HttpException('获取系统指标失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取模型利用率
   */
  @Get('utilization')
  @ApiOperation({ summary: '获取模型利用率', description: '获取各模型的利用率统计' })
  @ApiResponse({ status: 200, description: '模型利用率', type: [ModelUtilizationDto] })
  async getModelUtilization(
    @Query('timeRange') timeRange?: string,
  ): Promise<ModelUtilizationDto[]> {
    try {
      const models = this.inferenceService.getModels();
      const stats = this.inferenceService.getInferenceStats();

      const utilization: ModelUtilizationDto[] = models.map(model => ({
        modelId: model.id,
        modelName: model.name,
        requestCount: stats.modelUtilization[model.id] || 0,
        averageLatency: model.metrics.averageLatency,
        errorRate: model.metrics.errorRate,
        throughput: model.metrics.throughput,
        memoryUsage: model.memoryUsage,
        lastUsed: model.lastUsed,
        utilizationRate: this.calculateUtilizationRate(model.metrics.lastHourRequests),
      }));

      return utilization.sort((a, b) => b.requestCount - a.requestCount);

    } catch (error) {
      this.logger.error('获取模型利用率失败:', error);
      throw new HttpException('获取模型利用率失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取性能报告
   */
  @Get('report')
  @ApiOperation({ summary: '获取性能报告', description: '获取系统性能报告' })
  @ApiResponse({ status: 200, description: '性能报告', type: PerformanceReportDto })
  async getPerformanceReport(
    @Query('startTime') startTime?: number,
    @Query('endTime') endTime?: number,
  ): Promise<PerformanceReportDto> {
    try {
      const report = await this.monitoringService.generatePerformanceReport(startTime, endTime);

      return {
        reportId: `report_${Date.now()}`,
        timeRange: {
          start: report.timeRange.start,
          end: report.timeRange.end,
        },
        summary: {
          totalRequests: report.summary.totalRequests,
          successRate: report.summary.successRate,
          averageLatency: report.summary.averageLatency,
          peakThroughput: report.summary.peakThroughput,
          errorCount: report.summary.errorCount,
        },
        modelPerformance: report.modelPerformance,
        systemHealth: {
          cpuUsage: report.systemHealth.cpuUsage,
          memoryUsage: report.systemHealth.memoryUsage,
          diskUsage: report.systemHealth.diskUsage,
          networkTraffic: report.systemHealth.networkTraffic,
        },
        alerts: report.alerts,
        recommendations: report.recommendations,
        generatedAt: Date.now(),
      };

    } catch (error) {
      this.logger.error('获取性能报告失败:', error);
      throw new HttpException('获取性能报告失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取实时指标
   */
  @Get('realtime')
  @ApiOperation({ summary: '获取实时指标', description: '获取实时系统指标' })
  @ApiResponse({ status: 200, description: '实时指标' })
  async getRealtimeMetrics(): Promise<{
    timestamp: number;
    activeRequests: number;
    queueSize: number;
    throughput: number;
    errorRate: number;
    cpuUsage: number;
    memoryUsage: number;
  }> {
    try {
      const stats = this.inferenceService.getInferenceStats();
      const systemMetrics = await this.monitoringService.getSystemMetrics();

      return {
        timestamp: Date.now(),
        activeRequests: stats.activeRequests,
        queueSize: stats.queuedRequests,
        throughput: stats.throughput,
        errorRate: stats.errorRate,
        cpuUsage: systemMetrics.cpu.usage,
        memoryUsage: systemMetrics.memory.usage,
      };

    } catch (error) {
      this.logger.error('获取实时指标失败:', error);
      throw new HttpException('获取实时指标失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取告警信息
   */
  @Get('alerts')
  @ApiOperation({ summary: '获取告警信息', description: '获取系统告警信息' })
  @ApiResponse({ status: 200, description: '告警信息' })
  async getAlerts(
    @Query('severity') severity?: string,
    @Query('limit') limit?: number,
  ): Promise<{
    alerts: Array<{
      id: string;
      type: string;
      severity: string;
      message: string;
      timestamp: number;
      resolved: boolean;
    }>;
    total: number;
  }> {
    try {
      const alerts = await this.monitoringService.getAlerts(severity, limit);

      return {
        alerts,
        total: alerts.length,
      };

    } catch (error) {
      this.logger.error('获取告警信息失败:', error);
      throw new HttpException('获取告警信息失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取队列状态
   */
  @Get('queue')
  @ApiOperation({ summary: '获取队列状态', description: '获取推理队列状态' })
  @ApiResponse({ status: 200, description: '队列状态' })
  async getQueueStatus(): Promise<{
    totalJobs: number;
    waitingJobs: number;
    activeJobs: number;
    completedJobs: number;
    failedJobs: number;
    averageWaitTime: number;
  }> {
    try {
      const queueStatus = await this.monitoringService.getQueueStatus();

      return {
        totalJobs: queueStatus.totalJobs,
        waitingJobs: queueStatus.waitingJobs,
        activeJobs: queueStatus.activeJobs,
        completedJobs: queueStatus.completedJobs,
        failedJobs: queueStatus.failedJobs,
        averageWaitTime: queueStatus.averageWaitTime,
      };

    } catch (error) {
      this.logger.error('获取队列状态失败:', error);
      throw new HttpException('获取队列状态失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 计算利用率
   */
  private calculateUtilizationRate(lastHourRequests: number): number {
    // 简化的利用率计算：基于最近一小时的请求数
    const maxRequestsPerHour = 1000; // 假设的最大处理能力
    return Math.min(lastHourRequests / maxRequestsPerHour, 1.0);
  }
}

import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  Index,
} from 'typeorm';
import { PartnerCertification } from './partner-certification.entity';
import { PartnerAgreement } from './partner-agreement.entity';

export enum PartnerType {
  TECHNOLOGY_PROVIDER = 'technology_provider',
  SYSTEM_INTEGRATOR = 'system_integrator',
  SOLUTION_PROVIDER = 'solution_provider',
  HARDWARE_VENDOR = 'hardware_vendor',
  SOFTWARE_VENDOR = 'software_vendor',
  CONSULTING_FIRM = 'consulting_firm',
  RESEARCH_INSTITUTE = 'research_institute',
  INDUSTRY_ASSOCIATION = 'industry_association'
}

export enum PartnerTier {
  PLATINUM = 'platinum',
  GOLD = 'gold',
  SILVER = 'silver',
  BRONZE = 'bronze',
  CERTIFIED = 'certified'
}

export enum PartnerStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  SUSPENDED = 'suspended'
}

@Entity('partners')
@Index(['type', 'tier', 'status'])
export class Partner {
  @PrimaryGeneratedColumn('uuid')
  partnerId: string;

  @Column({ length: 255 })
  @Index()
  name: string;

  @Column({
    type: 'enum',
    enum: PartnerType,
  })
  type: PartnerType;

  @Column({
    type: 'enum',
    enum: PartnerTier,
    default: PartnerTier.CERTIFIED,
  })
  tier: PartnerTier;

  @Column('text')
  description: string;

  @Column({ length: 500, nullable: true })
  website: string;

  @Column({
    type: 'enum',
    enum: PartnerStatus,
    default: PartnerStatus.PENDING,
  })
  @Index()
  status: PartnerStatus;

  // 联系信息 - 存储为JSON
  @Column('json')
  contactInfo: {
    primaryContact: {
      name: string;
      email: string;
      phone: string;
      role: string;
    };
    technicalContact?: {
      name: string;
      email: string;
      phone: string;
      role: string;
    };
    businessContact?: {
      name: string;
      email: string;
      phone: string;
      role: string;
    };
    supportContact?: {
      name: string;
      email: string;
      phone: string;
      role: string;
    };
  };

  // 合作伙伴能力 - 存储为JSON
  @Column('json', { nullable: true })
  capabilities: {
    technologies: string[];
    industries: string[];
    regions: string[];
    languages: string[];
    certifications: string[];
    specializations: string[];
    supportLevels: string[];
  };

  // 性能指标 - 存储为JSON
  @Column('json', { nullable: true })
  performance: {
    revenue: number;
    customers: number;
    projects: number;
    satisfaction: number; // %
    support_rating: number; // 1-5
    certification_compliance: number; // %
    last_evaluation: Date;
  };

  @Column({ type: 'timestamp', nullable: true })
  joinedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  lastActivity: Date;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 关联关系
  @OneToMany(() => PartnerCertification, certification => certification.partner)
  certifications: PartnerCertification[];

  @OneToMany(() => PartnerAgreement, agreement => agreement.partner)
  agreements: PartnerAgreement[];
}

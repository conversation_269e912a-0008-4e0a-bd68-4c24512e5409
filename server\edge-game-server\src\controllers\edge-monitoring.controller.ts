import { Controller, Get, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { EdgeMonitoringService } from '../services/edge-monitoring.service';

/**
 * 边缘监控控制器
 */
@ApiTags('边缘监控')
@Controller('monitoring')
export class EdgeMonitoringController {
  private readonly logger = new Logger(EdgeMonitoringController.name);

  constructor(private readonly monitoringService: EdgeMonitoringService) {}

  /**
   * 获取监控指标
   */
  @Get('metrics')
  @ApiOperation({ summary: '获取监控指标' })
  @ApiResponse({ status: 200, description: '成功获取监控指标' })
  async getMetrics() {
    try {
      const metrics = await this.monitoringService.getMetrics();
      
      return {
        success: true,
        data: metrics,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error(`获取监控指标失败: ${error.message}`);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * 获取系统状态
   */
  @Get('status')
  @ApiOperation({ summary: '获取系统状态' })
  @ApiResponse({ status: 200, description: '成功获取系统状态' })
  getSystemStatus() {
    try {
      return {
        success: true,
        data: {
          status: 'healthy',
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          version: process.version
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error(`获取系统状态失败: ${error.message}`);
      return {
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

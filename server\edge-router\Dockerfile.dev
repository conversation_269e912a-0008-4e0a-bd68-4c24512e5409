# 开发环境Dockerfile
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 安装开发工具
RUN apk add --no-cache curl

# 复制package.json和package-lock.json
COPY package*.json ./

# 安装所有依赖（包括开发依赖）
RUN npm install

# 复制源代码
COPY . .

# 暴露端口
EXPOSE 3012 9229

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3012/api/health || exit 1

# 启动开发服务器
CMD ["npm", "run", "start:dev"]

-- 边缘计算增强服务 - 种子数据脚本
-- 插入测试和演示数据

-- 插入更多测试节点
INSERT INTO scheduler_nodes (node_id, name, capacity, current_load, status, location, metadata) VALUES
('edge-node-004', '边缘节点004', 1.2, 0.3, 'active', '{"region": "north", "zone": "zone-d", "datacenter": "dc-north-01"}', '{"cpu_cores": 8, "memory_gb": 16, "storage_gb": 500}'),
('edge-node-005', '边缘节点005', 0.8, 0.1, 'active', '{"region": "south", "zone": "zone-e", "datacenter": "dc-south-01"}', '{"cpu_cores": 4, "memory_gb": 8, "storage_gb": 250}'),
('edge-node-006', '边缘节点006', 2.5, 0.7, 'active', '{"region": "east", "zone": "zone-f", "datacenter": "dc-east-02"}', '{"cpu_cores": 16, "memory_gb": 32, "storage_gb": 1000}'),
('edge-node-007', '边缘节点007', 1.0, 0.0, 'maintenance', '{"region": "west", "zone": "zone-g", "datacenter": "dc-west-02"}', '{"cpu_cores": 8, "memory_gb": 16, "storage_gb": 500}'),
('edge-node-008', '边缘节点008', 1.8, 0.4, 'active', '{"region": "central", "zone": "zone-h", "datacenter": "dc-central-02"}', '{"cpu_cores": 12, "memory_gb": 24, "storage_gb": 750}')
ON CONFLICT (node_id) DO NOTHING;

-- 插入历史负载数据（过去24小时的模拟数据）
INSERT INTO scheduler_load_history (node_id, load_value, cpu_usage, memory_usage, network_usage, timestamp, metadata)
SELECT 
    node_id,
    ROUND((RANDOM() * 0.8 + 0.1)::NUMERIC, 2) as load_value,
    ROUND((RANDOM() * 80 + 10)::NUMERIC, 2) as cpu_usage,
    ROUND((RANDOM() * 70 + 20)::NUMERIC, 2) as memory_usage,
    ROUND((RANDOM() * 1000 + 100)::NUMERIC, 2) as network_usage,
    CURRENT_TIMESTAMP - (INTERVAL '1 hour' * generate_series(0, 23)) as timestamp,
    '{"source": "monitoring_agent", "collection_method": "automated"}'::JSONB
FROM scheduler_nodes 
WHERE status = 'active'
CROSS JOIN generate_series(0, 23);

-- 插入负载预测数据
INSERT INTO scheduler_predictions (node_id, predicted_load, confidence, time_window, prediction_timestamp, model_version)
SELECT 
    node_id,
    ROUND((RANDOM() * 0.9 + 0.1)::NUMERIC, 2) as predicted_load,
    ROUND((RANDOM() * 0.3 + 0.7)::NUMERIC, 4) as confidence,
    30 as time_window,
    CURRENT_TIMESTAMP - (INTERVAL '1 hour' * generate_series(0, 11)) as prediction_timestamp,
    'v1.2.3'
FROM scheduler_nodes 
WHERE status = 'active'
CROSS JOIN generate_series(0, 11);

-- 插入缓存统计历史数据
INSERT INTO cache_statistics (cache_level, hit_count, miss_count, total_requests, hit_rate, avg_response_time, timestamp)
VALUES
-- L1缓存数据
('L1', 8500, 1500, 10000, 0.85, 2.5, CURRENT_TIMESTAMP - INTERVAL '1 hour'),
('L1', 9200, 1800, 11000, 0.836, 2.3, CURRENT_TIMESTAMP - INTERVAL '2 hours'),
('L1', 7800, 1200, 9000, 0.867, 2.7, CURRENT_TIMESTAMP - INTERVAL '3 hours'),
('L1', 9500, 1500, 11000, 0.864, 2.4, CURRENT_TIMESTAMP - INTERVAL '4 hours'),

-- L2缓存数据
('L2', 6500, 3500, 10000, 0.65, 15.2, CURRENT_TIMESTAMP - INTERVAL '1 hour'),
('L2', 7200, 3800, 11000, 0.655, 14.8, CURRENT_TIMESTAMP - INTERVAL '2 hours'),
('L2', 5800, 3200, 9000, 0.644, 16.1, CURRENT_TIMESTAMP - INTERVAL '3 hours'),
('L2', 7100, 3900, 11000, 0.645, 15.5, CURRENT_TIMESTAMP - INTERVAL '4 hours'),

-- L3缓存数据
('L3', 4500, 5500, 10000, 0.45, 85.7, CURRENT_TIMESTAMP - INTERVAL '1 hour'),
('L3', 5200, 5800, 11000, 0.473, 82.3, CURRENT_TIMESTAMP - INTERVAL '2 hours'),
('L3', 3800, 5200, 9000, 0.422, 89.1, CURRENT_TIMESTAMP - INTERVAL '3 hours'),
('L3', 4900, 6100, 11000, 0.445, 86.4, CURRENT_TIMESTAMP - INTERVAL '4 hours');

-- 插入用户行为模式数据
INSERT INTO user_behavior_patterns (user_id, access_pattern, frequency_score, last_access, prediction_accuracy)
VALUES
('user_001', '{"pages": ["/dashboard", "/reports", "/settings"], "peak_hours": [9, 14, 16], "avg_session_duration": 45}', 0.85, CURRENT_TIMESTAMP - INTERVAL '30 minutes', 0.78),
('user_002', '{"pages": ["/analytics", "/data", "/export"], "peak_hours": [10, 15, 18], "avg_session_duration": 60}', 0.92, CURRENT_TIMESTAMP - INTERVAL '15 minutes', 0.82),
('user_003', '{"pages": ["/monitoring", "/alerts", "/logs"], "peak_hours": [8, 12, 20], "avg_session_duration": 35}', 0.76, CURRENT_TIMESTAMP - INTERVAL '2 hours', 0.71),
('user_004', '{"pages": ["/admin", "/users", "/config"], "peak_hours": [9, 13, 17], "avg_session_duration": 25}', 0.88, CURRENT_TIMESTAMP - INTERVAL '1 hour', 0.85),
('user_005', '{"pages": ["/api", "/docs", "/tools"], "peak_hours": [11, 16, 19], "avg_session_duration": 40}', 0.79, CURRENT_TIMESTAMP - INTERVAL '45 minutes', 0.73);

-- 插入缓存预加载日志
INSERT INTO cache_preload_logs (user_id, cache_key, preload_reason, success, response_time, cache_level, timestamp)
VALUES
('user_001', 'dashboard_data_user_001', 'behavior_prediction', true, 125.5, 'L1', CURRENT_TIMESTAMP - INTERVAL '30 minutes'),
('user_001', 'reports_summary_user_001', 'access_pattern', true, 89.2, 'L2', CURRENT_TIMESTAMP - INTERVAL '25 minutes'),
('user_002', 'analytics_chart_user_002', 'frequency_based', true, 156.8, 'L1', CURRENT_TIMESTAMP - INTERVAL '20 minutes'),
('user_002', 'data_export_user_002', 'time_based', false, 0, 'L3', CURRENT_TIMESTAMP - INTERVAL '15 minutes'),
('user_003', 'monitoring_alerts_user_003', 'behavior_prediction', true, 78.3, 'L1', CURRENT_TIMESTAMP - INTERVAL '10 minutes'),
('user_004', 'admin_panel_user_004', 'access_pattern', true, 234.7, 'L2', CURRENT_TIMESTAMP - INTERVAL '5 minutes'),
('user_005', 'api_docs_user_005', 'frequency_based', true, 45.6, 'L1', CURRENT_TIMESTAMP - INTERVAL '2 minutes');

-- 插入网络质量指标数据
INSERT INTO network_quality_metrics (node_id, latency, bandwidth, packet_loss, jitter, reliability_score, timestamp)
SELECT 
    node_id,
    ROUND((RANDOM() * 50 + 10)::NUMERIC, 3) as latency,
    ROUND((RANDOM() * 900 + 100)::NUMERIC, 2) as bandwidth,
    ROUND((RANDOM() * 0.05)::NUMERIC, 4) as packet_loss,
    ROUND((RANDOM() * 5 + 1)::NUMERIC, 3) as jitter,
    ROUND((RANDOM() * 0.2 + 0.8)::NUMERIC, 4) as reliability_score,
    CURRENT_TIMESTAMP - (INTERVAL '15 minutes' * generate_series(0, 95)) as timestamp
FROM scheduler_nodes 
WHERE status = 'active'
CROSS JOIN generate_series(0, 95);

-- 插入传输日志数据
INSERT INTO transmission_logs (transmission_id, source_node, destination_node, data_size, compressed_size, compression_ratio, reliability_level, success, actual_latency, timestamp)
VALUES
(uuid_generate_v4(), 'edge-node-001', 'edge-node-002', 1048576, 524288, 0.5, 'reliable', true, 45.2, CURRENT_TIMESTAMP - INTERVAL '1 hour'),
(uuid_generate_v4(), 'edge-node-002', 'edge-node-003', 2097152, 1258291, 0.6, 'high_reliable', true, 67.8, CURRENT_TIMESTAMP - INTERVAL '50 minutes'),
(uuid_generate_v4(), 'edge-node-003', 'edge-node-004', 524288, 314573, 0.6, 'best_effort', true, 23.1, CURRENT_TIMESTAMP - INTERVAL '40 minutes'),
(uuid_generate_v4(), 'edge-node-004', 'edge-node-005', 4194304, 2516582, 0.6, 'reliable', false, 0, CURRENT_TIMESTAMP - INTERVAL '30 minutes'),
(uuid_generate_v4(), 'edge-node-005', 'edge-node-006', 1572864, 943718, 0.6, 'high_reliable', true, 89.5, CURRENT_TIMESTAMP - INTERVAL '20 minutes'),
(uuid_generate_v4(), 'edge-node-006', 'edge-node-001', 3145728, 1887437, 0.6, 'best_effort', true, 34.7, CURRENT_TIMESTAMP - INTERVAL '10 minutes');

-- 插入系统监控指标数据
INSERT INTO system_metrics (metric_name, metric_value, metric_unit, tags, timestamp)
VALUES
('cpu_usage_percent', 65.5, 'percent', '{"service": "edge-enhancement", "instance": "main"}', CURRENT_TIMESTAMP - INTERVAL '5 minutes'),
('memory_usage_bytes', 2147483648, 'bytes', '{"service": "edge-enhancement", "instance": "main"}', CURRENT_TIMESTAMP - INTERVAL '5 minutes'),
('disk_usage_percent', 45.2, 'percent', '{"service": "edge-enhancement", "instance": "main", "mount": "/"}', CURRENT_TIMESTAMP - INTERVAL '5 minutes'),
('network_rx_bytes', 1048576000, 'bytes', '{"service": "edge-enhancement", "instance": "main", "interface": "eth0"}', CURRENT_TIMESTAMP - INTERVAL '5 minutes'),
('network_tx_bytes', 524288000, 'bytes', '{"service": "edge-enhancement", "instance": "main", "interface": "eth0"}', CURRENT_TIMESTAMP - INTERVAL '5 minutes'),
('http_requests_total', 15420, 'count', '{"service": "edge-enhancement", "method": "GET", "status": "200"}', CURRENT_TIMESTAMP - INTERVAL '5 minutes'),
('http_requests_total', 3250, 'count', '{"service": "edge-enhancement", "method": "POST", "status": "200"}', CURRENT_TIMESTAMP - INTERVAL '5 minutes'),
('http_requests_total', 125, 'count', '{"service": "edge-enhancement", "method": "GET", "status": "404"}', CURRENT_TIMESTAMP - INTERVAL '5 minutes'),
('http_request_duration_seconds', 0.125, 'seconds', '{"service": "edge-enhancement", "method": "GET", "quantile": "0.5"}', CURRENT_TIMESTAMP - INTERVAL '5 minutes'),
('http_request_duration_seconds', 0.250, 'seconds', '{"service": "edge-enhancement", "method": "GET", "quantile": "0.95"}', CURRENT_TIMESTAMP - INTERVAL '5 minutes'),
('cache_hit_rate', 0.85, 'ratio', '{"service": "edge-enhancement", "cache_level": "L1"}', CURRENT_TIMESTAMP - INTERVAL '5 minutes'),
('cache_hit_rate', 0.65, 'ratio', '{"service": "edge-enhancement", "cache_level": "L2"}', CURRENT_TIMESTAMP - INTERVAL '5 minutes'),
('cache_hit_rate', 0.45, 'ratio', '{"service": "edge-enhancement", "cache_level": "L3"}', CURRENT_TIMESTAMP - INTERVAL '5 minutes');

-- 更新节点当前负载（基于最新的历史数据）
UPDATE scheduler_nodes 
SET current_load = (
    SELECT COALESCE(AVG(load_value), 0)
    FROM scheduler_load_history 
    WHERE scheduler_load_history.node_id = scheduler_nodes.node_id 
    AND timestamp > CURRENT_TIMESTAMP - INTERVAL '1 hour'
)
WHERE status = 'active';

-- 创建一些有用的函数
CREATE OR REPLACE FUNCTION get_node_load_trend(p_node_id VARCHAR, p_hours INTEGER DEFAULT 24)
RETURNS TABLE(
    hour_offset INTEGER,
    avg_load DECIMAL(10,2),
    max_load DECIMAL(10,2),
    min_load DECIMAL(10,2)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        EXTRACT(HOUR FROM CURRENT_TIMESTAMP - h.timestamp)::INTEGER as hour_offset,
        ROUND(AVG(h.load_value), 2) as avg_load,
        ROUND(MAX(h.load_value), 2) as max_load,
        ROUND(MIN(h.load_value), 2) as min_load
    FROM scheduler_load_history h
    WHERE h.node_id = p_node_id 
    AND h.timestamp > CURRENT_TIMESTAMP - (p_hours || ' hours')::INTERVAL
    GROUP BY EXTRACT(HOUR FROM CURRENT_TIMESTAMP - h.timestamp)
    ORDER BY hour_offset;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_cache_performance_summary(p_hours INTEGER DEFAULT 24)
RETURNS TABLE(
    cache_level VARCHAR(10),
    total_requests BIGINT,
    hit_rate DECIMAL(5,4),
    avg_response_time DECIMAL(10,3)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cs.cache_level,
        SUM(cs.total_requests) as total_requests,
        CASE 
            WHEN SUM(cs.total_requests) > 0 
            THEN ROUND(SUM(cs.hit_count)::DECIMAL / SUM(cs.total_requests)::DECIMAL, 4)
            ELSE 0.0 
        END as hit_rate,
        ROUND(AVG(cs.avg_response_time), 3) as avg_response_time
    FROM cache_statistics cs
    WHERE cs.timestamp > CURRENT_TIMESTAMP - (p_hours || ' hours')::INTERVAL
    GROUP BY cs.cache_level
    ORDER BY cs.cache_level;
END;
$$ LANGUAGE plpgsql;

-- 完成种子数据插入
SELECT 'Seed data insertion completed successfully' as status;

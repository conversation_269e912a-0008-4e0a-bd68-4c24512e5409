#!/bin/bash

# Docker构建脚本
set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 配置
IMAGE_NAME="ecosystem-service"
VERSION=${1:-latest}
REGISTRY=${DOCKER_REGISTRY:-""}

echo -e "${GREEN}开始构建 Docker 镜像...${NC}"

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}错误: Docker 未运行${NC}"
    exit 1
fi

# 构建镜像
echo -e "${YELLOW}构建镜像: ${IMAGE_NAME}:${VERSION}${NC}"
docker build -t ${IMAGE_NAME}:${VERSION} .

# 如果指定了注册表，则推送镜像
if [ ! -z "$REGISTRY" ]; then
    echo -e "${YELLOW}标记镜像用于推送到注册表...${NC}"
    docker tag ${IMAGE_NAME}:${VERSION} ${REGISTRY}/${IMAGE_NAME}:${VERSION}
    
    echo -e "${YELLOW}推送镜像到注册表...${NC}"
    docker push ${REGISTRY}/${IMAGE_NAME}:${VERSION}
    
    # 如果版本不是latest，也推送latest标签
    if [ "$VERSION" != "latest" ]; then
        docker tag ${IMAGE_NAME}:${VERSION} ${REGISTRY}/${IMAGE_NAME}:latest
        docker push ${REGISTRY}/${IMAGE_NAME}:latest
    fi
fi

echo -e "${GREEN}Docker 镜像构建完成!${NC}"

# 显示镜像信息
echo -e "${YELLOW}镜像信息:${NC}"
docker images ${IMAGE_NAME}:${VERSION}

# 运行安全扫描（如果安装了trivy）
if command -v trivy &> /dev/null; then
    echo -e "${YELLOW}运行安全扫描...${NC}"
    trivy image ${IMAGE_NAME}:${VERSION}
fi

echo -e "${GREEN}构建完成!${NC}"

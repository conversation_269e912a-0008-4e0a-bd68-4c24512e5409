/**
 * Jest 测试配置文件
 * 群体协调服务测试配置
 */

module.exports = {
  // 测试环境
  testEnvironment: 'node',

  // 模块文件扩展名
  moduleFileExtensions: ['js', 'json', 'ts'],

  // 根目录
  rootDir: 'src',

  // 测试文件匹配模式
  testRegex: '.*\\.spec\\.ts$',

  // 转换配置
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },

  // 覆盖率收集配置
  collectCoverageFrom: [
    '**/*.(t|j)s',
    '!**/*.spec.ts',
    '!**/*.interface.ts',
    '!**/*.dto.ts',
    '!**/main.ts',
  ],

  // 覆盖率输出目录
  coverageDirectory: '../coverage',

  // 覆盖率报告格式
  coverageReporters: [
    'text',
    'text-summary',
    'html',
    'lcov',
    'clover'
  ],

  // 覆盖率阈值
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    }
  },

  // 模块路径映射
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/$1',
    '^@shared/(.*)$': '<rootDir>/../shared/$1'
  },

  // 设置文件
  setupFilesAfterEnv: ['<rootDir>/../test/setup.ts'],

  // 测试超时时间
  testTimeout: 30000,

  // 详细输出
  verbose: true,

  // 清除模拟
  clearMocks: true,

  // 恢复模拟
  restoreMocks: true,

  // 错误时退出
  bail: false,

  // 最大工作进程数
  maxWorkers: '50%',

  // 缓存目录
  cacheDirectory: '<rootDir>/../.jest-cache',

  // 忽略的路径
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/coverage/'
  ],

  // 转换忽略的路径
  transformIgnorePatterns: [
    '/node_modules/(?!(.*\\.mjs$))'
  ],

  // 全局变量
  globals: {
    'ts-jest': {
      tsconfig: 'tsconfig.json',
      isolatedModules: true
    }
  },

  // 预设
  preset: 'ts-jest',

  // 模块目录
  moduleDirectories: ['node_modules', '<rootDir>'],

  // 错误报告
  errorOnDeprecated: true,

  // 通知配置
  notify: false,
  notifyMode: 'failure-change',

  // 监听插件
  watchPlugins: [
    'jest-watch-typeahead/filename',
    'jest-watch-typeahead/testname'
  ]
};

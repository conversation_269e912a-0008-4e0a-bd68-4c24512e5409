import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import compression from 'compression';
import helmet from 'helmet';
import { AppModule } from './app.module';

/**
 * 边缘计算增强服务启动入口
 */
async function bootstrap() {
  const logger = new Logger('EdgeEnhancementService');
  
  try {
    // 创建NestJS应用实例
    const app = await NestFactory.create(AppModule, {
      logger: ['log', 'error', 'warn', 'debug', 'verbose'],
    });

    // 获取配置服务
    const configService = app.get(ConfigService);
    const port = configService.get<number>('PORT', 3040);
    const nodeEnv = configService.get<string>('NODE_ENV', 'development');

    // 启用CORS
    app.enableCors({
      origin: true,
      methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
      credentials: true,
    });

    // 安全中间件
    app.use(helmet({
      contentSecurityPolicy: false,
      crossOriginEmbedderPolicy: false,
    }));

    // 压缩中间件
    app.use(compression());

    // 全局验证管道
    app.useGlobalPipes(
      new ValidationPipe({
        transform: true,
        whitelist: true,
        forbidNonWhitelisted: true,
        disableErrorMessages: nodeEnv === 'production',
      }),
    );

    // 设置全局前缀
    app.setGlobalPrefix('api/v1');

    // Swagger API文档配置
    if (nodeEnv !== 'production') {
      const config = new DocumentBuilder()
        .setTitle('边缘计算增强服务 API')
        .setDescription(`
          边缘计算增强服务提供以下核心功能：
          
          ## 🧠 智能调度服务
          - 基于机器学习的负载预测
          - 智能流量分配算法
          - 自适应资源调度
          
          ## 🚀 预测性缓存服务
          - 多层缓存架构 (L1/L2/L3)
          - 用户行为分析与预测
          - 智能预加载机制
          
          ## 🌐 自适应网络传输
          - 动态数据压缩
          - 前向纠错编码 (FEC)
          - 可靠性等级控制
          
          ## 📊 监控与统计
          - 实时性能监控
          - 详细统计报告
          - 健康状态检查
        `)
        .setVersion('1.0.0')
        .addTag('边缘计算增强', '边缘计算增强服务核心API')
        .addTag('智能调度', '智能调度相关接口')
        .addTag('预测性缓存', '预测性缓存相关接口')
        .addTag('自适应网络', '自适应网络传输相关接口')
        .addTag('监控统计', '监控与统计相关接口')
        .addBearerAuth()
        .addServer(`http://localhost:${port}`, '本地开发环境')
        .addServer('http://edge-enhancement:3040', 'Docker容器环境')
        .build();

      const document = SwaggerModule.createDocument(app, config);
      SwaggerModule.setup('api/docs', app, document, {
        swaggerOptions: {
          persistAuthorization: true,
          displayRequestDuration: true,
          docExpansion: 'none',
          filter: true,
          showRequestHeaders: true,
          tryItOutEnabled: true,
        },
        customSiteTitle: '边缘计算增强服务 API 文档',
        customfavIcon: '/favicon.ico',
        customJs: [
          'https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/4.15.5/swagger-ui-bundle.min.js',
          'https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/4.15.5/swagger-ui-standalone-preset.min.js',
        ],
        customCssUrl: [
          'https://cdnjs.cloudflare.com/ajax/libs/swagger-ui/4.15.5/swagger-ui.min.css',
        ],
      });

      logger.log(`📚 Swagger API文档已启用: http://localhost:${port}/api/docs`);
    }

    // 启动应用
    await app.listen(port, '0.0.0.0');

    logger.log(`🚀 边缘计算增强服务已启动`);
    logger.log(`🌐 服务地址: http://localhost:${port}`);
    logger.log(`📊 健康检查: http://localhost:${port}/api/v1/edge-enhancement/health`);
    logger.log(`📈 服务状态: http://localhost:${port}/api/v1/edge-enhancement/status`);
    logger.log(`🔧 环境模式: ${nodeEnv}`);
    
    if (nodeEnv !== 'production') {
      logger.log(`📖 API文档: http://localhost:${port}/api/docs`);
    }

    // 优雅关闭处理
    process.on('SIGTERM', async () => {
      logger.log('🛑 收到SIGTERM信号，开始优雅关闭...');
      await app.close();
      logger.log('✅ 应用已优雅关闭');
      process.exit(0);
    });

    process.on('SIGINT', async () => {
      logger.log('🛑 收到SIGINT信号，开始优雅关闭...');
      await app.close();
      logger.log('✅ 应用已优雅关闭');
      process.exit(0);
    });

  } catch (error) {
    logger.error('❌ 应用启动失败:', error);
    process.exit(1);
  }
}

// 启动应用
bootstrap().catch((error) => {
  console.error('💥 启动过程中发生致命错误:', error);
  process.exit(1);
});

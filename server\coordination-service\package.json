{"name": "coordination-service", "version": "1.0.0", "description": "群体协调服务 - 提供大规模群体行为的协调和管理功能", "main": "dist/main.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "lint:check": "eslint \"{src,apps,libs,test}/**/*.ts\"", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:e2e:watch": "jest --config ./test/jest-e2e.json --watch", "test:e2e:cov": "jest --config ./test/jest-e2e.json --coverage", "dev": "chmod +x scripts/dev.sh && ./scripts/dev.sh", "deploy": "chmod +x scripts/deploy.sh && ./scripts/deploy.sh", "test:all": "chmod +x scripts/test.sh && ./scripts/test.sh all", "docker:build": "docker build -t coordination-service .", "docker:run": "docker run -p 3010:3010 coordination-service", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f coordination-service", "clean": "rm -rf dist coverage .jest-cache", "precommit": "npm run lint:check && npm run format:check && npm run test", "prepare": "npm run build"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^2.0.0", "@nestjs/microservices": "^10.4.19", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^4.0.0", "axios": "^1.5.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "ioredis": "^5.3.2", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "uuid": "^9.0.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.5.0", "jest-watch-typeahead": "^2.2.2", "prettier": "^2.8.8", "source-map-support": "^0.5.21", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}
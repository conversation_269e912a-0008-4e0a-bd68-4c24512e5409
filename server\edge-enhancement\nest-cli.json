{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "src", "compilerOptions": {"deleteOutDir": true, "webpack": true, "tsConfigPath": "tsconfig.json"}, "projects": {"edge-enhancement-service": {"type": "application", "root": "", "entryFile": "main", "sourceRoot": "src", "compilerOptions": {"tsConfigPath": "tsconfig.json"}}}, "monorepo": false, "root": "", "entryFile": "main", "exec": "node"}
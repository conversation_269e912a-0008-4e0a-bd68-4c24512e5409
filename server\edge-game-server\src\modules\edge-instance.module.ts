import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';

// 服务
import { EdgeInstanceService } from '../services/edge-instance.service';
import { EdgeGameSessionService } from '../services/edge-game-session.service';
import { EdgeUserService } from '../services/edge-user.service';

// 控制器
import { EdgeInstanceController } from '../controllers/edge-instance.controller';

/**
 * 边缘实例模块
 * 管理边缘节点上的游戏实例和用户会话
 */
@Module({
  imports: [
    ConfigModule,
    EventEmitterModule,
  ],
  
  controllers: [
    EdgeInstanceController,
  ],
  
  providers: [
    EdgeInstanceService,
    EdgeGameSessionService,
    EdgeUserService,
  ],
  
  exports: [
    EdgeInstanceService,
    EdgeGameSessionService,
    EdgeUserService,
  ],
})
export class EdgeInstanceModule {}

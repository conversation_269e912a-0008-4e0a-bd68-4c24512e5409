import { NestFactory } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { ValidationPipe, Logger } from '@nestjs/common';
import { Transport, MicroserviceOptions } from '@nestjs/microservices';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import compression from 'compression';
import helmet from 'helmet';

import { AppModule } from './app.module';

/**
 * 边缘路由服务启动函数
 */
async function bootstrap() {
  const logger = new Logger('EdgeRouter');
  
  // 创建NestJS应用实例
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  // 获取配置
  const port = configService.get<number>('PORT', 3012);
  const apiPrefix = configService.get<string>('API_PREFIX', 'api');
  const microservicePort = configService.get<number>('MICROSERVICE_PORT', 3012);
  const microserviceHost = configService.get<string>('MICROSERVICE_HOST', 'localhost');

  // 配置微服务
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.TCP,
    options: {
      host: microserviceHost,
      port: microservicePort,
    },
  });

  // 全局前缀
  app.setGlobalPrefix(apiPrefix);

  // 全局管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // 启用CORS
  app.enableCors({
    origin: configService.get<string>('CORS_ORIGIN', '*'),
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    credentials: true,
  });

  // 启用压缩
  app.use(compression());

  // 启用安全头
  app.use(helmet({
    contentSecurityPolicy: false,
    crossOriginEmbedderPolicy: false,
  }));

  // Swagger API文档
  const swaggerTitle = configService.get<string>('SWAGGER_TITLE', '边缘路由服务API');
  const swaggerDescription = configService.get<string>('SWAGGER_DESCRIPTION', 'DL引擎边缘路由服务API文档');
  const swaggerVersion = configService.get<string>('SWAGGER_VERSION', '1.0.0');
  const swaggerPath = configService.get<string>('SWAGGER_PATH', 'api/docs');

  const config = new DocumentBuilder()
    .setTitle(swaggerTitle)
    .setDescription(swaggerDescription)
    .setVersion(swaggerVersion)
    .addTag('edge-router', '边缘路由')
    .addTag('routing', '路由决策')
    .addTag('health', '健康检查')
    .addTag('monitoring', '监控统计')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup(swaggerPath, app, document, {
    swaggerOptions: {
      persistAuthorization: true,
      displayRequestDuration: true,
    },
  });

  // 启动微服务
  await app.startAllMicroservices();
  logger.log(`🚀 微服务已启动 - TCP://${microserviceHost}:${microservicePort}`);

  // 启动HTTP服务
  await app.listen(port);
  logger.log(`🌐 HTTP服务已启动 - http://localhost:${port}/${apiPrefix}`);
  logger.log(`📖 API文档地址 - http://localhost:${port}/${swaggerPath}`);
  logger.log(`🧭 边缘路由服务已就绪`);
}

// 启动应用
bootstrap().catch((error) => {
  console.error('启动边缘路由服务失败:', error);
  process.exit(1);
});

/**
 * 群体协调服务接口定义
 */

/**
 * 基础服务接口
 */
export interface ICoordinationService {
  /**
   * 创建协调任务
   */
  createCoordinationTask(
    type: string,
    entityIds: string[],
    parameters: any,
    priority?: number,
    deadline?: number
  ): Promise<string>;

  /**
   * 获取任务状态
   */
  getTaskStatus(taskId: string): any;

  /**
   * 获取活跃冲突
   */
  getActiveConflicts(): any[];

  /**
   * 获取可用资源
   */
  getAvailableResources(): any[];
}

/**
 * 协调策略接口
 */
export interface ICoordinationStrategy {
  /**
   * 策略名称
   */
  name: string;

  /**
   * 策略描述
   */
  description: string;

  /**
   * 适用的任务类型
   */
  applicableTaskTypes: string[];

  /**
   * 执行策略
   */
  execute(task: any, context: any): Promise<any>;
}

/**
 * 群体管理接口
 */
export interface IGroupManager {
  /**
   * 创建群体
   */
  createGroup(memberIds: string[], type: string): Promise<string>;

  /**
   * 解散群体
   */
  dissolveGroup(groupId: string): Promise<void>;

  /**
   * 添加成员
   */
  addMember(groupId: string, entityId: string): Promise<void>;

  /**
   * 移除成员
   */
  removeMember(groupId: string, entityId: string): Promise<void>;

  /**
   * 获取群体信息
   */
  getGroupInfo(groupId: string): Promise<any>;
}

/**
 * 角色管理接口
 */
export interface IRoleManager {
  /**
   * 分配角色
   */
  assignRole(entityId: string, role: string): Promise<void>;

  /**
   * 撤销角色
   */
  revokeRole(entityId: string): Promise<void>;

  /**
   * 获取实体角色
   */
  getEntityRole(entityId: string): Promise<string | null>;

  /**
   * 获取角色持有者
   */
  getRoleHolders(role: string): Promise<string[]>;
}

/**
 * 冲突管理接口
 */
export interface IConflictManager {
  /**
   * 报告冲突
   */
  reportConflict(conflict: any): Promise<string>;

  /**
   * 解决冲突
   */
  resolveConflict(conflictId: string, resolution: any): Promise<void>;

  /**
   * 获取冲突详情
   */
  getConflictDetails(conflictId: string): Promise<any>;

  /**
   * 获取实体相关冲突
   */
  getEntityConflicts(entityId: string): Promise<any[]>;
}

/**
 * 资源管理接口
 */
export interface IResourceManager {
  /**
   * 分配资源
   */
  allocateResource(entityId: string, resourceType: string, amount: number): Promise<void>;

  /**
   * 释放资源
   */
  releaseResource(entityId: string, resourceType: string, amount: number): Promise<void>;

  /**
   * 获取资源使用情况
   */
  getResourceUsage(resourceType: string): Promise<any>;

  /**
   * 获取实体资源
   */
  getEntityResources(entityId: string): Promise<any[]>;
}

/**
 * 事件处理接口
 */
export interface IEventHandler {
  /**
   * 处理协调请求
   */
  handleCoordinationRequest(data: any): Promise<void>;

  /**
   * 处理冲突报告
   */
  handleConflictReport(data: any): Promise<void>;

  /**
   * 处理资源更新
   */
  handleResourceUpdate(data: any): Promise<void>;
}

/**
 * 监控接口
 */
export interface ICoordinationMonitor {
  /**
   * 获取服务状态
   */
  getServiceStatus(): Promise<any>;

  /**
   * 获取性能指标
   */
  getPerformanceMetrics(): Promise<any>;

  /**
   * 获取任务统计
   */
  getTaskStatistics(): Promise<any>;

  /**
   * 获取错误日志
   */
  getErrorLogs(limit?: number): Promise<any[]>;
}

/**
 * 配置接口
 */
export interface ICoordinationConfig {
  /**
   * Redis配置
   */
  redis: {
    host: string;
    port: number;
    password?: string;
    db: number;
  };

  /**
   * 服务配置
   */
  service: {
    name: string;
    version: string;
    port: number;
    environment: string;
  };

  /**
   * 协调配置
   */
  coordination: {
    maxConcurrentTasks: number;
    taskTimeout: number;
    retryAttempts: number;
    cleanupInterval: number;
  };
}

/**
 * 健康检查接口
 */
export interface IHealthCheck {
  /**
   * 检查服务健康状态
   */
  checkHealth(): Promise<{
    status: 'healthy' | 'unhealthy';
    timestamp: Date;
    details: {
      redis: boolean;
      database: boolean;
      memory: number;
      cpu: number;
    };
  }>;
}

/**
 * 日志接口
 */
export interface ICoordinationLogger {
  /**
   * 记录信息日志
   */
  info(message: string, context?: any): void;

  /**
   * 记录警告日志
   */
  warn(message: string, context?: any): void;

  /**
   * 记录错误日志
   */
  error(message: string, error?: Error, context?: any): void;

  /**
   * 记录调试日志
   */
  debug(message: string, context?: any): void;
}

/**
 * 缓存接口
 */
export interface ICoordinationCache {
  /**
   * 设置缓存
   */
  set(key: string, value: any, ttl?: number): Promise<void>;

  /**
   * 获取缓存
   */
  get(key: string): Promise<any>;

  /**
   * 删除缓存
   */
  delete(key: string): Promise<void>;

  /**
   * 清空缓存
   */
  clear(): Promise<void>;
}

/**
 * 消息队列接口
 */
export interface IMessageQueue {
  /**
   * 发布消息
   */
  publish(channel: string, message: any): Promise<void>;

  /**
   * 订阅消息
   */
  subscribe(channel: string, handler: (message: any) => void): Promise<void>;

  /**
   * 取消订阅
   */
  unsubscribe(channel: string): Promise<void>;
}

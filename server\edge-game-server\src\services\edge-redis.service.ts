import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis from 'ioredis';

/**
 * 边缘Redis服务
 * 提供Redis连接和基础操作
 */
@Injectable()
export class EdgeRedisService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(EdgeRedisService.name);
  private redis: Redis | null = null;

  constructor(private readonly configService: ConfigService) {}

  async onModuleInit() {
    await this.connect();
  }

  async onModuleDestroy() {
    await this.disconnect();
  }

  /**
   * 连接Redis
   */
  private async connect(): Promise<void> {
    try {
      const config = {
        host: this.configService.get<string>('REDIS_HOST', 'localhost'),
        port: this.configService.get<number>('REDIS_PORT', 6379),
        password: this.configService.get<string>('REDIS_PASSWORD'),
        db: this.configService.get<number>('REDIS_DB', 0),
        retryDelayOnFailover: 100,
        retryDelayOnClusterDown: 300,
        maxRetriesPerRequest: 3,
        lazyConnect: true,
      };

      this.redis = new Redis(config);
      
      this.redis.on('connect', () => {
        this.logger.log('Redis连接已建立');
      });

      this.redis.on('error', (error) => {
        this.logger.error(`Redis连接错误: ${error.message}`);
      });

      await this.redis.ping();
    } catch (error) {
      this.logger.warn(`Redis连接失败: ${error.message}`);
    }
  }

  /**
   * 断开Redis连接
   */
  private async disconnect(): Promise<void> {
    if (this.redis) {
      await this.redis.quit();
      this.redis = null;
    }
  }

  /**
   * 获取Redis客户端
   */
  getClient(): Redis | null {
    return this.redis;
  }

  /**
   * 检查连接状态
   */
  isConnected(): boolean {
    return this.redis?.status === 'ready';
  }
}

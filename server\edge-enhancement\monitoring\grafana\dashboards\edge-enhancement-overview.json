{"dashboard": {"id": null, "title": "边缘计算增强服务概览", "tags": ["edge-enhancement", "overview"], "style": "dark", "timezone": "browser", "refresh": "5s", "time": {"from": "now-1h", "to": "now"}, "panels": [{"id": 1, "title": "服务状态", "type": "stat", "targets": [{"expr": "up{job=\"edge-enhancement-service\"}", "legendFormat": "服务状态"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "green", "value": 1}]}, "mappings": [{"options": {"0": {"text": "离线"}}, "type": "value"}, {"options": {"1": {"text": "在线"}}, "type": "value"}]}}, "gridPos": {"h": 4, "w": 6, "x": 0, "y": 0}}, {"id": 2, "title": "请求速率", "type": "graph", "targets": [{"expr": "rate(http_requests_total{job=\"edge-enhancement-service\"}[5m])", "legendFormat": "{{method}} {{status}}"}], "gridPos": {"h": 8, "w": 12, "x": 6, "y": 0}}, {"id": 3, "title": "响应时间", "type": "graph", "targets": [{"expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket{job=\"edge-enhancement-service\"}[5m]))", "legendFormat": "95th percentile"}, {"expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket{job=\"edge-enhancement-service\"}[5m]))", "legendFormat": "50th percentile"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "缓存命中率", "type": "stat", "targets": [{"expr": "cache_hit_rate{job=\"edge-enhancement-service\"}", "legendFormat": "命中率"}], "fieldConfig": {"defaults": {"unit": "percent", "color": {"mode": "thresholds"}, "thresholds": {"steps": [{"color": "red", "value": 0}, {"color": "yellow", "value": 50}, {"color": "green", "value": 80}]}}}, "gridPos": {"h": 4, "w": 6, "x": 12, "y": 8}}, {"id": 5, "title": "内存使用率", "type": "graph", "targets": [{"expr": "process_resident_memory_bytes{job=\"edge-enhancement-service\"}", "legendFormat": "内存使用"}], "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 6, "title": "CPU使用率", "type": "graph", "targets": [{"expr": "rate(process_cpu_seconds_total{job=\"edge-enhancement-service\"}[5m]) * 100", "legendFormat": "CPU使用率"}], "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}], "templating": {"list": []}, "annotations": {"list": []}, "schemaVersion": 30, "version": 1}}
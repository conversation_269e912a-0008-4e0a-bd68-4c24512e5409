import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallH<PERSON>ler,
  Logger,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Observable, of } from 'rxjs';
import { tap } from 'rxjs/operators';
import { CacheService } from '../redis/cache.service';
import { CACHE_KEY, CACHE_TTL } from '../decorators/cache.decorator';

@Injectable()
export class CacheInterceptor implements NestInterceptor {
  private readonly logger = new Logger(CacheInterceptor.name);

  constructor(
    private readonly cacheService: CacheService,
    private readonly reflector: Reflector,
  ) {}

  async intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Promise<Observable<any>> {
    const cacheKey = this.reflector.get<string>(CACHE_KEY, context.getHandler());
    const cacheTTL = this.reflector.get<number>(CACHE_TTL, context.getHandler());

    if (!cacheKey) {
      return next.handle();
    }

    // 构建实际的缓存键
    const request = context.switchToHttp().getRequest();
    const actualKey = this.buildCacheKey(cacheKey, request);

    try {
      // 尝试从缓存获取数据
      const cachedData = await this.cacheService.get(actualKey);
      if (cachedData !== null) {
        this.logger.debug(`Cache hit for key: ${actualKey}`);
        return of(cachedData);
      }

      this.logger.debug(`Cache miss for key: ${actualKey}`);

      // 缓存未命中，执行原方法并缓存结果
      return next.handle().pipe(
        tap(async (data) => {
          if (data !== null && data !== undefined) {
            await this.cacheService.set(actualKey, data, cacheTTL);
            this.logger.debug(`Data cached for key: ${actualKey}`);
          }
        }),
      );
    } catch (error) {
      this.logger.error(`Cache error for key ${actualKey}:`, error);
      // 缓存出错时直接执行原方法
      return next.handle();
    }
  }

  private buildCacheKey(template: string, request: any): string {
    let key = template;
    
    // 替换路径参数 {param}
    const params = request.params || {};
    Object.keys(params).forEach(param => {
      key = key.replace(new RegExp(`{${param}}`, 'g'), params[param]);
    });

    // 替换查询参数 {query.param}
    const query = request.query || {};
    Object.keys(query).forEach(param => {
      key = key.replace(new RegExp(`{query.${param}}`, 'g'), query[param]);
    });

    // 替换用户ID {userId}
    if (request.user && request.user.id) {
      key = key.replace(/{userId}/g, request.user.id);
    }

    return key;
  }
}

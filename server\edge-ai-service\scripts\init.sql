-- 边缘AI服务数据库初始化脚本

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `edge_ai_service` 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE `edge_ai_service`;

-- 创建边缘设备表
CREATE TABLE IF NOT EXISTS `edge_devices` (
  `id` varchar(36) NOT NULL,
  `deviceId` varchar(255) NOT NULL UNIQUE,
  `name` varchar(255) NOT NULL,
  `type` enum('industrial_pc','embedded_controller','smart_sensor','gateway','mobile_device','fpga','gpu_accelerator','tpu') DEFAULT 'industrial_pc',
  `location` varchar(255) NOT NULL,
  `status` enum('online','offline','maintenance') DEFAULT 'offline',
  `capabilities` json NOT NULL,
  `performance` json NOT NULL,
  `networkInfo` json NOT NULL,
  `deployedModels` json DEFAULT NULL,
  `lastHeartbeat` timestamp NULL DEFAULT NULL,
  `description` text,
  `metadata` json DEFAULT NULL,
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_type` (`type`),
  KEY `idx_location` (`location`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建AI模型表
CREATE TABLE IF NOT EXISTS `ai_models` (
  `id` varchar(36) NOT NULL,
  `modelId` varchar(255) NOT NULL UNIQUE,
  `name` varchar(255) NOT NULL,
  `version` varchar(50) NOT NULL,
  `type` enum('classification','regression','detection','segmentation','nlp','recommendation','anomaly_detection','time_series') DEFAULT 'classification',
  `format` enum('tflite','onnx','tensorrt','openvino','coreml','tfjs','pytorch_mobile') DEFAULT 'tfjs',
  `status` enum('active','inactive','deprecated','training') DEFAULT 'active',
  `description` text,
  `size` decimal(10,2) NOT NULL,
  `inputShape` json NOT NULL,
  `outputShape` json NOT NULL,
  `labels` json DEFAULT NULL,
  `performance` json NOT NULL,
  `requirements` json NOT NULL,
  `preprocessing` json DEFAULT NULL,
  `postprocessing` json DEFAULT NULL,
  `modelPath` varchar(500) DEFAULT NULL,
  `configPath` varchar(500) DEFAULT NULL,
  `weightsPath` varchar(500) DEFAULT NULL,
  `trainingConfig` json DEFAULT NULL,
  `deploymentInfo` json DEFAULT NULL,
  `metadata` json DEFAULT NULL,
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_type` (`type`),
  KEY `idx_format` (`format`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建推理请求表
CREATE TABLE IF NOT EXISTS `inference_requests` (
  `id` varchar(36) NOT NULL,
  `requestId` varchar(255) NOT NULL UNIQUE,
  `modelId` varchar(255) NOT NULL,
  `deviceId` varchar(255) DEFAULT NULL,
  `inputData` json NOT NULL,
  `priority` enum('low','medium','high','critical') DEFAULT 'medium',
  `status` enum('pending','processing','completed','failed','timeout') DEFAULT 'pending',
  `timeout` int DEFAULT 5000,
  `callback` varchar(500) DEFAULT NULL,
  `metadata` json DEFAULT NULL,
  `preferredDeviceId` varchar(255) DEFAULT NULL,
  `includeDetails` tinyint(1) DEFAULT 0,
  `startedAt` timestamp NULL DEFAULT NULL,
  `completedAt` timestamp NULL DEFAULT NULL,
  `processingTime` int DEFAULT NULL,
  `errorMessage` text,
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`),
  KEY `idx_modelId` (`modelId`),
  KEY `idx_deviceId` (`deviceId`),
  KEY `idx_createdAt` (`createdAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建推理结果表
CREATE TABLE IF NOT EXISTS `inference_results` (
  `id` varchar(36) NOT NULL,
  `requestId` varchar(255) NOT NULL,
  `modelId` varchar(255) NOT NULL,
  `deviceId` varchar(255) NOT NULL,
  `result` json NOT NULL,
  `confidence` decimal(5,4) DEFAULT NULL,
  `processingTime` int NOT NULL,
  `metadata` json DEFAULT NULL,
  `performance` json DEFAULT NULL,
  `details` json DEFAULT NULL,
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_requestId` (`requestId`),
  KEY `idx_modelId` (`modelId`),
  KEY `idx_deviceId` (`deviceId`),
  KEY `idx_createdAt` (`createdAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建学习任务表
CREATE TABLE IF NOT EXISTS `learning_tasks` (
  `id` varchar(36) NOT NULL,
  `taskId` varchar(255) NOT NULL UNIQUE,
  `name` varchar(255) NOT NULL,
  `algorithm` enum('federated_learning','distributed_training','ensemble_learning') DEFAULT 'federated_learning',
  `status` enum('pending','running','completed','failed','cancelled') DEFAULT 'pending',
  `participants` json NOT NULL,
  `modelTemplate` json NOT NULL,
  `aggregationStrategy` varchar(100) DEFAULT 'federated_averaging',
  `rounds` int DEFAULT 10,
  `currentRound` int DEFAULT 0,
  `learningRate` decimal(10,6) DEFAULT NULL,
  `batchSize` int DEFAULT NULL,
  `localEpochs` int DEFAULT NULL,
  `minParticipants` int DEFAULT NULL,
  `convergenceThreshold` decimal(10,6) DEFAULT NULL,
  `privacyConfig` json DEFAULT NULL,
  `dataDistribution` json DEFAULT NULL,
  `results` json DEFAULT NULL,
  `progress` json DEFAULT NULL,
  `startTime` timestamp NULL DEFAULT NULL,
  `endTime` timestamp NULL DEFAULT NULL,
  `errorMessage` text,
  `metadata` json DEFAULT NULL,
  `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_algorithm` (`algorithm`),
  KEY `idx_createdAt` (`createdAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建设备性能监控表
CREATE TABLE IF NOT EXISTS `device_performance` (
  `id` varchar(36) NOT NULL,
  `deviceId` varchar(255) NOT NULL,
  `cpuUsage` decimal(5,2) NOT NULL,
  `memoryUsage` decimal(5,2) NOT NULL,
  `storageUsage` decimal(5,2) NOT NULL,
  `gpuUsage` decimal(5,2) DEFAULT NULL,
  `temperature` decimal(5,2) NOT NULL,
  `powerConsumption` decimal(8,2) NOT NULL,
  `networkLatency` decimal(8,2) NOT NULL,
  `uptime` decimal(10,2) NOT NULL,
  `activeInferences` int DEFAULT NULL,
  `queuedInferences` int DEFAULT NULL,
  `throughput` decimal(8,2) DEFAULT NULL,
  `averageInferenceTime` decimal(8,2) DEFAULT NULL,
  `errorRate` decimal(5,4) DEFAULT NULL,
  `modelPerformance` json DEFAULT NULL,
  `networkInfo` json DEFAULT NULL,
  `alerts` json DEFAULT NULL,
  `timestamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_deviceId` (`deviceId`),
  KEY `idx_timestamp` (`timestamp`),
  KEY `idx_deviceId_timestamp` (`deviceId`, `timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入示例数据

-- 插入示例AI模型
INSERT INTO `ai_models` (`id`, `modelId`, `name`, `version`, `type`, `format`, `size`, `inputShape`, `outputShape`, `performance`, `requirements`) VALUES
('model-001', 'anomaly_detection_v1', '异常检测模型', '1.0.0', 'anomaly_detection', 'tfjs', 50.5, '[224, 224, 3]', '[1]', '{"accuracy": 0.95, "inferenceTime": 25}', '{"minMemory": 512, "minCpuCores": 2}'),
('model-002', 'object_detection_v1', '目标检测模型', '1.0.0', 'detection', 'tflite', 120.8, '[416, 416, 3]', '[10647, 85]', '{"accuracy": 0.88, "inferenceTime": 45}', '{"minMemory": 1024, "accelerator": "gpu"}'),
('model-003', 'classification_v1', '图像分类模型', '1.0.0', 'classification', 'onnx', 75.2, '[224, 224, 3]', '[1000]', '{"accuracy": 0.92, "inferenceTime": 15}', '{"minMemory": 256, "minCpuCores": 1}');

-- 插入示例边缘设备
INSERT INTO `edge_devices` (`id`, `deviceId`, `name`, `type`, `location`, `status`, `capabilities`, `performance`, `networkInfo`) VALUES
('device-001', 'edge_001', '工业控制器-001', 'industrial_pc', '生产车间A', 'online', 
'{"cpu": {"cores": 4, "frequency": 2400, "architecture": "x86_64"}, "memory": {"total": 8192, "available": 6144}, "storage": {"total": 256, "available": 200}, "connectivity": ["ethernet", "wifi"]}',
'{"cpuUsage": 25.5, "memoryUsage": 35.2, "storageUsage": 22.1, "temperature": 45.8, "powerConsumption": 85.5, "networkLatency": 12.3, "uptime": 168.5, "lastUpdated": "2024-01-01 10:00:00"}',
'{"ipAddress": "*************", "bandwidth": 1000, "latency": 12, "reliability": 99.5, "protocol": "TCP/IP", "encryption": true}'),

('device-002', 'edge_002', '智能传感器-002', 'smart_sensor', '仓库B区', 'online',
'{"cpu": {"cores": 2, "frequency": 1800, "architecture": "arm64"}, "memory": {"total": 4096, "available": 3072}, "storage": {"total": 128, "available": 100}, "sensors": ["temperature", "humidity", "motion"], "connectivity": ["wifi", "bluetooth"]}',
'{"cpuUsage": 15.2, "memoryUsage": 28.7, "storageUsage": 18.9, "temperature": 38.2, "powerConsumption": 25.8, "networkLatency": 18.5, "uptime": 72.3, "lastUpdated": "2024-01-01 10:00:00"}',
'{"ipAddress": "*************", "bandwidth": 100, "latency": 18, "reliability": 98.8, "protocol": "TCP/IP", "encryption": true}');

SET FOREIGN_KEY_CHECKS = 1;

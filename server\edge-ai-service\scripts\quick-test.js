const http = require('http');

// 快速测试 API 端点
async function quickTest() {
  console.log('🧪 快速测试边缘AI服务API...');
  
  const baseUrl = 'http://localhost:3002';
  const endpoints = [
    { path: '/api/v1/edge/statistics', method: 'GET', name: '边缘统计' },
    { path: '/api/v1/edge/devices', method: 'GET', name: '设备列表' },
    { path: '/api/v1/devices', method: 'GET', name: '设备管理' },
    { path: '/api/v1/devices/statistics', method: 'GET', name: '设备统计' },
    { path: '/api/v1/models', method: 'GET', name: '模型列表' },
    { path: '/api/v1/inference/statistics', method: 'GET', name: '推理统计' },
    { path: '/api/v1/learning', method: 'GET', name: '学习任务' },
    { path: '/api/v1/monitoring/metrics', method: 'GET', name: '监控指标' },
  ];

  let successCount = 0;
  let totalCount = endpoints.length;

  for (const endpoint of endpoints) {
    try {
      console.log(`\n📡 测试: ${endpoint.name} (${endpoint.method} ${endpoint.path})`);
      
      const result = await makeRequest(baseUrl + endpoint.path, endpoint.method);
      
      if (result.success) {
        console.log(`✅ ${endpoint.name}: 成功 (${result.statusCode})`);
        successCount++;
        if (result.data && typeof result.data === 'object') {
          console.log(`   响应: ${JSON.stringify(result.data).substring(0, 80)}...`);
        }
      } else {
        console.log(`❌ ${endpoint.name}: 失败 (${result.statusCode || 'N/A'})`);
        console.log(`   错误: ${result.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.log(`❌ ${endpoint.name}: 异常`);
      console.log(`   异常: ${error.message}`);
    }
  }

  console.log('\n📊 测试结果汇总');
  console.log('='.repeat(40));
  console.log(`✅ 成功: ${successCount}/${totalCount}`);
  console.log(`❌ 失败: ${totalCount - successCount}/${totalCount}`);
  console.log(`📈 成功率: ${((successCount / totalCount) * 100).toFixed(1)}%`);
  
  if (successCount === totalCount) {
    console.log('\n🎉 所有API测试通过！服务运行正常');
  } else if (successCount > 0) {
    console.log('\n⚠️  部分API测试通过，服务基本正常');
  } else {
    console.log('\n❌ 所有API测试失败，请检查服务状态');
  }
}

function makeRequest(url, method = 'GET') {
  return new Promise((resolve) => {
    const options = {
      method,
      timeout: 3000,
    };

    const req = http.request(url, options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsedData = data ? JSON.parse(data) : null;
          resolve({
            success: res.statusCode >= 200 && res.statusCode < 300,
            statusCode: res.statusCode,
            data: parsedData,
          });
        } catch (error) {
          resolve({
            success: res.statusCode >= 200 && res.statusCode < 300,
            statusCode: res.statusCode,
            data: data,
          });
        }
      });
    });

    req.on('error', (error) => {
      resolve({
        success: false,
        error: error.message,
      });
    });

    req.on('timeout', () => {
      req.destroy();
      resolve({
        success: false,
        error: 'Request timeout',
      });
    });

    req.end();
  });
}

// 检查服务是否运行
async function checkService() {
  console.log('🔍 检查服务状态...');
  
  try {
    const result = await makeRequest('http://localhost:3002/api/v1/edge/statistics');
    if (result.success) {
      console.log('✅ 服务正在运行在端口 3002');
      return true;
    } else {
      console.log(`❌ 服务响应异常 (状态码: ${result.statusCode})`);
      return false;
    }
  } catch (error) {
    console.log('❌ 无法连接到服务');
    return false;
  }
}

async function main() {
  console.log('🚀 边缘AI服务快速测试工具');
  console.log('================================');
  
  const isRunning = await checkService();
  
  if (isRunning) {
    await quickTest();
  } else {
    console.log('\n💡 请先启动服务:');
    console.log('   cd server/edge-ai-service');
    console.log('   $env:PORT=3002; node dist/main.js');
    console.log('\n或者尝试其他端口:');
    console.log('   $env:PORT=3003; node dist/main.js');
  }
}

main().catch(console.error);

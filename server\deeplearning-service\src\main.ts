/**
 * 深度学习推理服务主入口
 * 
 * 启动NestJS应用，配置中间件、CORS、Swagger文档等
 */

import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import cors from 'cors';
import * as express from 'express';
import * as path from 'path';
import { DeepLearningServiceModule } from './deeplearning-service.module';

/**
 * 启动应用
 */
async function bootstrap() {
  const logger = new Logger('Bootstrap');

  try {
    // 创建NestJS应用
    const app = await NestFactory.create(DeepLearningServiceModule, {
      logger: ['log', 'error', 'warn', 'debug', 'verbose'],
    });

    // 获取配置服务
    const configService = app.get(ConfigService);
    
    // 配置全局前缀
    app.setGlobalPrefix('api/v1');

    // 配置CORS
    app.use(cors({
      origin: configService.get('CORS_ORIGIN', '*'),
      methods: configService.get('CORS_METHODS', 'GET,POST,PUT,DELETE,PATCH'),
      allowedHeaders: configService.get('CORS_HEADERS', 'Content-Type,Authorization,X-Request-ID'),
      credentials: configService.get('CORS_CREDENTIALS', 'true') === 'true',
    }));

    // 提供静态文件
    app.use('/public', express.static(path.join(__dirname, '..', 'public')));

    // 配置全局验证管道
    app.useGlobalPipes(new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      disableErrorMessages: false,
      validationError: {
        target: false,
        value: false,
      },
    }));

    // 配置Swagger文档
    if (configService.get('ENABLE_SWAGGER', 'true') === 'true') {
      const config = new DocumentBuilder()
        .setTitle('深度学习推理服务')
        .setDescription('提供AI模型推理、管理和监控功能的微服务API')
        .setVersion('1.0.0')
        .addTag('inference', '模型推理相关接口')
        .addTag('models', '模型管理相关接口')
        .addTag('monitoring', '监控和统计相关接口')
        .addBearerAuth()
        .addServer('http://localhost:3020', '本地开发环境')
        .addServer('https://api.example.com', '生产环境')
        .build();

      const document = SwaggerModule.createDocument(app, config);
      SwaggerModule.setup(
        configService.get('SWAGGER_PATH', 'api/docs'),
        app,
        document,
        {
          swaggerOptions: {
            persistAuthorization: true,
            displayRequestDuration: true,
          },
        }
      );

      logger.log(`Swagger文档已启用: http://localhost:${configService.get('PORT', 3020)}/${configService.get('SWAGGER_PATH', 'api/docs')}`);
    }

    // 配置微服务
    const microservicePort = configService.get('MICROSERVICE_PORT', 3021);
    app.connectMicroservice<MicroserviceOptions>({
      transport: Transport.TCP,
      options: {
        host: configService.get('MICROSERVICE_HOST', 'localhost'),
        port: microservicePort,
        retryAttempts: configService.get('MICROSERVICE_RETRY_ATTEMPTS', 5),
        retryDelay: configService.get('MICROSERVICE_RETRY_DELAY', 3000),
      },
    });

    // 启动微服务
    await app.startAllMicroservices();
    logger.log(`微服务已启动在端口: ${microservicePort}`);

    // 启动HTTP服务
    const port = configService.get('PORT', 3020);
    const host = configService.get('HOST', '0.0.0.0');
    
    await app.listen(port, host);

    logger.log(`🚀 深度学习推理服务已启动`);
    logger.log(`📍 HTTP服务地址: http://${host}:${port}`);
    logger.log(`📍 API文档地址: http://${host}:${port}/${configService.get('SWAGGER_PATH', 'api/docs')}`);
    logger.log(`📍 健康检查: http://${host}:${port}/api/v1/health`);
    logger.log(`🔧 环境: ${configService.get('NODE_ENV', 'development')}`);

    // 优雅关闭处理
    process.on('SIGTERM', async () => {
      logger.log('收到SIGTERM信号，正在优雅关闭...');
      await app.close();
      process.exit(0);
    });

    process.on('SIGINT', async () => {
      logger.log('收到SIGINT信号，正在优雅关闭...');
      await app.close();
      process.exit(0);
    });

  } catch (error) {
    logger.error('应用启动失败:', error);
    process.exit(1);
  }
}

// 启动应用
bootstrap();

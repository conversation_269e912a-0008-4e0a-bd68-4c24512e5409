#!/usr/bin/env node

/**
 * 边缘AI服务API测试脚本
 * 用于测试服务的主要API端点
 */

const axios = require('axios');

const BASE_URL = process.env.API_BASE_URL || 'http://localhost:3006/api/v1';

// 测试数据
const testDevice = {
  name: '测试边缘设备',
  type: 'industrial_pc',
  location: '测试实验室',
  capabilities: {
    cpu: { cores: 4, frequency: 2400, architecture: 'x86_64' },
    memory: { total: 8192, available: 6144 },
    storage: { total: 256, available: 200 },
    connectivity: ['ethernet', 'wifi']
  },
  networkInfo: {
    ipAddress: '*************',
    bandwidth: 1000,
    latency: 10,
    reliability: 99.9,
    protocol: 'TCP/IP',
    encryption: true
  }
};

const testInference = {
  requestId: `test-${Date.now()}`,
  modelId: 'anomaly_detection_v1',
  inputData: { 
    image: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k='
  },
  priority: 'medium',
  timeout: 5000
};

const testLearningTask = {
  name: '测试联邦学习任务',
  algorithm: 'federated_learning',
  participants: ['edge_001', 'edge_002'],
  modelTemplate: {
    type: 'classification',
    architecture: 'cnn',
    layers: [
      { type: 'conv2d', filters: 32, kernelSize: 3 },
      { type: 'maxpool2d', poolSize: 2 },
      { type: 'flatten' },
      { type: 'dense', units: 10, activation: 'softmax' }
    ]
  },
  rounds: 5,
  learningRate: 0.01,
  batchSize: 32
};

// 测试函数
async function testAPI() {
  console.log('🚀 开始测试边缘AI服务API...\n');

  try {
    // 1. 测试服务健康状态
    console.log('1. 测试服务健康状态...');
    const healthResponse = await axios.get(`${BASE_URL}/edge/statistics`);
    console.log('✅ 服务健康状态正常');
    console.log(`   设备数量: ${healthResponse.data.data.devices.total}`);
    console.log(`   模型数量: ${healthResponse.data.data.models.total}\n`);

    // 2. 注册边缘设备
    console.log('2. 注册边缘设备...');
    const deviceResponse = await axios.post(`${BASE_URL}/edge/devices/register`, testDevice);
    const deviceId = deviceResponse.data.data.deviceId;
    console.log('✅ 设备注册成功');
    console.log(`   设备ID: ${deviceId}\n`);

    // 3. 获取设备列表
    console.log('3. 获取设备列表...');
    const devicesResponse = await axios.get(`${BASE_URL}/devices`);
    console.log('✅ 设备列表获取成功');
    console.log(`   设备数量: ${devicesResponse.data.data.length}\n`);

    // 4. 更新设备心跳
    console.log('4. 更新设备心跳...');
    await axios.put(`${BASE_URL}/devices/${deviceId}/heartbeat`);
    console.log('✅ 设备心跳更新成功\n');

    // 5. 执行边缘推理
    console.log('5. 执行边缘推理...');
    const inferenceResponse = await axios.post(`${BASE_URL}/edge/inference`, testInference);
    console.log('✅ 边缘推理执行成功');
    console.log(`   处理时间: ${inferenceResponse.data.data.processingTime}ms`);
    console.log(`   置信度: ${inferenceResponse.data.data.confidence}\n`);

    // 6. 获取推理统计
    console.log('6. 获取推理统计...');
    const inferenceStatsResponse = await axios.get(`${BASE_URL}/inference/statistics`);
    console.log('✅ 推理统计获取成功');
    console.log(`   总推理次数: ${inferenceStatsResponse.data.data.total}`);
    console.log(`   成功率: ${inferenceStatsResponse.data.data.successRate.toFixed(2)}%\n`);

    // 7. 启动分布式学习任务
    console.log('7. 启动分布式学习任务...');
    try {
      const learningResponse = await axios.post(`${BASE_URL}/edge/learning/start`, testLearningTask);
      console.log('✅ 分布式学习任务启动成功');
      console.log(`   任务ID: ${learningResponse.data.data.taskId}\n`);
    } catch (error) {
      console.log('⚠️  分布式学习任务启动失败（可能是参与者不足）\n');
    }

    // 8. 执行实时优化
    console.log('8. 执行实时优化...');
    const optimizationRequest = {
      context: {
        currentLoad: { cpu: 75, memory: 60 },
        performanceMetrics: { latency: 50, throughput: 100 }
      },
      constraints: {
        maxLatency: 100,
        minThroughput: 80
      }
    };
    const optimizationResponse = await axios.post(`${BASE_URL}/edge/optimization`, optimizationRequest);
    console.log('✅ 实时优化执行成功');
    console.log(`   预期改善: ${optimizationResponse.data.data.expectedImprovement}%\n`);

    // 9. 获取监控指标
    console.log('9. 获取监控指标...');
    const monitoringResponse = await axios.get(`${BASE_URL}/monitoring/metrics`);
    console.log('✅ 监控指标获取成功\n');

    // 10. 清理测试数据
    console.log('10. 清理测试数据...');
    try {
      await axios.delete(`${BASE_URL}/devices/${deviceId}`);
      console.log('✅ 测试设备删除成功\n');
    } catch (error) {
      console.log('⚠️  测试设备删除失败（可能已被删除）\n');
    }

    console.log('🎉 所有API测试完成！');

  } catch (error) {
    console.error('❌ API测试失败:', error.message);
    if (error.response) {
      console.error('   状态码:', error.response.status);
      console.error('   错误信息:', error.response.data);
    }
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  testAPI();
}

module.exports = { testAPI };

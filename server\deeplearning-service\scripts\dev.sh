#!/bin/bash

# 深度学习推理服务开发启动脚本

echo "🚀 启动深度学习推理服务开发环境..."

# 检查Node.js版本
NODE_VERSION=$(node --version)
echo "📦 Node.js版本: $NODE_VERSION"

# 检查npm版本
NPM_VERSION=$(npm --version)
echo "📦 npm版本: $NPM_VERSION"

# 检查环境配置文件
if [ ! -f ".env" ]; then
    echo "⚠️  未找到.env文件，复制.env.example..."
    cp .env.example .env
    echo "✅ 已创建.env文件，请根据需要修改配置"
fi

# 检查模型存储目录
if [ ! -d "models" ]; then
    echo "📁 创建模型存储目录..."
    mkdir -p models
    echo "✅ 模型存储目录已创建"
fi

# 检查Redis连接
echo "🔍 检查Redis连接..."
if command -v redis-cli &> /dev/null; then
    if redis-cli ping &> /dev/null; then
        echo "✅ Redis连接正常"
    else
        echo "❌ Redis连接失败，请确保Redis服务正在运行"
        echo "💡 启动Redis: redis-server"
        exit 1
    fi
else
    echo "⚠️  未安装redis-cli，跳过Redis连接检查"
fi

# 安装依赖（如果需要）
if [ ! -d "node_modules" ]; then
    echo "📦 安装依赖..."
    npm install
fi

# 构建项目
echo "🔨 构建项目..."
npm run build

if [ $? -eq 0 ]; then
    echo "✅ 构建成功"
else
    echo "❌ 构建失败"
    exit 1
fi

# 启动开发服务器
echo "🚀 启动开发服务器..."
echo "📍 服务地址: http://localhost:3020"
echo "📍 API文档: http://localhost:3020/api/docs"
echo "📍 健康检查: http://localhost:3020/health"
echo ""
echo "按 Ctrl+C 停止服务"
echo ""

npm run start:dev

# 多阶段构建 - 边缘计算增强服务
# 基于Node.js 18 Alpine镜像

# ================================
# 构建阶段
# ================================
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装构建依赖
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git \
    curl

# 复制package文件
COPY package*.json ./
COPY tsconfig*.json ./
COPY nest-cli.json ./

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# 复制源代码
COPY src/ ./src/

# 构建应用
RUN npm run build

# ================================
# 生产阶段
# ================================
FROM node:18-alpine AS production

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=3040
ENV TZ=Asia/Shanghai

# 创建应用用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nestjs -u 1001

# 设置工作目录
WORKDIR /app

# 安装运行时依赖
RUN apk add --no-cache \
    dumb-init \
    curl \
    tzdata && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

# 复制构建产物
COPY --from=builder --chown=nestjs:nodejs /app/dist ./dist
COPY --from=builder --chown=nestjs:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nestjs:nodejs /app/package*.json ./

# 创建必要的目录
RUN mkdir -p /app/logs /app/data /app/cache && \
    chown -R nestjs:nodejs /app

# 切换到应用用户
USER nestjs

# 暴露端口
EXPOSE 3040

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:3040/api/v1/edge-enhancement/health || exit 1

# 启动应用
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "dist/main.js"]

# ================================
# 开发阶段
# ================================
FROM node:18-alpine AS development

# 设置环境变量
ENV NODE_ENV=development
ENV PORT=3040

# 设置工作目录
WORKDIR /app

# 安装开发依赖
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git \
    curl

# 复制package文件
COPY package*.json ./
COPY tsconfig*.json ./
COPY nest-cli.json ./

# 安装所有依赖（包括开发依赖）
RUN npm install

# 复制源代码
COPY src/ ./src/
COPY test/ ./test/

# 暴露端口
EXPOSE 3040
EXPOSE 9229

# 启动开发服务器
CMD ["npm", "run", "start:dev"]

# ================================
# 测试阶段
# ================================
FROM development AS test

# 运行测试
RUN npm run test

# 运行代码覆盖率测试
RUN npm run test:cov

# ================================
# 构建信息
# ================================
LABEL maintainer="DL Engine Team"
LABEL version="1.0.0"
LABEL description="边缘计算增强服务 - 提供智能调度、预测性缓存和自适应网络传输功能"
LABEL org.opencontainers.image.title="Edge Enhancement Service"
LABEL org.opencontainers.image.description="边缘计算增强服务"
LABEL org.opencontainers.image.version="1.0.0"
LABEL org.opencontainers.image.vendor="DL Engine Team"
LABEL org.opencontainers.image.licenses="MIT"

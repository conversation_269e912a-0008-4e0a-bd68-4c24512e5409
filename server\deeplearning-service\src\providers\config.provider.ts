/**
 * 配置提供者
 * 
 * 提供统一的配置管理和访问接口
 */

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

/**
 * 配置提供者
 */
@Injectable()
export class ConfigProvider {
  private readonly logger = new Logger(ConfigProvider.name);

  constructor(
    private readonly configService: ConfigService,
  ) {
    this.validateConfiguration();
  }

  /**
   * 获取配置值
   */
  public get<T = string>(key: string, defaultValue?: T): T {
    return this.configService.get<T>(key, defaultValue);
  }

  /**
   * 获取数字配置
   */
  public getNumber(key: string, defaultValue?: number): number {
    const value = this.configService.get(key, defaultValue?.toString());
    const parsed = parseInt(value, 10);
    
    if (isNaN(parsed)) {
      if (defaultValue !== undefined) {
        return defaultValue;
      }
      throw new Error(`配置值 ${key} 不是有效的数字: ${value}`);
    }
    
    return parsed;
  }

  /**
   * 获取浮点数配置
   */
  public getFloat(key: string, defaultValue?: number): number {
    const value = this.configService.get(key, defaultValue?.toString());
    const parsed = parseFloat(value);
    
    if (isNaN(parsed)) {
      if (defaultValue !== undefined) {
        return defaultValue;
      }
      throw new Error(`配置值 ${key} 不是有效的浮点数: ${value}`);
    }
    
    return parsed;
  }

  /**
   * 获取布尔配置
   */
  public getBoolean(key: string, defaultValue?: boolean): boolean {
    const value = this.configService.get(key, defaultValue?.toString());
    
    if (typeof value === 'boolean') {
      return value;
    }
    
    if (typeof value === 'string') {
      const lowerValue = value.toLowerCase();
      if (lowerValue === 'true' || lowerValue === '1' || lowerValue === 'yes') {
        return true;
      }
      if (lowerValue === 'false' || lowerValue === '0' || lowerValue === 'no') {
        return false;
      }
    }
    
    if (defaultValue !== undefined) {
      return defaultValue;
    }
    
    throw new Error(`配置值 ${key} 不是有效的布尔值: ${value}`);
  }

  /**
   * 获取数组配置
   */
  public getArray(key: string, separator: string = ',', defaultValue?: string[]): string[] {
    const value = this.configService.get(key);
    
    if (!value) {
      return defaultValue || [];
    }
    
    if (Array.isArray(value)) {
      return value;
    }
    
    if (typeof value === 'string') {
      return value.split(separator).map(item => item.trim()).filter(item => item.length > 0);
    }
    
    return defaultValue || [];
  }

  /**
   * 获取JSON配置
   */
  public getJson<T = any>(key: string, defaultValue?: T): T {
    const value = this.configService.get(key);
    
    if (!value) {
      if (defaultValue !== undefined) {
        return defaultValue;
      }
      throw new Error(`配置值 ${key} 不存在`);
    }
    
    if (typeof value === 'object') {
      return value as T;
    }
    
    if (typeof value === 'string') {
      try {
        return JSON.parse(value) as T;
      } catch (error) {
        throw new Error(`配置值 ${key} 不是有效的JSON: ${value}`);
      }
    }
    
    throw new Error(`配置值 ${key} 类型不支持: ${typeof value}`);
  }

  /**
   * 检查配置是否存在
   */
  public has(key: string): boolean {
    const value = this.configService.get(key);
    return value !== undefined && value !== null;
  }

  /**
   * 获取所有配置
   */
  public getAll(): Record<string, any> {
    // 注意：这可能包含敏感信息，谨慎使用
    return process.env;
  }

  /**
   * 获取服务配置
   */
  public getServiceConfig() {
    return {
      name: this.get('SERVICE_NAME', 'deeplearning-service'),
      version: this.get('SERVICE_VERSION', '1.0.0'),
      environment: this.get('NODE_ENV', 'development'),
      port: this.getNumber('PORT', 3020),
      host: this.get('HOST', '0.0.0.0'),
    };
  }

  /**
   * 获取Redis配置
   */
  public getRedisConfig() {
    return {
      host: this.get('REDIS_HOST', 'localhost'),
      port: this.getNumber('REDIS_PORT', 6379),
      password: this.get('REDIS_PASSWORD'),
      db: this.getNumber('REDIS_DB', 0),
      connectTimeout: this.getNumber('REDIS_CONNECT_TIMEOUT', 10000),
      commandTimeout: this.getNumber('REDIS_COMMAND_TIMEOUT', 5000),
    };
  }

  /**
   * 获取推理配置
   */
  public getInferenceConfig() {
    return {
      maxConcurrentInferences: this.getNumber('MAX_CONCURRENT_INFERENCES', 10),
      maxQueueSize: this.getNumber('MAX_QUEUE_SIZE', 1000),
      modelCacheSize: this.getNumber('MODEL_CACHE_SIZE', 5),
      defaultTimeout: this.getNumber('INFERENCE_TIMEOUT', 30000),
      cleanupInterval: this.getNumber('CLEANUP_INTERVAL', 3600000),
      metricsRetention: this.getNumber('METRICS_RETENTION', 86400000),
    };
  }

  /**
   * 获取模型配置
   */
  public getModelConfig() {
    return {
      modelStoragePath: this.get('MODEL_STORAGE_PATH', './models'),
      maxModelSize: this.getNumber('MAX_MODEL_SIZE', **********), // 1GB
      supportedFormats: this.getArray('SUPPORTED_MODEL_FORMATS', ',', ['onnx', 'pytorch', 'tensorflow']),
      autoLoadModels: this.getBoolean('AUTO_LOAD_MODELS', true),
      modelRegistryUrl: this.get('MODEL_REGISTRY_URL'),
    };
  }

  /**
   * 获取监控配置
   */
  public getMonitoringConfig() {
    return {
      enableMetrics: this.getBoolean('ENABLE_METRICS', true),
      metricsInterval: this.getNumber('METRICS_INTERVAL', 60000),
      enableHealthCheck: this.getBoolean('ENABLE_HEALTH_CHECK', true),
      healthCheckInterval: this.getNumber('HEALTH_CHECK_INTERVAL', 30000),
      alertThresholds: {
        errorRate: this.getFloat('ALERT_ERROR_RATE_THRESHOLD', 0.1),
        latency: this.getNumber('ALERT_LATENCY_THRESHOLD', 5000),
        queueSize: this.getNumber('ALERT_QUEUE_SIZE_THRESHOLD', 500),
        memoryUsage: this.getFloat('ALERT_MEMORY_USAGE_THRESHOLD', 0.8),
      },
    };
  }

  /**
   * 获取CORS配置
   */
  public getCorsConfig() {
    return {
      origin: this.get('CORS_ORIGIN', '*'),
      methods: this.getArray('CORS_METHODS', ',', ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']),
      allowedHeaders: this.getArray('CORS_HEADERS', ',', ['Content-Type', 'Authorization', 'X-Request-ID']),
      credentials: this.getBoolean('CORS_CREDENTIALS', true),
    };
  }

  /**
   * 获取Swagger配置
   */
  public getSwaggerConfig() {
    return {
      enabled: this.getBoolean('ENABLE_SWAGGER', true),
      path: this.get('SWAGGER_PATH', 'api/docs'),
      title: this.get('SWAGGER_TITLE', '深度学习推理服务'),
      description: this.get('SWAGGER_DESCRIPTION', '提供AI模型推理、管理和监控功能的微服务API'),
      version: this.get('SWAGGER_VERSION', '1.0.0'),
    };
  }

  /**
   * 获取微服务配置
   */
  public getMicroserviceConfig() {
    return {
      host: this.get('MICROSERVICE_HOST', 'localhost'),
      port: this.getNumber('MICROSERVICE_PORT', 3021),
      retryAttempts: this.getNumber('MICROSERVICE_RETRY_ATTEMPTS', 5),
      retryDelay: this.getNumber('MICROSERVICE_RETRY_DELAY', 3000),
    };
  }

  /**
   * 获取日志配置
   */
  public getLogConfig() {
    return {
      level: this.get('LOG_LEVEL', 'info'),
      enableDebug: this.getBoolean('DEBUG', false),
      filePath: this.get('LOG_FILE_PATH'),
      maxSize: this.getNumber('LOG_FILE_MAX_SIZE', 10), // MB
      maxDays: this.getNumber('LOG_FILE_MAX_DAYS', 30),
    };
  }

  /**
   * 验证必需的配置
   */
  private validateConfiguration(): void {
    const requiredConfigs = [
      'NODE_ENV',
      'PORT',
    ];

    const missingConfigs = requiredConfigs.filter(config => !this.has(config));

    if (missingConfigs.length > 0) {
      this.logger.warn(`缺少以下配置，将使用默认值: ${missingConfigs.join(', ')}`);
    }

    // 验证端口范围
    const port = this.getNumber('PORT', 3020);
    if (port < 1 || port > 65535) {
      throw new Error(`端口号无效: ${port}，必须在1-65535范围内`);
    }

    // 验证Redis配置
    const redisPort = this.getNumber('REDIS_PORT', 6379);
    if (redisPort < 1 || redisPort > 65535) {
      throw new Error(`Redis端口号无效: ${redisPort}，必须在1-65535范围内`);
    }

    this.logger.log('配置验证完成');
  }

  /**
   * 获取敏感配置的掩码版本（用于日志）
   */
  public getMaskedConfig(): Record<string, any> {
    const config = this.getAll();
    const maskedConfig: Record<string, any> = {};

    const sensitiveKeys = [
      'password',
      'secret',
      'key',
      'token',
      'auth',
      'credential',
    ];

    for (const [key, value] of Object.entries(config)) {
      const lowerKey = key.toLowerCase();
      const isSensitive = sensitiveKeys.some(sensitiveKey => lowerKey.includes(sensitiveKey));

      if (isSensitive && value) {
        maskedConfig[key] = '***';
      } else {
        maskedConfig[key] = value;
      }
    }

    return maskedConfig;
  }
}

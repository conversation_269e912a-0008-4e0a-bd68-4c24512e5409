version: '3.8'

services:
  edge-router:
    build: .
    ports:
      - "3012:3012"
    environment:
      - NODE_ENV=production
      - PORT=3012
      - EDGE_REGISTRY_HOST=edge-registry
      - EDGE_REGISTRY_PORT=3011
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DB=1
    depends_on:
      - redis
    restart: unless-stopped
    networks:
      - edge-router-network

  redis:
    image: redis:7-alpine
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - edge-router-network

volumes:
  redis_data:

networks:
  edge-router-network:
    driver: bridge

import { registerAs } from '@nestjs/config';

export default registerAs('database', () => ({
  host: process.env.DATABASE_HOST || 'localhost',
  port: parseInt(process.env.DATABASE_PORT, 10) || 5432,
  username: process.env.DATABASE_USERNAME || 'edge_user',
  password: process.env.DATABASE_PASSWORD || 'edge_password',
  name: process.env.DATABASE_NAME || 'edge_enhancement',
  ssl: process.env.DATABASE_SSL === 'true',
  synchronize: process.env.NODE_ENV !== 'production',
  logging: process.env.NODE_ENV === 'development',
}));

export const databaseConfig = registerAs('database', () => ({
  host: process.env.DATABASE_HOST || 'localhost',
  port: parseInt(process.env.DATABASE_PORT, 10) || 5432,
  username: process.env.DATABASE_USERNAME || 'edge_user',
  password: process.env.DATABASE_PASSWORD || 'edge_password',
  name: process.env.DATABASE_NAME || 'edge_enhancement',
  ssl: process.env.DATABASE_SSL === 'true',
  synchronize: process.env.NODE_ENV !== 'production',
  logging: process.env.NODE_ENV === 'development',
}));

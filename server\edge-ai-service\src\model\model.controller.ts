import { Controller, Get } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { ModelService } from './model.service';

@ApiTags('models')
@Controller('models')
export class ModelController {
  constructor(private readonly modelService: ModelService) {}

  @Get()
  async findAll() {
    return {
      success: true,
      data: await this.modelService.findAll(),
      message: '模型列表获取成功',
    };
  }
}

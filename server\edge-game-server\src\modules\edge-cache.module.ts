import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';

// 服务
import { EdgeCacheService } from '../services/edge-cache.service';
import { EdgeRedisService } from '../services/edge-redis.service';

// 控制器
import { EdgeCacheController } from '../controllers/edge-cache.controller';

/**
 * 边缘缓存模块
 * 提供边缘节点的本地缓存和Redis缓存功能
 */
@Module({
  imports: [
    ConfigModule,
    EventEmitterModule,
  ],
  
  controllers: [
    EdgeCacheController,
  ],
  
  providers: [
    EdgeCacheService,
    EdgeRedisService,
    {
      provide: 'REDIS_CONFIG',
      useFactory: (configService: ConfigService) => ({
        host: configService.get<string>('REDIS_HOST', 'localhost'),
        port: configService.get<number>('REDIS_PORT', 6379),
        password: configService.get<string>('REDIS_PASSWORD'),
        db: configService.get<number>('REDIS_DB', 0),
        retryDelayOnFailover: 100,
        retryDelayOnClusterDown: 300,
        retryDelayOnFailoverAttempts: 3,
        maxRetriesPerRequest: 3,
        lazyConnect: true,
        keepAlive: 30000,
        connectTimeout: 10000,
        commandTimeout: 5000,
      }),
      inject: [ConfigService],
    },
  ],
  
  exports: [
    EdgeCacheService,
    EdgeRedisService,
  ],
})
export class EdgeCacheModule {}

import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { EventEmitter2 } from '@nestjs/event-emitter';

import { EdgeNodeEntity, EdgeNodeStatus } from '../entities/edge-node.entity';
import { EdgeNodeManagerService, EdgeNode } from './edge-node-manager.service';

/**
 * 边缘注册中心服务
 * 负责数据持久化和内存管理的协调
 */
@Injectable()
export class EdgeRegistryService implements OnModuleInit {
  private readonly logger = new Logger(EdgeRegistryService.name);

  constructor(
    @InjectRepository(EdgeNodeEntity)
    private readonly nodeRepository: Repository<EdgeNodeEntity>,
    private readonly nodeManager: EdgeNodeManagerService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  async onModuleInit() {
    this.logger.log('边缘注册中心服务初始化');
    await this.loadNodesFromDatabase();
    this.setupEventListeners();
  }

  /**
   * 从数据库加载节点到内存
   */
  private async loadNodesFromDatabase(): Promise<void> {
    try {
      const nodes = await this.nodeRepository.find();
      this.logger.log(`从数据库加载 ${nodes.length} 个节点`);

      for (const nodeEntity of nodes) {
        const nodeData = nodeEntity.toBusinessObject();
        await this.nodeManager.registerNode(nodeData);
      }
    } catch (error) {
      this.logger.error(`从数据库加载节点失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听节点注册事件
    this.eventEmitter.on('edge.node.registered', async (node: EdgeNode) => {
      await this.saveNodeToDatabase(node);
    });

    // 监听节点注销事件
    this.eventEmitter.on('edge.node.unregistered', async (node: EdgeNode) => {
      await this.removeNodeFromDatabase(node.nodeId);
    });

    // 监听节点状态变更事件
    this.eventEmitter.on('edge.node.status.changed', async (event: any) => {
      await this.updateNodeInDatabase(event.node);
    });
  }

  /**
   * 保存节点到数据库
   */
  private async saveNodeToDatabase(node: EdgeNode): Promise<void> {
    try {
      const nodeEntity = EdgeNodeEntity.fromBusinessObject(node);
      await this.nodeRepository.save(nodeEntity);
      this.logger.log(`节点 ${node.nodeId} 已保存到数据库`);
    } catch (error) {
      this.logger.error(`保存节点到数据库失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 从数据库删除节点
   */
  private async removeNodeFromDatabase(nodeId: string): Promise<void> {
    try {
      await this.nodeRepository.delete({ nodeId });
      this.logger.log(`节点 ${nodeId} 已从数据库删除`);
    } catch (error) {
      this.logger.error(`从数据库删除节点失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 更新数据库中的节点信息
   */
  private async updateNodeInDatabase(node: EdgeNode): Promise<void> {
    try {
      const updateData = {
        status: node.status,
        currentUsers: node.metrics.currentUsers,
        cpuUsage: node.metrics.cpuUsage,
        memoryUsage: node.metrics.memoryUsage,
        networkLatency: node.metrics.networkLatency,
        uptime: node.metrics.uptime,
        lastHeartbeat: node.lastHeartbeat,
        metadata: node.metadata,
      };

      await this.nodeRepository.update({ nodeId: node.nodeId }, updateData);
    } catch (error) {
      this.logger.error(`更新数据库节点信息失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 注册节点（对外接口）
   */
  async registerNode(nodeData: Partial<EdgeNode>): Promise<EdgeNode> {
    return await this.nodeManager.registerNode(nodeData);
  }

  /**
   * 注销节点（对外接口）
   */
  async unregisterNode(nodeId: string): Promise<boolean> {
    return await this.nodeManager.unregisterNode(nodeId);
  }

  /**
   * 更新心跳（对外接口）
   */
  async updateHeartbeat(nodeId: string, metrics?: Partial<EdgeNode['metrics']>): Promise<boolean> {
    const success = await this.nodeManager.updateHeartbeat(nodeId, metrics);
    
    if (success) {
      // 异步更新数据库
      const node = this.nodeManager.getNode(nodeId);
      if (node) {
        setImmediate(() => this.updateNodeInDatabase(node));
      }
    }
    
    return success;
  }

  /**
   * 获取最优节点（对外接口）
   */
  async getOptimalNode(
    clientLocation?: { latitude: number; longitude: number },
    strategy?: any,
    region?: string,
  ): Promise<EdgeNode | null> {
    return await this.nodeManager.getOptimalNode(clientLocation, strategy, region);
  }

  /**
   * 获取所有节点（对外接口）
   */
  getAllNodes(): EdgeNode[] {
    return this.nodeManager.getAllNodes();
  }

  /**
   * 获取区域节点（对外接口）
   */
  getNodesByRegion(region: string): EdgeNode[] {
    return this.nodeManager.getNodesByRegion(region);
  }

  /**
   * 获取节点（对外接口）
   */
  getNode(nodeId: string): EdgeNode | undefined {
    return this.nodeManager.getNode(nodeId);
  }

  /**
   * 获取集群统计（对外接口）
   */
  getClusterStats() {
    return this.nodeManager.getClusterStats();
  }

  /**
   * 获取可用区域（对外接口）
   */
  getAvailableRegions(): string[] {
    return this.nodeManager.getAvailableRegions();
  }

  /**
   * 获取数据库中的节点统计
   */
  async getDatabaseStats(): Promise<{
    totalNodes: number;
    nodesByStatus: Record<string, number>;
    nodesByRegion: Record<string, number>;
  }> {
    try {
      const totalNodes = await this.nodeRepository.count();
      
      const statusStats = await this.nodeRepository
        .createQueryBuilder('node')
        .select('node.status', 'status')
        .addSelect('COUNT(*)', 'count')
        .groupBy('node.status')
        .getRawMany();

      const regionStats = await this.nodeRepository
        .createQueryBuilder('node')
        .select('node.region', 'region')
        .addSelect('COUNT(*)', 'count')
        .groupBy('node.region')
        .getRawMany();

      const nodesByStatus = statusStats.reduce((acc, item) => {
        acc[item.status] = parseInt(item.count);
        return acc;
      }, {});

      const nodesByRegion = regionStats.reduce((acc, item) => {
        acc[item.region] = parseInt(item.count);
        return acc;
      }, {});

      return {
        totalNodes,
        nodesByStatus,
        nodesByRegion,
      };
    } catch (error) {
      this.logger.error(`获取数据库统计失败: ${error.message}`, error.stack);
      return {
        totalNodes: 0,
        nodesByStatus: {},
        nodesByRegion: {},
      };
    }
  }

  /**
   * 清理离线节点
   */
  async cleanupOfflineNodes(timeoutMinutes: number = 30): Promise<number> {
    try {
      const cutoffTime = new Date(Date.now() - timeoutMinutes * 60 * 1000);
      
      const result = await this.nodeRepository
        .createQueryBuilder()
        .delete()
        .from(EdgeNodeEntity)
        .where('lastHeartbeat < :cutoffTime', { cutoffTime })
        .andWhere('status = :status', { status: EdgeNodeStatus.OFFLINE })
        .execute();

      this.logger.log(`清理了 ${result.affected} 个离线节点`);
      return result.affected || 0;
    } catch (error) {
      this.logger.error(`清理离线节点失败: ${error.message}`, error.stack);
      return 0;
    }
  }
}

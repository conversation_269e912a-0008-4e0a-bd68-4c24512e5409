#!/bin/bash

# 群体协调服务开发脚本
# 用于快速启动开发环境

set -e

echo "🚀 启动群体协调服务开发环境..."

# 检查 Node.js 版本
echo "📋 检查 Node.js 版本..."
node_version=$(node -v)
echo "Node.js 版本: $node_version"

# 检查 npm 版本
echo "📋 检查 npm 版本..."
npm_version=$(npm -v)
echo "npm 版本: $npm_version"

# 安装依赖
echo "📦 安装依赖..."
npm install

# 检查 Redis 连接
echo "🔍 检查 Redis 连接..."
if command -v redis-cli &> /dev/null; then
    if redis-cli ping &> /dev/null; then
        echo "✅ Redis 连接正常"
    else
        echo "❌ Redis 连接失败，请确保 Redis 服务正在运行"
        echo "💡 可以使用以下命令启动 Redis:"
        echo "   docker run -d -p 6379:6379 redis:7-alpine"
        exit 1
    fi
else
    echo "⚠️  未找到 redis-cli，跳过 Redis 连接检查"
fi

# 运行代码检查
echo "🔍 运行代码检查..."
npm run lint

# 运行测试
echo "🧪 运行测试..."
npm run test

# 启动开发服务器
echo "🎯 启动开发服务器..."
npm run start:dev

import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import helmet from 'helmet';
import * as compression from 'compression';
import * as cors from 'cors';

// 简化的测试模块
import { Module } from '@nestjs/common';
import { EdgeAIService } from './edge/edge-ai.service';
import { EdgeController } from './edge/edge.controller';

@Module({
  imports: [],
  controllers: [EdgeController],
  providers: [EdgeAIService],
})
export class TestAppModule {}

async function bootstrap() {
  console.log('🚀 启动边缘AI计算服务（测试模式）...');
  
  try {
    // 创建HTTP应用
    const app = await NestFactory.create(TestAppModule);

    // 安全中间件
    app.use(helmet());
    app.use(compression());
    app.use(cors({
      origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
      credentials: true
    }));

    // 全局验证管道
    app.useGlobalPipes(new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true
      }
    }));

    // 全局前缀
    app.setGlobalPrefix('api/v1');

    // Swagger文档配置
    const config = new DocumentBuilder()
      .setTitle('边缘AI计算服务（测试版）')
      .setDescription('边缘设备AI推理、分布式学习、实时决策优化服务API')
      .setVersion('1.0')
      .addTag('edge-ai', '边缘AI计算')
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('api/docs', app, document, {
      swaggerOptions: {
        persistAuthorization: true,
      },
    });

    // 启动HTTP服务器
    const port = process.env.PORT || 3001;
    await app.listen(port);

    console.log(`🚀 边缘AI计算服务已启动（测试模式）`);
    console.log(`📖 API文档: http://localhost:${port}/api/docs`);
    console.log(`🔗 服务地址: http://localhost:${port}/api/v1`);
    console.log(`✅ 服务运行正常，可以进行API测试`);
  } catch (error) {
    console.error('❌ 启动边缘AI计算服务失败:', error);
    (process as any).exit(1);
  }
}

bootstrap();

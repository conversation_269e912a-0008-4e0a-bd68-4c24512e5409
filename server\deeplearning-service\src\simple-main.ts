/**
 * 最简单的启动文件 - 用于调试
 */

console.log('🚀 开始启动深度学习推理服务...');

import { NestFactory } from '@nestjs/core';
import { Logger } from '@nestjs/common';

console.log('✅ 导入完成');

// 最简单的模块
import { Module, Controller, Get } from '@nestjs/common';

@Controller()
export class SimpleController {
  @Get('health')
  health() {
    return { 
      status: 'ok', 
      service: 'deeplearning-service',
      timestamp: new Date().toISOString()
    };
  }

  @Get('test')
  test() {
    return { 
      message: '深度学习推理服务测试接口',
      version: '1.0.0'
    };
  }
}

@Module({
  controllers: [SimpleController],
})
export class SimpleModule {}

async function bootstrap() {
  const logger = new Logger('SimpleBootstrap');
  
  try {
    console.log('📦 创建NestJS应用...');
    
    const app = await NestFactory.create(SimpleModule, {
      logger: ['log', 'error', 'warn', 'debug'],
    });

    console.log('⚙️ 配置应用...');

    // 启用CORS
    app.enableCors({
      origin: '*',
      methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
      allowedHeaders: 'Content-Type, Accept, Authorization',
    });

    const port = 3020;
    const host = '0.0.0.0';
    
    console.log(`🌐 启动服务在 ${host}:${port}...`);
    
    await app.listen(port, host);

    logger.log('🎉 服务启动成功！');
    logger.log(`📍 健康检查: http://localhost:${port}/health`);
    logger.log(`📍 测试接口: http://localhost:${port}/test`);
    
    console.log('✅ 启动完成');

  } catch (error) {
    console.error('❌ 启动失败:', error);
    logger.error('启动失败:', error);
    process.exit(1);
  }
}

console.log('🔄 调用bootstrap函数...');
bootstrap();

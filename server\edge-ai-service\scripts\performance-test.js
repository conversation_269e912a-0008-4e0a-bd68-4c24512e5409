#!/usr/bin/env node

/**
 * 边缘AI服务性能测试脚本
 * 测试服务在高并发情况下的性能表现
 */

const axios = require('axios');
const { performance } = require('perf_hooks');

const BASE_URL = process.env.API_BASE_URL || 'http://localhost:3006/api/v1';
const CONCURRENT_REQUESTS = parseInt(process.env.CONCURRENT_REQUESTS) || 10;
const TOTAL_REQUESTS = parseInt(process.env.TOTAL_REQUESTS) || 100;

// 性能测试结果
const results = {
  totalRequests: 0,
  successfulRequests: 0,
  failedRequests: 0,
  totalTime: 0,
  minTime: Infinity,
  maxTime: 0,
  responseTimes: []
};

// 生成测试推理请求
function generateInferenceRequest() {
  return {
    requestId: `perf-test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    modelId: 'anomaly_detection_v1',
    inputData: {
      image: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k=',
      metadata: {
        timestamp: new Date().toISOString(),
        testId: 'performance-test'
      }
    },
    priority: 'medium',
    timeout: 5000
  };
}

// 执行单个推理请求
async function performInferenceRequest() {
  const startTime = performance.now();
  
  try {
    const request = generateInferenceRequest();
    const response = await axios.post(`${BASE_URL}/edge/inference`, request);
    
    const endTime = performance.now();
    const responseTime = endTime - startTime;
    
    results.successfulRequests++;
    results.responseTimes.push(responseTime);
    results.minTime = Math.min(results.minTime, responseTime);
    results.maxTime = Math.max(results.maxTime, responseTime);
    
    return {
      success: true,
      responseTime,
      data: response.data
    };
    
  } catch (error) {
    const endTime = performance.now();
    const responseTime = endTime - startTime;
    
    results.failedRequests++;
    
    return {
      success: false,
      responseTime,
      error: error.message
    };
  }
}

// 并发执行推理请求
async function runConcurrentRequests(count) {
  const promises = [];
  
  for (let i = 0; i < count; i++) {
    promises.push(performInferenceRequest());
  }
  
  return await Promise.all(promises);
}

// 计算统计信息
function calculateStatistics() {
  if (results.responseTimes.length === 0) {
    return null;
  }
  
  const sortedTimes = results.responseTimes.sort((a, b) => a - b);
  const total = sortedTimes.reduce((sum, time) => sum + time, 0);
  
  return {
    totalRequests: results.totalRequests,
    successfulRequests: results.successfulRequests,
    failedRequests: results.failedRequests,
    successRate: (results.successfulRequests / results.totalRequests * 100).toFixed(2),
    averageResponseTime: (total / sortedTimes.length).toFixed(2),
    minResponseTime: results.minTime.toFixed(2),
    maxResponseTime: results.maxTime.toFixed(2),
    medianResponseTime: sortedTimes[Math.floor(sortedTimes.length / 2)].toFixed(2),
    p95ResponseTime: sortedTimes[Math.floor(sortedTimes.length * 0.95)].toFixed(2),
    p99ResponseTime: sortedTimes[Math.floor(sortedTimes.length * 0.99)].toFixed(2),
    requestsPerSecond: (results.successfulRequests / (results.totalTime / 1000)).toFixed(2)
  };
}

// 主性能测试函数
async function runPerformanceTest() {
  console.log('🚀 开始边缘AI服务性能测试...\n');
  console.log(`配置信息:`);
  console.log(`  - 并发请求数: ${CONCURRENT_REQUESTS}`);
  console.log(`  - 总请求数: ${TOTAL_REQUESTS}`);
  console.log(`  - 服务地址: ${BASE_URL}\n`);
  
  // 预热请求
  console.log('🔥 执行预热请求...');
  await performInferenceRequest();
  console.log('✅ 预热完成\n');
  
  const overallStartTime = performance.now();
  
  // 分批执行请求
  const batches = Math.ceil(TOTAL_REQUESTS / CONCURRENT_REQUESTS);
  
  for (let batch = 0; batch < batches; batch++) {
    const batchSize = Math.min(CONCURRENT_REQUESTS, TOTAL_REQUESTS - batch * CONCURRENT_REQUESTS);
    
    console.log(`📊 执行第 ${batch + 1}/${batches} 批请求 (${batchSize} 个请求)...`);
    
    const batchStartTime = performance.now();
    const batchResults = await runConcurrentRequests(batchSize);
    const batchEndTime = performance.now();
    
    results.totalRequests += batchSize;
    
    const batchTime = batchEndTime - batchStartTime;
    const batchSuccessRate = (batchResults.filter(r => r.success).length / batchSize * 100).toFixed(2);
    
    console.log(`   批次完成时间: ${batchTime.toFixed(2)}ms`);
    console.log(`   批次成功率: ${batchSuccessRate}%`);
    
    // 短暂休息避免过载
    if (batch < batches - 1) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }
  
  const overallEndTime = performance.now();
  results.totalTime = overallEndTime - overallStartTime;
  
  // 计算并显示统计信息
  console.log('\n📈 性能测试结果:');
  console.log('=' * 50);
  
  const stats = calculateStatistics();
  if (stats) {
    console.log(`总请求数: ${stats.totalRequests}`);
    console.log(`成功请求数: ${stats.successfulRequests}`);
    console.log(`失败请求数: ${stats.failedRequests}`);
    console.log(`成功率: ${stats.successRate}%`);
    console.log(`平均响应时间: ${stats.averageResponseTime}ms`);
    console.log(`最小响应时间: ${stats.minResponseTime}ms`);
    console.log(`最大响应时间: ${stats.maxResponseTime}ms`);
    console.log(`中位数响应时间: ${stats.medianResponseTime}ms`);
    console.log(`95%响应时间: ${stats.p95ResponseTime}ms`);
    console.log(`99%响应时间: ${stats.p99ResponseTime}ms`);
    console.log(`每秒请求数: ${stats.requestsPerSecond} RPS`);
    console.log(`总测试时间: ${(results.totalTime / 1000).toFixed(2)}s`);
  } else {
    console.log('❌ 无法计算统计信息，所有请求都失败了');
  }
  
  console.log('\n🎉 性能测试完成！');
  
  // 返回结果用于进一步分析
  return stats;
}

// 运行测试
if (require.main === module) {
  runPerformanceTest().catch(error => {
    console.error('❌ 性能测试失败:', error.message);
    process.exit(1);
  });
}

module.exports = { runPerformanceTest };

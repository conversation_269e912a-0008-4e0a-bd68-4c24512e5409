import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

// 实体
import { EdgeNodeEntity } from '../entities/edge-node.entity';

// 服务
import { EdgeNodeManagerService } from '../services/edge-node-manager.service';
import { EdgeRegistryService } from '../services/edge-registry.service';

// 控制器
import { EdgeRegistryController } from '../controllers/edge-registry.controller';
import { EdgeRegistryMicroserviceController } from '../controllers/edge-registry-microservice.controller';

/**
 * 边缘注册中心模块
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([EdgeNodeEntity]),
  ],
  controllers: [
    EdgeRegistryController,
    EdgeRegistryMicroserviceController,
  ],
  providers: [
    EdgeNodeManagerService,
    EdgeRegistryService,
  ],
  exports: [
    EdgeNodeManagerService,
    EdgeRegistryService,
  ],
})
export class EdgeRegistryModule {}

#!/usr/bin/env node

/**
 * 边缘AI服务项目完整性验证脚本
 * 检查项目结构、依赖、配置等是否完整
 */

const fs = require('fs');
const path = require('path');

// 颜色定义
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

// 必需的文件和目录
const requiredStructure = {
  files: [
    'package.json',
    'tsconfig.json',
    'README.md',
    'PROJECT_STATUS.md',
    'Dockerfile',
    'docker-compose.yml',
    'docker-compose.dev.yml',
    'jest.config.js',
    '.env.example',
    'src/main.ts',
    'src/app.module.ts',
    'src/edge/edge-ai.service.ts',
    'src/edge/edge.controller.ts',
    'src/edge/edge.gateway.ts',
    'src/edge/edge.module.ts',
    'src/device/device.module.ts',
    'src/device/device.service.ts',
    'src/device/device.controller.ts',
    'src/device/entities/edge-device.entity.ts',
    'src/model/model.module.ts',
    'src/model/entities/ai-model.entity.ts',
    'src/inference/inference.module.ts',
    'src/inference/entities/inference-request.entity.ts',
    'src/inference/entities/inference-result.entity.ts',
    'src/learning/learning.module.ts',
    'src/learning/entities/learning-task.entity.ts',
    'src/monitoring/monitoring.module.ts',
    'src/monitoring/entities/device-performance.entity.ts',
    'scripts/init.sql',
    'scripts/test-api.js',
    'scripts/performance-test.js',
    'scripts/deploy.sh',
    'k8s/deployment.yaml',
    'nginx/nginx.conf',
    'monitoring/prometheus.yml',
    'monitoring/alert_rules.yml',
    'test/edge-ai.e2e-spec.ts',
    'test/setup.ts'
  ],
  directories: [
    'src',
    'src/edge',
    'src/edge/dto',
    'src/device',
    'src/device/entities',
    'src/model',
    'src/model/entities',
    'src/inference',
    'src/inference/entities',
    'src/learning',
    'src/learning/entities',
    'src/monitoring',
    'src/monitoring/entities',
    'src/optimization',
    'src/websocket',
    'scripts',
    'test',
    'k8s',
    'nginx',
    'monitoring'
  ]
};

// 必需的依赖包
const requiredDependencies = [
  '@nestjs/common',
  '@nestjs/core',
  '@nestjs/platform-express',
  '@nestjs/typeorm',
  '@nestjs/config',
  '@nestjs/swagger',
  '@nestjs/websockets',
  '@nestjs/platform-socket.io',
  'typeorm',
  'mysql2',
  'redis',
  'socket.io',
  'class-validator',
  'class-transformer',
  'rxjs',
  'reflect-metadata'
];

// 验证结果
const results = {
  files: { passed: 0, failed: 0, missing: [] },
  directories: { passed: 0, failed: 0, missing: [] },
  dependencies: { passed: 0, failed: 0, missing: [] },
  configuration: { passed: 0, failed: 0, issues: [] }
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkFileExists(filePath) {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    return false;
  }
}

function checkDirectoryExists(dirPath) {
  try {
    return fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory();
  } catch (error) {
    return false;
  }
}

function verifyFiles() {
  log('\n📁 验证文件结构...', 'blue');
  
  requiredStructure.files.forEach(file => {
    if (checkFileExists(file)) {
      log(`  ✅ ${file}`, 'green');
      results.files.passed++;
    } else {
      log(`  ❌ ${file}`, 'red');
      results.files.failed++;
      results.files.missing.push(file);
    }
  });
}

function verifyDirectories() {
  log('\n📂 验证目录结构...', 'blue');
  
  requiredStructure.directories.forEach(dir => {
    if (checkDirectoryExists(dir)) {
      log(`  ✅ ${dir}/`, 'green');
      results.directories.passed++;
    } else {
      log(`  ❌ ${dir}/`, 'red');
      results.directories.failed++;
      results.directories.missing.push(dir);
    }
  });
}

function verifyDependencies() {
  log('\n📦 验证依赖包...', 'blue');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    const dependencies = { ...packageJson.dependencies, ...packageJson.devDependencies };
    
    requiredDependencies.forEach(dep => {
      if (dependencies[dep]) {
        log(`  ✅ ${dep} (${dependencies[dep]})`, 'green');
        results.dependencies.passed++;
      } else {
        log(`  ❌ ${dep}`, 'red');
        results.dependencies.failed++;
        results.dependencies.missing.push(dep);
      }
    });
  } catch (error) {
    log(`  ❌ 无法读取package.json: ${error.message}`, 'red');
    results.dependencies.failed = requiredDependencies.length;
  }
}

function verifyConfiguration() {
  log('\n⚙️  验证配置文件...', 'blue');
  
  // 检查TypeScript配置
  try {
    const tsConfig = JSON.parse(fs.readFileSync('tsconfig.json', 'utf8'));
    if (tsConfig.compilerOptions && tsConfig.compilerOptions.experimentalDecorators) {
      log('  ✅ TypeScript配置正确', 'green');
      results.configuration.passed++;
    } else {
      log('  ❌ TypeScript配置缺少装饰器支持', 'red');
      results.configuration.failed++;
      results.configuration.issues.push('TypeScript配置缺少装饰器支持');
    }
  } catch (error) {
    log(`  ❌ TypeScript配置错误: ${error.message}`, 'red');
    results.configuration.failed++;
    results.configuration.issues.push('TypeScript配置错误');
  }
  
  // 检查Docker配置
  if (checkFileExists('Dockerfile') && checkFileExists('docker-compose.yml')) {
    log('  ✅ Docker配置完整', 'green');
    results.configuration.passed++;
  } else {
    log('  ❌ Docker配置不完整', 'red');
    results.configuration.failed++;
    results.configuration.issues.push('Docker配置不完整');
  }
  
  // 检查环境变量示例
  if (checkFileExists('.env.example')) {
    log('  ✅ 环境变量示例文件存在', 'green');
    results.configuration.passed++;
  } else {
    log('  ❌ 缺少环境变量示例文件', 'red');
    results.configuration.failed++;
    results.configuration.issues.push('缺少环境变量示例文件');
  }
}

function generateReport() {
  log('\n📊 验证报告', 'blue');
  log('=' * 50, 'blue');
  
  const totalFiles = requiredStructure.files.length;
  const totalDirs = requiredStructure.directories.length;
  const totalDeps = requiredDependencies.length;
  
  log(`\n文件结构: ${results.files.passed}/${totalFiles} 通过`, 
      results.files.failed === 0 ? 'green' : 'yellow');
  
  log(`目录结构: ${results.directories.passed}/${totalDirs} 通过`, 
      results.directories.failed === 0 ? 'green' : 'yellow');
  
  log(`依赖包: ${results.dependencies.passed}/${totalDeps} 通过`, 
      results.dependencies.failed === 0 ? 'green' : 'yellow');
  
  log(`配置文件: ${results.configuration.passed}/${results.configuration.passed + results.configuration.failed} 通过`, 
      results.configuration.failed === 0 ? 'green' : 'yellow');
  
  // 显示缺失项目
  if (results.files.missing.length > 0) {
    log('\n❌ 缺失文件:', 'red');
    results.files.missing.forEach(file => log(`  - ${file}`, 'red'));
  }
  
  if (results.directories.missing.length > 0) {
    log('\n❌ 缺失目录:', 'red');
    results.directories.missing.forEach(dir => log(`  - ${dir}/`, 'red'));
  }
  
  if (results.dependencies.missing.length > 0) {
    log('\n❌ 缺失依赖:', 'red');
    results.dependencies.missing.forEach(dep => log(`  - ${dep}`, 'red'));
  }
  
  if (results.configuration.issues.length > 0) {
    log('\n❌ 配置问题:', 'red');
    results.configuration.issues.forEach(issue => log(`  - ${issue}`, 'red'));
  }
  
  // 总体评估
  const totalIssues = results.files.failed + results.directories.failed + 
                     results.dependencies.failed + results.configuration.failed;
  
  if (totalIssues === 0) {
    log('\n🎉 项目验证通过！所有必需组件都已就绪。', 'green');
    log('✅ 项目可以正常启动和运行。', 'green');
  } else {
    log(`\n⚠️  发现 ${totalIssues} 个问题需要解决。`, 'yellow');
    log('🔧 请根据上述报告修复问题后重新验证。', 'yellow');
  }
  
  return totalIssues === 0;
}

function main() {
  log('🔍 开始验证边缘AI服务项目完整性...', 'blue');
  
  verifyFiles();
  verifyDirectories();
  verifyDependencies();
  verifyConfiguration();
  
  const isValid = generateReport();
  
  process.exit(isValid ? 0 : 1);
}

// 运行验证
if (require.main === module) {
  main();
}

module.exports = { main };

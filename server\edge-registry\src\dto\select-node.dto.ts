import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsNumber, IsEnum, ValidateNested, Min, Max } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * 负载均衡策略枚举
 */
export enum LoadBalancingStrategy {
  ROUND_ROBIN = 'round_robin',
  LEAST_CONNECTIONS = 'least_connections',
  GEOGRAPHIC_PROXIMITY = 'geographic_proximity',
  RESOURCE_BASED = 'resource_based',
  HYBRID = 'hybrid',
}

/**
 * 客户端位置信息DTO
 */
export class ClientLocationDto {
  @ApiPropertyOptional({ description: '客户端纬度', example: 39.9042 })
  @IsNumber()
  @Min(-90)
  @Max(90)
  latitude: number;

  @ApiPropertyOptional({ description: '客户端经度', example: 116.4074 })
  @IsNumber()
  @Min(-180)
  @Max(180)
  longitude: number;
}

/**
 * 选择最优节点DTO
 */
export class SelectOptimalNodeDto {
  @ApiPropertyOptional({ 
    description: '负载均衡策略', 
    enum: LoadBalancingStrategy,
    example: LoadBalancingStrategy.HYBRID 
  })
  @IsOptional()
  @IsEnum(LoadBalancingStrategy)
  strategy?: LoadBalancingStrategy;

  @ApiPropertyOptional({ description: '指定区域', example: 'beijing-zone-1' })
  @IsOptional()
  @IsString()
  region?: string;

  @ApiPropertyOptional({ description: '客户端位置信息', type: ClientLocationDto })
  @IsOptional()
  @ValidateNested()
  @Type(() => ClientLocationDto)
  clientLocation?: ClientLocationDto;

  @ApiPropertyOptional({ description: '最小资源要求', example: { cpu: '500m', memory: '1Gi' } })
  @IsOptional()
  minResources?: {
    cpu?: string;
    memory?: string;
    storage?: string;
  };

  @ApiPropertyOptional({ description: '必需功能列表', example: ['webrtc', 'ai-inference'] })
  @IsOptional()
  @IsString({ each: true })
  requiredFeatures?: string[];
}

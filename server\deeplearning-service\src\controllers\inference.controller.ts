/**
 * 推理控制器
 * 
 * 处理模型推理相关的HTTP请求
 */

import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpException,
  Logger,
  UseInterceptors,
  UploadedFile,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiTags, ApiOperation, ApiResponse, ApiConsumes, ApiBody } from '@nestjs/swagger';
import { ModelInferenceService } from '../services/model-inference.service';
import {
  SubmitInferenceDto,
  InferenceResultDto,
  InferenceStatusDto,
  BatchInferenceDto,
} from '../dto/inference.dto';

/**
 * 推理控制器
 */
@ApiTags('inference')
@Controller('inference')
export class InferenceController {
  private readonly logger = new Logger(InferenceController.name);

  constructor(
    private readonly inferenceService: ModelInferenceService,
  ) {}

  /**
   * 提交推理请求
   */
  @Post('submit')
  @ApiOperation({ summary: '提交推理请求', description: '向指定模型提交推理请求' })
  @ApiResponse({ status: 201, description: '推理请求已提交', type: String })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 404, description: '模型不存在' })
  @ApiResponse({ status: 500, description: '服务器内部错误' })
  async submitInference(@Body() submitDto: SubmitInferenceDto): Promise<{ requestId: string; queuePosition?: number }> {
    try {
      this.logger.log(`提交推理请求: 模型=${submitDto.modelId}, 用户=${submitDto.userId}`);

      const requestId = await this.inferenceService.submitInference({
        id: `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        modelId: submitDto.modelId,
        input: submitDto.input,
        priority: submitDto.priority || 5,
        timeout: submitDto.timeout || 30000,
        userId: submitDto.userId,
        sessionId: submitDto.sessionId,
        metadata: submitDto.metadata || {},
        timestamp: Date.now(),
        status: 'queued',
      });

      return { requestId };

    } catch (error) {
      this.logger.error('提交推理请求失败:', error);

      const errorMessage = error instanceof Error ? error.message : String(error);

      if (errorMessage.includes('不存在')) {
        throw new HttpException(errorMessage, HttpStatus.NOT_FOUND);
      } else if (errorMessage.includes('队列已满') || errorMessage.includes('状态异常')) {
        throw new HttpException(errorMessage, HttpStatus.BAD_REQUEST);
      }

      throw new HttpException('提交推理请求失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 批量提交推理请求
   */
  @Post('batch')
  @ApiOperation({ summary: '批量提交推理请求', description: '批量向指定模型提交多个推理请求' })
  @ApiResponse({ status: 201, description: '批量推理请求已提交' })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  async submitBatchInference(@Body() batchDto: BatchInferenceDto): Promise<{ requestIds: string[]; totalCount: number }> {
    try {
      this.logger.log(`批量提交推理请求: 模型=${batchDto.modelId}, 数量=${batchDto.inputs.length}`);

      const requestIds: string[] = [];

      for (let i = 0; i < batchDto.inputs.length; i++) {
        const input = batchDto.inputs[i];
        const requestId = await this.inferenceService.submitInference({
          id: `batch_${Date.now()}_${i}_${Math.random().toString(36).substring(2, 11)}`,
          modelId: batchDto.modelId,
          input,
          priority: batchDto.priority || 5,
          timeout: batchDto.timeout || 30000,
          userId: batchDto.userId,
          sessionId: batchDto.sessionId,
          metadata: { ...batchDto.metadata, batchIndex: i },
          timestamp: Date.now(),
          status: 'queued',
        });

        requestIds.push(requestId);
      }

      return {
        requestIds,
        totalCount: requestIds.length,
      };

    } catch (error) {
      this.logger.error('批量提交推理请求失败:', error);
      throw new HttpException('批量提交推理请求失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 上传文件并推理
   */
  @Post('upload/:modelId')
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({ summary: '上传文件并推理', description: '上传文件并使用指定模型进行推理' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: '上传的文件',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        userId: {
          type: 'string',
        },
        priority: {
          type: 'number',
        },
      },
    },
  })
  async uploadAndInfer(
    @Param('modelId') modelId: string,
    @UploadedFile() file: Express.Multer.File,
    @Body('userId') userId: string,
    @Body('priority') priority?: number,
  ): Promise<{ requestId: string }> {
    try {
      if (!file) {
        throw new HttpException('未上传文件', HttpStatus.BAD_REQUEST);
      }

      this.logger.log(`文件上传推理: 模型=${modelId}, 文件=${file.originalname}, 大小=${file.size}`);

      // 处理文件数据
      const input = {
        type: 'file',
        filename: file.originalname,
        mimetype: file.mimetype,
        size: file.size,
        data: file.buffer.toString('base64'), // 转换为base64
      };

      const requestId = await this.inferenceService.submitInference({
        id: `upload_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        modelId,
        input,
        priority: priority || 5,
        timeout: 60000, // 文件推理超时时间更长
        userId,
        metadata: {
          uploadedFile: true,
          originalName: file.originalname,
          fileSize: file.size,
        },
        timestamp: Date.now(),
        status: 'queued',
      });

      return { requestId };

    } catch (error) {
      this.logger.error('文件上传推理失败:', error);
      throw new HttpException('文件上传推理失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取推理结果
   */
  @Get('result/:requestId')
  @ApiOperation({ summary: '获取推理结果', description: '根据请求ID获取推理结果' })
  @ApiResponse({ status: 200, description: '推理结果', type: InferenceResultDto })
  @ApiResponse({ status: 404, description: '请求不存在或结果未就绪' })
  async getInferenceResult(@Param('requestId') requestId: string): Promise<InferenceResultDto> {
    try {
      const result = await this.inferenceService.getInferenceResult(requestId);

      if (!result) {
        throw new HttpException('推理结果未就绪或请求不存在', HttpStatus.NOT_FOUND);
      }

      return {
        requestId: result.requestId,
        modelId: result.modelId,
        output: result.output,
        confidence: result.confidence,
        processingTime: result.processingTime,
        queueTime: result.queueTime,
        status: result.status,
        error: result.error,
        metadata: result.metadata,
        timestamp: result.timestamp,
      };

    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error('获取推理结果失败:', error);
      throw new HttpException('获取推理结果失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取推理状态
   */
  @Get('status/:requestId')
  @ApiOperation({ summary: '获取推理状态', description: '根据请求ID获取推理状态' })
  @ApiResponse({ status: 200, description: '推理状态', type: InferenceStatusDto })
  @ApiResponse({ status: 404, description: '请求不存在' })
  async getInferenceStatus(@Param('requestId') requestId: string): Promise<InferenceStatusDto> {
    try {
      // 这里需要实现获取推理状态的逻辑
      // 暂时返回模拟数据
      return {
        requestId,
        status: 'processing',
        queuePosition: 3,
        estimatedTime: 15000,
        progress: 0.6,
      };

    } catch (error) {
      this.logger.error('获取推理状态失败:', error);
      throw new HttpException('获取推理状态失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 取消推理请求
   */
  @Post('cancel/:requestId')
  @ApiOperation({ summary: '取消推理请求', description: '取消指定的推理请求' })
  @ApiResponse({ status: 200, description: '推理请求已取消' })
  @ApiResponse({ status: 404, description: '请求不存在' })
  @ApiResponse({ status: 400, description: '请求无法取消' })
  async cancelInference(@Param('requestId') requestId: string): Promise<{ message: string }> {
    try {
      // 这里需要实现取消推理的逻辑
      this.logger.log(`取消推理请求: ${requestId}`);

      return { message: '推理请求已取消' };

    } catch (error) {
      this.logger.error('取消推理请求失败:', error);
      throw new HttpException('取消推理请求失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * 获取用户推理历史
   */
  @Get('history')
  @ApiOperation({ summary: '获取推理历史', description: '获取用户的推理历史记录' })
  @ApiResponse({ status: 200, description: '推理历史记录' })
  async getInferenceHistory(
    @Query('userId') userId: string,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
    @Query('modelId') modelId?: string,
  ): Promise<{ history: any[]; total: number }> {
    try {
      if (!userId) {
        throw new HttpException('用户ID不能为空', HttpStatus.BAD_REQUEST);
      }

      // 这里需要实现获取推理历史的逻辑
      // 暂时返回模拟数据
      return {
        history: [],
        total: 0,
      };

    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }

      this.logger.error('获取推理历史失败:', error);
      throw new HttpException('获取推理历史失败', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}

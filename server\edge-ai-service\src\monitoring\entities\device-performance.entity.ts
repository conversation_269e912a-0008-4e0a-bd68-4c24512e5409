import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  Index,
} from 'typeorm';

@Entity('device_performance')
@Index(['deviceId'])
@Index(['timestamp'])
@Index(['deviceId', 'timestamp'])
export class DevicePerformance {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  deviceId: string;

  @Column({ type: 'decimal', precision: 5, scale: 2 })
  cpuUsage: number; // %

  @Column({ type: 'decimal', precision: 5, scale: 2 })
  memoryUsage: number; // %

  @Column({ type: 'decimal', precision: 5, scale: 2 })
  storageUsage: number; // %

  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  gpuUsage: number; // %

  @Column({ type: 'decimal', precision: 5, scale: 2 })
  temperature: number; // °C

  @Column({ type: 'decimal', precision: 8, scale: 2 })
  powerConsumption: number; // watts

  @Column({ type: 'decimal', precision: 8, scale: 2 })
  networkLatency: number; // ms

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  uptime: number; // hours

  @Column({ type: 'int', nullable: true })
  activeInferences: number;

  @Column({ type: 'int', nullable: true })
  queuedInferences: number;

  @Column({ type: 'decimal', precision: 8, scale: 2, nullable: true })
  throughput: number; // inferences/second

  @Column({ type: 'decimal', precision: 8, scale: 2, nullable: true })
  averageInferenceTime: number; // ms

  @Column({ type: 'decimal', precision: 5, scale: 4, nullable: true })
  errorRate: number; // %

  @Column({ type: 'json', nullable: true })
  modelPerformance: {
    [modelId: string]: {
      inferenceCount: number;
      averageTime: number;
      errorCount: number;
      lastUsed: Date;
    };
  };

  @Column({ type: 'json', nullable: true })
  networkInfo: {
    bandwidth: number; // Mbps
    packetLoss: number; // %
    jitter: number; // ms
    connectionQuality: 'excellent' | 'good' | 'fair' | 'poor';
  };

  @Column({ type: 'json', nullable: true })
  alerts: {
    type: 'warning' | 'error' | 'critical';
    message: string;
    threshold: number;
    currentValue: number;
  }[];

  @CreateDateColumn()
  timestamp: Date;
}

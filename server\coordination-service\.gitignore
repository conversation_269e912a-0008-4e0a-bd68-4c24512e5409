# 群体协调服务 Git 忽略文件

# ================================
# Node.js
# ================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# ================================
# 构建输出
# ================================
dist/
build/
*.tsbuildinfo

# ================================
# 环境配置
# ================================
.env
.env.local
.env.development
.env.test
.env.production

# ================================
# 日志文件
# ================================
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# ================================
# 运行时数据
# ================================
pids/
*.pid
*.seed
*.pid.lock

# ================================
# 测试覆盖率
# ================================
coverage/
*.lcov
.nyc_output/

# ================================
# IDE 配置
# ================================
.vscode/
.idea/
*.swp
*.swo
*~

# ================================
# 操作系统文件
# ================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ================================
# 临时文件
# ================================
tmp/
temp/
.tmp/
.temp/

# ================================
# 缓存
# ================================
.cache/
.parcel-cache/
.eslintcache

# ================================
# 数据文件
# ================================
*.sqlite
*.db
data/

# ================================
# 安全文件
# ================================
*.pem
*.key
*.crt
*.p12
secrets/

# ================================
# Docker
# ================================
.docker/

# ================================
# 监控数据
# ================================
monitoring/data/
grafana/data/
prometheus/data/

# ================================
# 备份文件
# ================================
*.bak
*.backup

# ================================
# 其他
# ================================
.next/
.nuxt/
.vuepress/dist/
.serverless/
.fusebox/
.dynamodb/
.tern-port

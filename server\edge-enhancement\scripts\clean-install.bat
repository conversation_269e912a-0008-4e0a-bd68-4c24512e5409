@echo off
echo 正在清理node_modules目录...

REM 删除node_modules目录
if exist node_modules (
    echo 删除现有的node_modules目录...
    rmdir /s /q node_modules 2>nul
    if exist node_modules (
        echo 使用PowerShell强制删除...
        powershell -Command "Remove-Item -Path 'node_modules' -Recurse -Force -ErrorAction SilentlyContinue"
    )
)

REM 删除package-lock.json
if exist package-lock.json (
    echo 删除package-lock.json...
    del package-lock.json
)

REM 清理npm缓存
echo 清理npm缓存...
npm cache clean --force

REM 设置npm配置以避免权限问题
echo 配置npm设置...
npm config set fund false
npm config set audit false

REM 安装依赖
echo 开始安装依赖...
npm install --no-optional --legacy-peer-deps

echo 安装完成！
pause

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsObject, IsOptional, IsEnum, IsNumber, IsArray } from 'class-validator';

export class OptimizationRequestDto {
  @ApiProperty({ description: '决策上下文' })
  @IsObject()
  context: {
    currentLoad?: any;
    resourceUsage?: any;
    performanceMetrics?: any;
    environmentalFactors?: any;
    businessObjectives?: any;
  };

  @ApiProperty({ description: '约束条件' })
  @IsObject()
  constraints: {
    resourceLimits?: any;
    performanceRequirements?: any;
    costConstraints?: any;
    timeConstraints?: any;
    qualityRequirements?: any;
  };

  @ApiPropertyOptional({ 
    description: '优化目标',
    enum: ['performance', 'cost', 'energy', 'latency', 'throughput', 'balanced'],
    example: 'balanced'
  })
  @IsOptional()
  @IsEnum(['performance', 'cost', 'energy', 'latency', 'throughput', 'balanced'])
  objective?: 'performance' | 'cost' | 'energy' | 'latency' | 'throughput' | 'balanced';

  @ApiPropertyOptional({ 
    description: '优化算法',
    enum: ['genetic', 'simulated_annealing', 'particle_swarm', 'gradient_descent', 'reinforcement_learning'],
    example: 'genetic'
  })
  @IsOptional()
  @IsEnum(['genetic', 'simulated_annealing', 'particle_swarm', 'gradient_descent', 'reinforcement_learning'])
  algorithm?: 'genetic' | 'simulated_annealing' | 'particle_swarm' | 'gradient_descent' | 'reinforcement_learning';

  @ApiPropertyOptional({ description: '优化时间限制 (ms)', example: 10000 })
  @IsOptional()
  @IsNumber()
  timeLimit?: number;

  @ApiPropertyOptional({ description: '目标设备ID列表', type: [String] })
  @IsOptional()
  @IsArray()
  targetDevices?: string[];

  @ApiPropertyOptional({ description: '权重配置' })
  @IsOptional()
  @IsObject()
  weights?: {
    performance?: number;
    cost?: number;
    energy?: number;
    reliability?: number;
  };

  @ApiPropertyOptional({ description: '历史数据窗口 (分钟)', example: 60 })
  @IsOptional()
  @IsNumber()
  historicalWindow?: number;

  @ApiPropertyOptional({ description: '预测时间窗口 (分钟)', example: 30 })
  @IsOptional()
  @IsNumber()
  forecastWindow?: number;
}

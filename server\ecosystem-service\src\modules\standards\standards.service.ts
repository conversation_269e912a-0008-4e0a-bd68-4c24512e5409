import { Injectable, Logger, NotFoundException } from '@nestjs/common';

@Injectable()
export class StandardsService {
  private readonly logger = new Logger(StandardsService.name);
  private standards: Map<string, any> = new Map();
  private certifications: Map<string, any> = new Map();
  private complianceRecords: Map<string, any[]> = new Map();

  async getStandards(filters: any = {}) {
    const allStandards = Array.from(this.standards.values());
    
    let filteredStandards = allStandards;
    
    if (filters.organization) {
      filteredStandards = filteredStandards.filter(std => std.organization === filters.organization);
    }
    
    if (filters.status) {
      filteredStandards = filteredStandards.filter(std => std.status === filters.status);
    }
    
    return {
      standards: filteredStandards,
      total: filteredStandards.length,
      filters
    };
  }

  async getStandard(id: string) {
    const standard = this.standards.get(id);
    if (!standard) {
      throw new NotFoundException(`标准 ${id} 不存在`);
    }
    return standard;
  }

  async createStandard(standard: any) {
    const standardId = `std_${Date.now()}`;
    
    const newStandard = {
      ...standard,
      standardId,
      status: 'draft',
      published_at: new Date(),
      version: standard.version || '1.0',
      organization: standard.organization || 'DL Engine Consortium'
    };
    
    this.standards.set(standardId, newStandard);
    
    this.logger.log(`标准创建: ${standardId} - ${standard.name}`);
    
    return {
      standardId,
      status: 'draft',
      message: '标准创建成功'
    };
  }

  async updateStandard(id: string, updateData: any) {
    const standard = this.standards.get(id);
    if (!standard) {
      throw new NotFoundException(`标准 ${id} 不存在`);
    }
    
    const updatedStandard = {
      ...standard,
      ...updateData,
      updatedAt: new Date()
    };
    
    this.standards.set(id, updatedStandard);
    
    this.logger.log(`标准更新: ${id}`);
    
    return updatedStandard;
  }

  async deleteStandard(id: string) {
    const standard = this.standards.get(id);
    if (!standard) {
      throw new NotFoundException(`标准 ${id} 不存在`);
    }
    
    this.standards.delete(id);
    this.complianceRecords.delete(id);
    
    this.logger.log(`标准删除: ${id}`);
    
    return {
      message: '标准已删除',
      deletedId: id
    };
  }

  async getStandardCompliance(id: string) {
    const standard = this.standards.get(id);
    if (!standard) {
      throw new NotFoundException(`标准 ${id} 不存在`);
    }
    
    const complianceRecords = this.complianceRecords.get(id) || [];
    
    return {
      standardId: id,
      standardName: standard.name,
      complianceLevel: standard.compliance_levels || [],
      complianceRecords,
      overallCompliance: this.calculateOverallCompliance(complianceRecords),
      lastAssessment: complianceRecords.length > 0 
        ? complianceRecords[complianceRecords.length - 1].assessmentDate 
        : null
    };
  }

  async applyCertification(id: string, application: any) {
    const standard = this.standards.get(id);
    if (!standard) {
      throw new NotFoundException(`标准 ${id} 不存在`);
    }
    
    const certificationId = `cert_${Date.now()}`;
    const newCertification = {
      ...application,
      certificationId,
      standardId: id,
      status: 'pending',
      appliedAt: new Date(),
      reviewedAt: null,
      issuedAt: null
    };
    
    this.certifications.set(certificationId, newCertification);
    
    this.logger.log(`认证申请: ${certificationId} - 标准: ${id}`);
    
    return {
      certificationId,
      status: 'pending',
      message: '认证申请已提交，等待审核',
      estimatedDuration: standard.certification_process?.duration || 30
    };
  }

  async getStandardRequirements(id: string) {
    const standard = this.standards.get(id);
    if (!standard) {
      throw new NotFoundException(`标准 ${id} 不存在`);
    }
    
    return {
      standardId: id,
      standardName: standard.name,
      requirements: standard.requirements || [],
      complianceLevels: standard.compliance_levels || [],
      certificationProcess: standard.certification_process || {
        steps: [],
        duration: 0,
        cost: 0,
        validity_period: 0,
        renewal_requirements: []
      }
    };
  }

  private calculateOverallCompliance(complianceRecords: any[]): number {
    if (complianceRecords.length === 0) return 0;
    
    const latestRecord = complianceRecords[complianceRecords.length - 1];
    return latestRecord.complianceScore || 0;
  }
}

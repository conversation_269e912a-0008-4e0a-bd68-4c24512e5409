#!/bin/bash

# 深度学习推理服务测试脚本

echo "🧪 深度学习推理服务测试套件"
echo "================================"

# 解析命令行参数
TEST_TYPE=${1:-"unit"}

case $TEST_TYPE in
    "unit")
        echo "🔬 运行单元测试..."
        npm run test
        ;;
    "e2e")
        echo "🌐 运行端到端测试..."
        npm run test:e2e
        ;;
    "cov")
        echo "📊 运行测试覆盖率..."
        npm run test:cov
        ;;
    "watch")
        echo "👀 运行监听模式测试..."
        npm run test:watch
        ;;
    "all")
        echo "🎯 运行所有测试..."
        
        echo ""
        echo "1️⃣ 单元测试"
        echo "----------"
        npm run test
        UNIT_EXIT_CODE=$?
        
        echo ""
        echo "2️⃣ 端到端测试"
        echo "----------"
        npm run test:e2e
        E2E_EXIT_CODE=$?
        
        echo ""
        echo "3️⃣ 测试覆盖率"
        echo "----------"
        npm run test:cov
        COV_EXIT_CODE=$?
        
        echo ""
        echo "📋 测试结果汇总"
        echo "=============="
        
        if [ $UNIT_EXIT_CODE -eq 0 ]; then
            echo "✅ 单元测试: 通过"
        else
            echo "❌ 单元测试: 失败"
        fi
        
        if [ $E2E_EXIT_CODE -eq 0 ]; then
            echo "✅ 端到端测试: 通过"
        else
            echo "❌ 端到端测试: 失败"
        fi
        
        if [ $COV_EXIT_CODE -eq 0 ]; then
            echo "✅ 测试覆盖率: 通过"
        else
            echo "❌ 测试覆盖率: 失败"
        fi
        
        # 计算总体结果
        TOTAL_EXIT_CODE=$((UNIT_EXIT_CODE + E2E_EXIT_CODE + COV_EXIT_CODE))
        
        if [ $TOTAL_EXIT_CODE -eq 0 ]; then
            echo ""
            echo "🎉 所有测试通过！"
            exit 0
        else
            echo ""
            echo "💥 部分测试失败"
            exit 1
        fi
        ;;
    "lint")
        echo "🔍 运行代码检查..."
        npm run lint
        ;;
    "format")
        echo "💅 运行代码格式化..."
        npm run format
        ;;
    "check")
        echo "🔍 运行完整代码质量检查..."
        
        echo ""
        echo "1️⃣ 代码格式检查"
        echo "----------"
        npm run format:check
        FORMAT_EXIT_CODE=$?
        
        echo ""
        echo "2️⃣ 代码规范检查"
        echo "----------"
        npm run lint:check
        LINT_EXIT_CODE=$?
        
        echo ""
        echo "3️⃣ 单元测试"
        echo "----------"
        npm run test
        TEST_EXIT_CODE=$?
        
        echo ""
        echo "📋 检查结果汇总"
        echo "=============="
        
        if [ $FORMAT_EXIT_CODE -eq 0 ]; then
            echo "✅ 代码格式: 通过"
        else
            echo "❌ 代码格式: 失败"
        fi
        
        if [ $LINT_EXIT_CODE -eq 0 ]; then
            echo "✅ 代码规范: 通过"
        else
            echo "❌ 代码规范: 失败"
        fi
        
        if [ $TEST_EXIT_CODE -eq 0 ]; then
            echo "✅ 单元测试: 通过"
        else
            echo "❌ 单元测试: 失败"
        fi
        
        # 计算总体结果
        TOTAL_EXIT_CODE=$((FORMAT_EXIT_CODE + LINT_EXIT_CODE + TEST_EXIT_CODE))
        
        if [ $TOTAL_EXIT_CODE -eq 0 ]; then
            echo ""
            echo "🎉 代码质量检查通过！"
            exit 0
        else
            echo ""
            echo "💥 代码质量检查失败"
            exit 1
        fi
        ;;
    *)
        echo "❌ 未知的测试类型: $TEST_TYPE"
        echo ""
        echo "用法: $0 [test_type]"
        echo ""
        echo "可用的测试类型:"
        echo "  unit    - 单元测试"
        echo "  e2e     - 端到端测试"
        echo "  cov     - 测试覆盖率"
        echo "  watch   - 监听模式测试"
        echo "  all     - 所有测试"
        echo "  lint    - 代码检查"
        echo "  format  - 代码格式化"
        echo "  check   - 完整代码质量检查"
        echo ""
        echo "示例:"
        echo "  $0 unit     # 运行单元测试"
        echo "  $0 all      # 运行所有测试"
        echo "  $0 check    # 运行代码质量检查"
        exit 1
        ;;
esac

import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';
import { TerminusModule } from '@nestjs/terminus';

// 业务模块
import { EdgeRouterModule } from './edge-router/edge-router.module';
import { HealthModule } from './health/health.module';

// 配置
import { redisConfig } from './config/redis.config';
import { routingConfig } from './config/routing.config';

/**
 * 边缘路由服务主应用模块
 */
@Module({
  imports: [
    // 配置模块
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
      load: [redisConfig, routingConfig],
    }),

    // 事件发射器模块
    EventEmitterModule.forRoot({
      wildcard: true,
      delimiter: '.',
      newListener: false,
      removeListener: false,
      maxListeners: 20,
      verboseMemoryLeak: false,
      ignoreErrors: false,
    }),

    // 定时任务模块
    ScheduleModule.forRoot(),

    // 健康检查模块
    TerminusModule,

    // 业务模块
    EdgeRouterModule,
    HealthModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}

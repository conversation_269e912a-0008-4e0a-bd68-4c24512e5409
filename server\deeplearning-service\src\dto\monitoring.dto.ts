/**
 * 监控相关数据传输对象
 */

import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * 推理统计DTO
 */
export class InferenceStatsDto {
  @ApiProperty({ description: '总请求数', example: 1000 })
  totalRequests: number;

  @ApiProperty({ description: '活跃请求数', example: 5 })
  activeRequests: number;

  @ApiProperty({ description: '队列中请求数', example: 10 })
  queuedRequests: number;

  @ApiProperty({ description: '已完成请求数', example: 950 })
  completedRequests: number;

  @ApiProperty({ description: '失败请求数', example: 45 })
  failedRequests: number;

  @ApiProperty({ description: '平均延迟 (毫秒)', example: 125.5 })
  averageLatency: number;

  @ApiProperty({ description: '吞吐量 (请求/分钟)', example: 120.5 })
  throughput: number;

  @ApiProperty({ description: '错误率', example: 0.045 })
  errorRate: number;

  @ApiProperty({ description: '模型利用率', example: { model_001: 500, model_002: 300 } })
  modelUtilization: { [modelId: string]: number };

  @ApiProperty({ 
    description: '资源使用情况', 
    example: { cpuUsage: 0.65, memoryUsage: 512.5, gpuUsage: 0.8 } 
  })
  resourceUsage: {
    cpuUsage: number;
    memoryUsage: number;
    gpuUsage?: number;
  };
}

/**
 * 系统指标DTO
 */
export class SystemMetricsDto {
  @ApiProperty({ description: '时间戳', example: 1640995200000 })
  timestamp: number;

  @ApiProperty({ 
    description: 'CPU指标', 
    example: { usage: 0.65, cores: 8, loadAverage: [1.2, 1.5, 1.8] } 
  })
  cpu: {
    usage: number;
    cores: number;
    loadAverage: number[];
  };

  @ApiProperty({ 
    description: '内存指标', 
    example: { total: 16777216, used: 8388608, free: 8388608, usage: 0.5 } 
  })
  memory: {
    total: number;
    used: number;
    free: number;
    usage: number;
  };

  @ApiProperty({ 
    description: '磁盘指标', 
    example: { total: 1073741824, used: 536870912, free: 536870912, usage: 0.5 } 
  })
  disk: {
    total: number;
    used: number;
    free: number;
    usage: number;
  };

  @ApiProperty({ 
    description: '网络指标', 
    example: { bytesIn: 1048576, bytesOut: 2097152, packetsIn: 1000, packetsOut: 1500 } 
  })
  network: {
    bytesIn: number;
    bytesOut: number;
    packetsIn: number;
    packetsOut: number;
  };

  @ApiPropertyOptional({ 
    description: 'GPU指标', 
    example: { usage: 0.8, memory: 0.6, temperature: 75 } 
  })
  gpu?: {
    usage: number;
    memory: number;
    temperature: number;
  };
}

/**
 * 模型利用率DTO
 */
export class ModelUtilizationDto {
  @ApiProperty({ description: '模型ID', example: 'model_001' })
  modelId: string;

  @ApiProperty({ description: '模型名称', example: 'Text Classifier' })
  modelName: string;

  @ApiProperty({ description: '请求数量', example: 500 })
  requestCount: number;

  @ApiProperty({ description: '平均延迟 (毫秒)', example: 125.5 })
  averageLatency: number;

  @ApiProperty({ description: '错误率', example: 0.05 })
  errorRate: number;

  @ApiProperty({ description: '吞吐量 (请求/分钟)', example: 120.5 })
  throughput: number;

  @ApiProperty({ description: '内存使用 (MB)', example: 256.5 })
  memoryUsage: number;

  @ApiProperty({ description: '最后使用时间', example: 1640995200000 })
  lastUsed: number;

  @ApiProperty({ description: '利用率', example: 0.75 })
  utilizationRate: number;
}

/**
 * 性能报告DTO
 */
export class PerformanceReportDto {
  @ApiProperty({ description: '报告ID', example: 'report_123456789' })
  reportId: string;

  @ApiProperty({ 
    description: '时间范围', 
    example: { start: 1640995200000, end: 1640998800000 } 
  })
  timeRange: {
    start: number;
    end: number;
  };

  @ApiProperty({ 
    description: '摘要信息', 
    example: { 
      totalRequests: 1000, 
      successRate: 0.95, 
      averageLatency: 125.5, 
      peakThroughput: 200, 
      errorCount: 50 
    } 
  })
  summary: {
    totalRequests: number;
    successRate: number;
    averageLatency: number;
    peakThroughput: number;
    errorCount: number;
  };

  @ApiProperty({ description: '模型性能数据', example: [] })
  modelPerformance: Array<{
    modelId: string;
    modelName: string;
    requests: number;
    averageLatency: number;
    errorRate: number;
    throughput: number;
  }>;

  @ApiProperty({ 
    description: '系统健康状况', 
    example: { 
      cpuUsage: 0.65, 
      memoryUsage: 0.75, 
      diskUsage: 0.5, 
      networkTraffic: 1048576 
    } 
  })
  systemHealth: {
    cpuUsage: number;
    memoryUsage: number;
    diskUsage: number;
    networkTraffic: number;
  };

  @ApiProperty({ description: '告警信息', example: [] })
  alerts: Array<{
    id: string;
    type: string;
    severity: string;
    message: string;
    timestamp: number;
  }>;

  @ApiProperty({ description: '建议', example: ['增加内存容量', '优化模型加载'] })
  recommendations: string[];

  @ApiProperty({ description: '生成时间', example: 1640995200000 })
  generatedAt: number;
}

/**
 * 告警DTO
 */
export class AlertDto {
  @ApiProperty({ description: '告警ID', example: 'alert_123456789' })
  id: string;

  @ApiProperty({ description: '告警类型', example: 'high_latency' })
  type: string;

  @ApiProperty({ description: '严重程度', example: 'warning', enum: ['info', 'warning', 'error', 'critical'] })
  severity: 'info' | 'warning' | 'error' | 'critical';

  @ApiProperty({ description: '告警消息', example: '模型推理延迟过高' })
  message: string;

  @ApiProperty({ description: '告警时间', example: 1640995200000 })
  timestamp: number;

  @ApiProperty({ description: '是否已解决', example: false })
  resolved: boolean;

  @ApiPropertyOptional({ description: '相关模型ID', example: 'model_001' })
  modelId?: string;

  @ApiPropertyOptional({ description: '阈值', example: 1000 })
  threshold?: number;

  @ApiPropertyOptional({ description: '当前值', example: 1500 })
  currentValue?: number;

  @ApiPropertyOptional({ description: '解决时间', example: 1640995260000 })
  resolvedAt?: number;
}

/**
 * 队列状态DTO
 */
export class QueueStatusDto {
  @ApiProperty({ description: '总任务数', example: 1000 })
  totalJobs: number;

  @ApiProperty({ description: '等待中任务数', example: 10 })
  waitingJobs: number;

  @ApiProperty({ description: '活跃任务数', example: 5 })
  activeJobs: number;

  @ApiProperty({ description: '已完成任务数', example: 950 })
  completedJobs: number;

  @ApiProperty({ description: '失败任务数', example: 35 })
  failedJobs: number;

  @ApiProperty({ description: '平均等待时间 (毫秒)', example: 2500 })
  averageWaitTime: number;

  @ApiProperty({ description: '队列健康状态', example: 'healthy' })
  healthStatus: 'healthy' | 'degraded' | 'unhealthy';

  @ApiProperty({ description: '最后更新时间', example: 1640995200000 })
  lastUpdated: number;
}

/**
 * 实时指标DTO
 */
export class RealtimeMetricsDto {
  @ApiProperty({ description: '时间戳', example: 1640995200000 })
  timestamp: number;

  @ApiProperty({ description: '活跃请求数', example: 5 })
  activeRequests: number;

  @ApiProperty({ description: '队列大小', example: 10 })
  queueSize: number;

  @ApiProperty({ description: '吞吐量 (请求/分钟)', example: 120.5 })
  throughput: number;

  @ApiProperty({ description: '错误率', example: 0.05 })
  errorRate: number;

  @ApiProperty({ description: 'CPU使用率', example: 0.65 })
  cpuUsage: number;

  @ApiProperty({ description: '内存使用率', example: 0.75 })
  memoryUsage: number;

  @ApiPropertyOptional({ description: 'GPU使用率', example: 0.8 })
  gpuUsage?: number;
}

/**
 * 历史指标查询DTO
 */
export class HistoricalMetricsQueryDto {
  @ApiProperty({ description: '开始时间', example: 1640995200000 })
  startTime: number;

  @ApiProperty({ description: '结束时间', example: 1640998800000 })
  endTime: number;

  @ApiPropertyOptional({ description: '指标类型', example: 'latency' })
  metricType?: string;

  @ApiPropertyOptional({ description: '聚合间隔 (秒)', example: 300 })
  interval?: number;

  @ApiPropertyOptional({ description: '模型ID过滤', example: 'model_001' })
  modelId?: string;
}

/**
 * 历史指标DTO
 */
export class HistoricalMetricsDto {
  @ApiProperty({ description: '指标类型', example: 'latency' })
  metricType: string;

  @ApiProperty({ description: '时间序列数据', example: [] })
  timeSeries: Array<{
    timestamp: number;
    value: number;
  }>;

  @ApiProperty({ description: '聚合统计', example: { avg: 125, min: 50, max: 500 } })
  aggregates: {
    avg: number;
    min: number;
    max: number;
    count: number;
  };

  @ApiProperty({ description: '时间范围', example: { start: 1640995200000, end: 1640998800000 } })
  timeRange: {
    start: number;
    end: number;
  };
}

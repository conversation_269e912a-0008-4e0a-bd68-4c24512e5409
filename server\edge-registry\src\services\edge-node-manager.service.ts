import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';

/**
 * 边缘节点状态枚举
 */
export enum EdgeNodeStatus {
  ONLINE = 'online',
  OFFLINE = 'offline',
  MAINTENANCE = 'maintenance',
  OVERLOADED = 'overloaded',
  UNHEALTHY = 'unhealthy',
}

/**
 * 边缘节点接口
 */
export interface EdgeNode {
  nodeId: string;
  region: string;
  endpoint: string;
  status: EdgeNodeStatus;
  capabilities: {
    maxUsers: number;
    supportedFeatures: string[];
    resources: {
      cpu: string;
      memory: string;
      storage: string;
    };
  };
  metrics: {
    currentUsers: number;
    cpuUsage: number;
    memoryUsage: number;
    networkLatency: number;
    uptime: number;
  };
  location: {
    latitude: number;
    longitude: number;
    city: string;
    country: string;
  };
  lastHeartbeat: Date;
  registeredAt: Date;
  version: string;
  metadata?: Record<string, any>;
}

/**
 * 负载均衡策略枚举
 */
export enum LoadBalancingStrategy {
  ROUND_ROBIN = 'round_robin',
  LEAST_CONNECTIONS = 'least_connections',
  GEOGRAPHIC_PROXIMITY = 'geographic_proximity',
  RESOURCE_BASED = 'resource_based',
  HYBRID = 'hybrid',
}

/**
 * 边缘节点管理服务
 * 负责边缘节点的注册、发现、健康监控和负载均衡
 */
@Injectable()
export class EdgeNodeManagerService implements OnModuleInit {
  private readonly logger = new Logger(EdgeNodeManagerService.name);
  private readonly edgeNodes: Map<string, EdgeNode> = new Map();
  private readonly regionNodes: Map<string, Set<string>> = new Map();
  private roundRobinCounters: Map<string, number> = new Map();
  
  // 配置参数
  private readonly heartbeatTimeout: number;
  private readonly healthCheckInterval: number;
  private readonly maxUnhealthyNodes: number;

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.heartbeatTimeout = this.configService.get<number>('EDGE_HEARTBEAT_TIMEOUT', 90000);
    this.healthCheckInterval = this.configService.get<number>('EDGE_HEALTH_CHECK_INTERVAL', 30000);
    this.maxUnhealthyNodes = this.configService.get<number>('MAX_UNHEALTHY_NODES', 3);
  }

  async onModuleInit() {
    this.logger.log('边缘节点管理服务初始化');
    this.startHealthMonitoring();
  }

  /**
   * 注册边缘节点
   */
  async registerNode(nodeInfo: Partial<EdgeNode>): Promise<EdgeNode> {
    const nodeId = nodeInfo.nodeId!;
    
    if (this.edgeNodes.has(nodeId)) {
      this.logger.warn(`节点 ${nodeId} 已存在，更新节点信息`);
    }

    const node: EdgeNode = {
      nodeId,
      region: nodeInfo.region!,
      endpoint: nodeInfo.endpoint!,
      status: EdgeNodeStatus.ONLINE,
      capabilities: nodeInfo.capabilities!,
      metrics: {
        currentUsers: 0,
        cpuUsage: 0,
        memoryUsage: 0,
        networkLatency: 0,
        uptime: 0,
        ...nodeInfo.metrics,
      },
      location: nodeInfo.location || {
        latitude: 0,
        longitude: 0,
        city: 'Unknown',
        country: 'Unknown',
      },
      lastHeartbeat: new Date(),
      registeredAt: new Date(),
      version: nodeInfo.version || '1.0.0',
    };

    // 添加到节点映射
    this.edgeNodes.set(nodeId, node);
    
    // 添加到区域映射
    if (!this.regionNodes.has(node.region)) {
      this.regionNodes.set(node.region, new Set());
    }
    this.regionNodes.get(node.region)!.add(nodeId);

    // 初始化轮询计数器
    if (!this.roundRobinCounters.has(node.region)) {
      this.roundRobinCounters.set(node.region, 0);
    }

    this.logger.log(`边缘节点注册成功: ${nodeId} (区域: ${node.region})`);
    
    // 触发节点注册事件
    this.eventEmitter.emit('edge.node.registered', node);

    return node;
  }

  /**
   * 注销边缘节点
   */
  async unregisterNode(nodeId: string): Promise<boolean> {
    const node = this.edgeNodes.get(nodeId);
    if (!node) {
      this.logger.warn(`尝试注销不存在的节点: ${nodeId}`);
      return false;
    }

    // 从节点映射中移除
    this.edgeNodes.delete(nodeId);
    
    // 从区域映射中移除
    const regionSet = this.regionNodes.get(node.region);
    if (regionSet) {
      regionSet.delete(nodeId);
      if (regionSet.size === 0) {
        this.regionNodes.delete(node.region);
        this.roundRobinCounters.delete(node.region);
      }
    }

    this.logger.log(`边缘节点注销成功: ${nodeId}`);
    
    // 触发节点注销事件
    this.eventEmitter.emit('edge.node.unregistered', node);

    return true;
  }

  /**
   * 更新节点心跳
   */
  async updateHeartbeat(nodeId: string, metrics?: Partial<EdgeNode['metrics']>): Promise<boolean> {
    const node = this.edgeNodes.get(nodeId);
    if (!node) {
      this.logger.warn(`收到未知节点的心跳: ${nodeId}`);
      return false;
    }

    // 更新心跳时间
    node.lastHeartbeat = new Date();
    
    // 更新指标
    if (metrics) {
      node.metrics = { ...node.metrics, ...metrics };
    }

    // 更新节点状态
    const newStatus = this.calculateNodeStatus(node);
    if (newStatus !== node.status) {
      const oldStatus = node.status;
      node.status = newStatus;
      
      this.logger.log(`节点 ${nodeId} 状态变更: ${oldStatus} -> ${newStatus}`);
      
      // 触发状态变更事件
      this.eventEmitter.emit('edge.node.status.changed', {
        nodeId,
        oldStatus,
        newStatus,
        node,
      });
    }

    return true;
  }

  /**
   * 获取最优边缘节点
   */
  async getOptimalNode(
    clientLocation?: { latitude: number; longitude: number },
    strategy: LoadBalancingStrategy = LoadBalancingStrategy.HYBRID,
    region?: string,
  ): Promise<EdgeNode | null> {
    let candidateNodes = Array.from(this.edgeNodes.values()).filter(
      node => node.status === EdgeNodeStatus.ONLINE && !this.isNodeOverloaded(node)
    );

    // 如果指定了区域，优先选择该区域的节点
    if (region) {
      const regionCandidates = candidateNodes.filter(node => node.region === region);
      if (regionCandidates.length > 0) {
        candidateNodes = regionCandidates;
      }
    }

    if (candidateNodes.length === 0) {
      this.logger.warn('没有可用的边缘节点');
      return null;
    }

    switch (strategy) {
      case LoadBalancingStrategy.ROUND_ROBIN:
        return this.selectByRoundRobin(candidateNodes, region);
      
      case LoadBalancingStrategy.LEAST_CONNECTIONS:
        return this.selectByLeastConnections(candidateNodes);
      
      case LoadBalancingStrategy.GEOGRAPHIC_PROXIMITY:
        return this.selectByGeographicProximity(candidateNodes, clientLocation);
      
      case LoadBalancingStrategy.RESOURCE_BASED:
        return this.selectByResourceUsage(candidateNodes);
      
      case LoadBalancingStrategy.HYBRID:
      default:
        return this.selectByHybridStrategy(candidateNodes, clientLocation);
    }
  }

  /**
   * 轮询选择
   */
  private selectByRoundRobin(nodes: EdgeNode[], region?: string): EdgeNode {
    const key = region || 'global';
    const counter = this.roundRobinCounters.get(key) || 0;
    const selectedNode = nodes[counter % nodes.length];
    
    this.roundRobinCounters.set(key, counter + 1);
    return selectedNode;
  }

  /**
   * 最少连接选择
   */
  private selectByLeastConnections(nodes: EdgeNode[]): EdgeNode {
    return nodes.reduce((best, current) => 
      current.metrics.currentUsers < best.metrics.currentUsers ? current : best
    );
  }

  /**
   * 地理位置就近选择
   */
  private selectByGeographicProximity(
    nodes: EdgeNode[], 
    clientLocation?: { latitude: number; longitude: number }
  ): EdgeNode {
    if (!clientLocation) {
      return nodes[0];
    }

    return nodes.reduce((best, current) => {
      const bestDistance = this.calculateDistance(clientLocation, best.location);
      const currentDistance = this.calculateDistance(clientLocation, current.location);
      return currentDistance < bestDistance ? current : best;
    });
  }

  /**
   * 资源使用率选择
   */
  private selectByResourceUsage(nodes: EdgeNode[]): EdgeNode {
    return nodes.reduce((best, current) => {
      const bestScore = (best.metrics.cpuUsage + best.metrics.memoryUsage) / 2;
      const currentScore = (current.metrics.cpuUsage + current.metrics.memoryUsage) / 2;
      return currentScore < bestScore ? current : best;
    });
  }

  /**
   * 混合策略选择
   */
  private selectByHybridStrategy(
    nodes: EdgeNode[], 
    clientLocation?: { latitude: number; longitude: number }
  ): EdgeNode {
    // 综合评分：地理距离(40%) + 资源使用率(30%) + 当前连接数(30%)
    return nodes.reduce((best, current) => {
      const bestScore = this.calculateHybridScore(best, clientLocation);
      const currentScore = this.calculateHybridScore(current, clientLocation);
      return currentScore < bestScore ? current : best;
    });
  }

  /**
   * 计算混合评分
   */
  private calculateHybridScore(
    node: EdgeNode, 
    clientLocation?: { latitude: number; longitude: number }
  ): number {
    let score = 0;

    // 地理距离评分 (40%)
    if (clientLocation) {
      const distance = this.calculateDistance(clientLocation, node.location);
      score += (distance / 20000) * 0.4; // 标准化到0-1范围
    }

    // 资源使用率评分 (30%)
    const resourceScore = (node.metrics.cpuUsage + node.metrics.memoryUsage) / 200;
    score += resourceScore * 0.3;

    // 连接数评分 (30%)
    const connectionScore = node.metrics.currentUsers / node.capabilities.maxUsers;
    score += connectionScore * 0.3;

    return score;
  }

  /**
   * 计算地理距离（公里）
   */
  private calculateDistance(
    loc1: { latitude: number; longitude: number },
    loc2: { latitude: number; longitude: number }
  ): number {
    const R = 6371; // 地球半径（公里）
    const dLat = this.toRadians(loc2.latitude - loc1.latitude);
    const dLng = this.toRadians(loc2.longitude - loc1.longitude);
    
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(this.toRadians(loc1.latitude)) * Math.cos(this.toRadians(loc2.latitude)) *
              Math.sin(dLng/2) * Math.sin(dLng/2);
    
    return R * 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  }

  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * 计算节点状态
   */
  private calculateNodeStatus(node: EdgeNode): EdgeNodeStatus {
    // 检查心跳超时
    const timeSinceHeartbeat = Date.now() - node.lastHeartbeat.getTime();
    if (timeSinceHeartbeat > this.heartbeatTimeout) {
      return EdgeNodeStatus.OFFLINE;
    }

    // 检查是否过载
    if (this.isNodeOverloaded(node)) {
      return EdgeNodeStatus.OVERLOADED;
    }

    // 检查资源健康状态
    if (node.metrics.cpuUsage > 90 || node.metrics.memoryUsage > 90) {
      return EdgeNodeStatus.UNHEALTHY;
    }

    return EdgeNodeStatus.ONLINE;
  }

  /**
   * 检查节点是否过载
   */
  private isNodeOverloaded(node: EdgeNode): boolean {
    const userLoadRatio = node.metrics.currentUsers / node.capabilities.maxUsers;
    return userLoadRatio > 0.9 || node.metrics.cpuUsage > 85 || node.metrics.memoryUsage > 85;
  }

  /**
   * 启动健康监控
   */
  private startHealthMonitoring(): void {
    this.logger.log('启动边缘节点健康监控');
  }

  /**
   * 定期健康检查
   */
  @Cron(CronExpression.EVERY_30_SECONDS)
  async performHealthCheck(): Promise<void> {
    const now = Date.now();
    const unhealthyNodes: string[] = [];

    for (const [nodeId, node] of this.edgeNodes.entries()) {
      const timeSinceHeartbeat = now - node.lastHeartbeat.getTime();
      
      if (timeSinceHeartbeat > this.heartbeatTimeout) {
        if (node.status !== EdgeNodeStatus.OFFLINE) {
          node.status = EdgeNodeStatus.OFFLINE;
          unhealthyNodes.push(nodeId);
          
          this.logger.warn(`节点 ${nodeId} 心跳超时，标记为离线`);
          
          // 触发节点离线事件
          this.eventEmitter.emit('edge.node.offline', node);
        }
      }
    }

    // 如果不健康节点过多，触发告警
    if (unhealthyNodes.length > this.maxUnhealthyNodes) {
      this.eventEmitter.emit('edge.cluster.unhealthy', {
        unhealthyCount: unhealthyNodes.length,
        unhealthyNodes,
        totalNodes: this.edgeNodes.size,
      });
    }
  }

  /**
   * 获取所有节点
   */
  getAllNodes(): EdgeNode[] {
    return Array.from(this.edgeNodes.values());
  }

  /**
   * 获取区域节点
   */
  getNodesByRegion(region: string): EdgeNode[] {
    const nodeIds = this.regionNodes.get(region);
    if (!nodeIds) return [];
    
    return Array.from(nodeIds)
      .map(id => this.edgeNodes.get(id))
      .filter(node => node !== undefined) as EdgeNode[];
  }

  /**
   * 获取在线节点数量
   */
  getOnlineNodeCount(): number {
    return Array.from(this.edgeNodes.values())
      .filter(node => node.status === EdgeNodeStatus.ONLINE).length;
  }

  /**
   * 获取集群统计信息
   */
  getClusterStats(): {
    totalNodes: number;
    onlineNodes: number;
    offlineNodes: number;
    overloadedNodes: number;
    totalUsers: number;
    averageCpuUsage: number;
    averageMemoryUsage: number;
  } {
    const nodes = Array.from(this.edgeNodes.values());

    return {
      totalNodes: nodes.length,
      onlineNodes: nodes.filter(n => n.status === EdgeNodeStatus.ONLINE).length,
      offlineNodes: nodes.filter(n => n.status === EdgeNodeStatus.OFFLINE).length,
      overloadedNodes: nodes.filter(n => n.status === EdgeNodeStatus.OVERLOADED).length,
      totalUsers: nodes.reduce((sum, n) => sum + n.metrics.currentUsers, 0),
      averageCpuUsage: nodes.reduce((sum, n) => sum + n.metrics.cpuUsage, 0) / nodes.length || 0,
      averageMemoryUsage: nodes.reduce((sum, n) => sum + n.metrics.memoryUsage, 0) / nodes.length || 0,
    };
  }

  /**
   * 获取节点
   */
  getNode(nodeId: string): EdgeNode | undefined {
    return this.edgeNodes.get(nodeId);
  }

  /**
   * 获取可用区域列表
   */
  getAvailableRegions(): string[] {
    return Array.from(this.regionNodes.keys());
  }
}

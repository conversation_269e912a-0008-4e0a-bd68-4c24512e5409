import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { ConfigService } from '@nestjs/config';
import * as zlib from 'zlib';

/**
 * 网络质量接口
 */
export interface NetworkQuality {
  bandwidth: number;        // 带宽 (bps)
  latency: number;         // 延迟 (ms)
  packetLoss: number;      // 丢包率 (0-1)
  jitter: number;          // 抖动 (ms)
  stability: number;       // 稳定性 (0-1)
  quality: 'poor' | 'fair' | 'good' | 'excellent';
}

/**
 * 数据特征接口
 */
export interface DataCharacteristics {
  size: number;
  type: string;
  compressibility: number; // 可压缩性 (0-1)
  priority: number;        // 优先级 (0-100)
  realtime: boolean;       // 是否实时数据
  redundancy: number;      // 冗余度 (0-1)
}

/**
 * 编码策略接口
 */
export interface EncodingStrategy {
  encoding: 'json' | 'msgpack' | 'protobuf' | 'binary';
  compression: 'none' | 'gzip' | 'lz4' | 'zstd' | 'brotli';
  priority: 'size' | 'speed' | 'balanced';
  fec: boolean;            // 是否使用前向纠错
  redundancy: number;      // 冗余度
}

/**
 * 编码数据接口
 */
export interface EncodedData {
  data: Buffer;
  encoding: string;
  compression: string;
  metadata: {
    originalSize: number;
    compressedSize: number;
    compressionRatio: number;
    estimatedTransferTime: number;
    checksum: string;
    fecBlocks?: Buffer[];
  };
}

/**
 * 传输结果接口
 */
export interface TransmissionResult {
  success: boolean;
  actualLatency: number;
  throughput: number;
  retransmissions: number;
  errorRate: number;
  timestamp: Date;
}

/**
 * 可靠性等级枚举
 */
export enum ReliabilityLevel {
  BEST_EFFORT = 'best_effort',
  RELIABLE = 'reliable',
  HIGH = 'high',
  CRITICAL = 'critical'
}

/**
 * FEC策略接口
 */
export interface FECStrategy {
  algorithm: 'none' | 'hamming' | 'reed-solomon' | 'ldpc';
  redundancy: number;      // 冗余度 (0-1)
  blockSize: number;       // 块大小
  interleaving: boolean;   // 是否交织
}

/**
 * 传输计划接口
 */
export interface TransmissionPlan {
  chunks: TransmissionChunk[];
  totalSize: number;
  estimatedTime: number;
  reliability: ReliabilityLevel;
  strategy: EncodingStrategy;
  fecStrategy: FECStrategy;
}

/**
 * 传输块接口
 */
export interface TransmissionChunk {
  id: string;
  data: Buffer;
  sequence: number;
  size: number;
  checksum: string;
  fecData?: Buffer;
}

/**
 * 边缘节点接口
 */
export interface EdgeNode {
  id: string;
  endpoint: string;
  region: string;
  networkQuality: NetworkQuality;
  capabilities: {
    maxBandwidth: number;
    supportedEncodings: string[];
    supportedCompressions: string[];
  };
}

/**
 * 自适应网络传输服务
 * 根据网络状况和数据特征动态选择最优传输策略
 */
@Injectable()
export class AdaptiveNetworkService {
  private readonly logger = new Logger(AdaptiveNetworkService.name);

  // 网络质量监控
  private networkQualities: Map<string, NetworkQuality> = new Map();

  // 丢包率预测模型
  private packetLossHistory: Map<string, number[]> = new Map();

  // 编码性能缓存
  private encodingPerformance: Map<string, any> = new Map();

  // 传输统计
  private transmissionStats = {
    totalTransmissions: 0,
    successfulTransmissions: 0,
    totalRetransmissions: 0,
    averageLatency: 0,
    averageThroughput: 0,
    compressionRatios: new Map<string, number>()
  };

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.initializeService();
  }

  /**
   * 初始化服务
   */
  private initializeService(): void {
    this.logger.log('自适应网络传输服务初始化');

    // 初始化编码性能基准
    this.initializeEncodingBenchmarks();
  }

  /**
   * 初始化编码性能基准
   */
  private initializeEncodingBenchmarks(): void {
    // JSON编码基准
    this.encodingPerformance.set('json', {
      encodeSpeed: 1000,    // KB/s
      decodeSpeed: 1200,
      compressionRatio: 0.3,
      cpuUsage: 0.1
    });

    // MessagePack编码基准
    this.encodingPerformance.set('msgpack', {
      encodeSpeed: 2000,
      decodeSpeed: 2500,
      compressionRatio: 0.5,
      cpuUsage: 0.15
    });

    // Protocol Buffers编码基准
    this.encodingPerformance.set('protobuf', {
      encodeSpeed: 3000,
      decodeSpeed: 3500,
      compressionRatio: 0.6,
      cpuUsage: 0.2
    });

    // 二进制编码基准
    this.encodingPerformance.set('binary', {
      encodeSpeed: 5000,
      decodeSpeed: 6000,
      compressionRatio: 0.8,
      cpuUsage: 0.05
    });
  }

  /**
   * 自适应编码数据
   * @param data 原始数据
   * @param targetNode 目标节点
   * @returns 编码后的数据
   */
  async encodeData(data: any, targetNode: EdgeNode): Promise<EncodedData> {
    const networkQuality = await this.getNetworkQuality(targetNode.id);
    const dataCharacteristics = this.analyzeData(data);

    // 选择最优编码策略
    const encodingStrategy = this.selectEncodingStrategy(
      networkQuality,
      dataCharacteristics,
      targetNode
    );

    // 应用编码
    const encodedData = await this.applyEncoding(data, encodingStrategy);

    // 记录性能数据
    this.recordEncodingPerformance(encodingStrategy, encodedData);

    return encodedData;
  }

  /**
   * 获取网络质量
   * @param nodeId 节点ID
   * @returns 网络质量
   */
  private async getNetworkQuality(nodeId: string): Promise<NetworkQuality> {
    let quality = this.networkQualities.get(nodeId);

    if (!quality) {
      // 初始化默认网络质量
      quality = {
        bandwidth: 10000000,  // 10Mbps
        latency: 50,          // 50ms
        packetLoss: 0.01,     // 1%
        jitter: 5,            // 5ms
        stability: 0.9,       // 90%
        quality: 'good'
      };
      this.networkQualities.set(nodeId, quality);
    }

    return quality;
  }

  /**
   * 分析数据特征
   * @param data 数据
   * @returns 数据特征
   */
  private analyzeData(data: any): DataCharacteristics {
    const serialized = JSON.stringify(data);
    const size = Buffer.byteLength(serialized, 'utf8');

    // 分析数据类型
    let type = 'unknown';
    if (typeof data === 'object') {
      if (Array.isArray(data)) {
        type = 'array';
      } else if (data.type) {
        type = data.type;
      } else {
        type = 'object';
      }
    } else {
      type = typeof data;
    }

    // 估算可压缩性
    const compressibility = this.estimateCompressibility(serialized);

    // 确定优先级
    const priority = data.priority || 50;

    // 检查是否为实时数据
    const realtime = data.realtime || false;

    return {
      size,
      type,
      compressibility,
      priority,
      realtime,
      redundancy: 0.1
    };
  }

  /**
   * 估算数据可压缩性
   * @param data 数据字符串
   * @returns 可压缩性 (0-1)
   */
  private estimateCompressibility(data: string): number {
    // 简单的重复字符分析
    const charFreq = new Map<string, number>();
    for (const char of data) {
      charFreq.set(char, (charFreq.get(char) || 0) + 1);
    }

    // 计算熵
    let entropy = 0;
    const length = data.length;
    for (const freq of charFreq.values()) {
      const probability = freq / length;
      entropy -= probability * Math.log2(probability);
    }

    // 归一化熵到可压缩性
    const maxEntropy = Math.log2(256); // 假设8位字符
    return 1 - (entropy / maxEntropy);
  }

  /**
   * 选择编码策略
   * @param networkQuality 网络质量
   * @param dataCharacteristics 数据特征
   * @param targetNode 目标节点
   * @returns 编码策略
   */
  private selectEncodingStrategy(
    networkQuality: NetworkQuality,
    dataCharacteristics: DataCharacteristics,
    targetNode: EdgeNode
  ): EncodingStrategy {
    let encoding: EncodingStrategy['encoding'] = 'json';
    let compression: EncodingStrategy['compression'] = 'none';
    let priority: EncodingStrategy['priority'] = 'balanced';
    let fec = false;
    let redundancy = 0;

    // 根据网络质量选择策略
    if (networkQuality.bandwidth < 1000000) { // 低带宽 < 1Mbps
      priority = 'size';
      compression = 'lz4'; // 快速压缩

      if (dataCharacteristics.compressibility > 0.5) {
        compression = 'gzip'; // 更好的压缩率
      }
    } else if (networkQuality.latency > 100) { // 高延迟 > 100ms
      priority = 'speed';
      encoding = 'binary';
      compression = 'none';
    } else if (networkQuality.packetLoss > 0.05) { // 高丢包率 > 5%
      fec = true;
      redundancy = Math.min(0.3, networkQuality.packetLoss * 3);
    }

    // 根据数据特征调整
    if (dataCharacteristics.realtime) {
      priority = 'speed';
      compression = 'none';
      encoding = 'binary';
    }

    if (dataCharacteristics.priority > 80) {
      fec = true;
      redundancy = Math.max(redundancy, 0.1);
    }

    // 根据节点能力调整
    if (!targetNode.capabilities.supportedEncodings.includes(encoding)) {
      encoding = 'json'; // 回退到通用编码
    }

    if (!targetNode.capabilities.supportedCompressions.includes(compression)) {
      compression = 'none'; // 回退到无压缩
    }

    return {
      encoding,
      compression,
      priority,
      fec,
      redundancy
    };
  }

  /**
   * 应用编码
   * @param data 原始数据
   * @param strategy 编码策略
   * @returns 编码后的数据
   */
  private async applyEncoding(data: any, strategy: EncodingStrategy): Promise<EncodedData> {

    // 第一步：序列化
    let serialized: Buffer;
    switch (strategy.encoding) {
      case 'msgpack':
        serialized = await this.encodeMsgPack(data);
        break;
      case 'protobuf':
        serialized = await this.encodeProtobuf(data);
        break;
      case 'binary':
        serialized = await this.encodeBinary(data);
        break;
      default: // json
        serialized = Buffer.from(JSON.stringify(data), 'utf8');
    }

    const originalSize = serialized.length;

    // 第二步：压缩
    let compressed: Buffer = serialized;
    if (strategy.compression !== 'none') {
      compressed = await this.applyCompression(serialized, strategy.compression);
    }

    // 第三步：前向纠错编码
    let fecBlocks: Buffer[] | undefined;
    if (strategy.fec) {
      fecBlocks = await this.generateFECBlocks(compressed, strategy.redundancy);
    }

    const compressedSize = compressed.length;
    const compressionRatio = compressedSize / originalSize;

    // 计算校验和
    const checksum = this.calculateChecksum(compressed);

    return {
      data: compressed,
      encoding: strategy.encoding,
      compression: strategy.compression,
      metadata: {
        originalSize,
        compressedSize,
        compressionRatio,
        estimatedTransferTime: this.estimateTransferTime(compressedSize, 10000000), // 假设10Mbps
        checksum,
        fecBlocks
      }
    };
  }

  /**
   * MessagePack编码
   * @param data 数据
   * @returns 编码后的Buffer
   */
  private async encodeMsgPack(data: any): Promise<Buffer> {
    // 简化实现，实际应使用msgpack库
    return Buffer.from(JSON.stringify(data), 'utf8');
  }

  /**
   * Protocol Buffers编码
   * @param data 数据
   * @returns 编码后的Buffer
   */
  private async encodeProtobuf(data: any): Promise<Buffer> {
    // 简化实现，实际应使用protobuf库
    return Buffer.from(JSON.stringify(data), 'utf8');
  }

  /**
   * 二进制编码
   * @param data 数据
   * @returns 编码后的Buffer
   */
  private async encodeBinary(data: any): Promise<Buffer> {
    // 简化的二进制编码
    const json = JSON.stringify(data);
    return Buffer.from(json, 'utf8');
  }

  /**
   * 模拟LZ4压缩算法
   * @param data 原始数据
   * @returns 压缩后的数据
   */
  private simulateLZ4Compression(data: Buffer): Buffer {
    // 简化的LZ4压缩模拟
    // 实际LZ4使用字典压缩和快速匹配算法
    const compressionRatio = 0.7; // 假设70%的压缩率
    const compressedSize = Math.floor(data.length * compressionRatio);
    const compressed = Buffer.alloc(compressedSize);

    // 简单的数据复制和压缩模拟
    for (let i = 0; i < compressedSize; i++) {
      compressed[i] = data[i % data.length];
    }

    return compressed;
  }

  /**
   * 应用压缩
   * @param data 数据
   * @param algorithm 压缩算法
   * @returns 压缩后的数据
   */
  private async applyCompression(data: Buffer, algorithm: string): Promise<Buffer> {
    switch (algorithm) {
      case 'gzip':
        return new Promise((resolve, reject) => {
          zlib.gzip(data, (err, result) => {
            if (err) reject(err);
            else resolve(result);
          });
        });

      case 'lz4':
        // 简化的LZ4压缩模拟实现
        return new Promise((resolve) => {
          // 模拟LZ4快速压缩算法
          const compressed = this.simulateLZ4Compression(data);
          resolve(compressed);
        });

      case 'zstd':
        // 简化实现，实际应使用zstd库
        return new Promise((resolve) => {
          const compressed = Buffer.alloc(Math.floor(data.length * 0.6));
          data.copy(compressed, 0, 0, compressed.length);
          resolve(compressed);
        });

      case 'brotli':
        return new Promise((resolve, reject) => {
          zlib.brotliCompress(data, (err, result) => {
            if (err) reject(err);
            else resolve(result);
          });
        });

      default:
        return data;
    }
  }

  /**
   * 生成前向纠错块
   * @param data 数据
   * @param redundancy 冗余度
   * @returns FEC块数组
   */
  private async generateFECBlocks(data: Buffer, redundancy: number): Promise<Buffer[]> {
    const fecBlocks: Buffer[] = [];
    const blockCount = Math.ceil(redundancy * 10); // 简化计算

    for (let i = 0; i < blockCount; i++) {
      // 简化的FEC块生成
      const blockSize = Math.floor(data.length / 10);
      const block = Buffer.alloc(blockSize);

      // 简单的异或校验
      for (let j = 0; j < blockSize; j++) {
        block[j] = data[j % data.length] ^ (i + 1);
      }

      fecBlocks.push(block);
    }

    return fecBlocks;
  }

  /**
   * 计算校验和
   * @param data 数据
   * @returns 校验和
   */
  private calculateChecksum(data: Buffer): string {
    let checksum = 0;
    for (let i = 0; i < data.length; i++) {
      checksum = (checksum + data[i]) % 65536;
    }
    return checksum.toString(16);
  }

  /**
   * 估算传输时间
   * @param size 数据大小
   * @param bandwidth 带宽
   * @returns 传输时间（毫秒）
   */
  private estimateTransferTime(size: number, bandwidth: number): number {
    return (size * 8 * 1000) / bandwidth; // 转换为毫秒
  }

  /**
   * 记录编码性能
   * @param strategy 编码策略
   * @param result 编码结果
   */
  private recordEncodingPerformance(strategy: EncodingStrategy, result: EncodedData): void {
    const key = `${strategy.encoding}_${strategy.compression}`;

    // 更新压缩比统计
    this.transmissionStats.compressionRatios.set(key, result.metadata.compressionRatio);

    // 触发性能记录事件
    this.eventEmitter.emit('encoding.performance', {
      strategy,
      compressionRatio: result.metadata.compressionRatio,
      originalSize: result.metadata.originalSize,
      compressedSize: result.metadata.compressedSize
    });
  }

  /**
   * 智能重传和可靠传输
   * @param data 数据
   * @param destination 目标节点
   * @param reliabilityLevel 可靠性等级
   * @returns 传输结果
   */
  async sendWithReliability(
    data: Buffer,
    destination: EdgeNode,
    reliabilityLevel: ReliabilityLevel
  ): Promise<TransmissionResult> {
    // 预测丢包率
    const lossRate = await this.predictPacketLoss(destination.id);

    // 选择FEC策略
    const fecStrategy = this.selectFECStrategy(lossRate, reliabilityLevel);

    // 编码数据
    const encodedData = await this.encodeFEC(data, fecStrategy);

    // 创建传输计划
    const transmissionPlan = await this.createTransmissionPlan(
      encodedData,
      destination,
      fecStrategy,
      reliabilityLevel
    );

    // 执行传输
    const result = await this.executeTransmission(transmissionPlan, destination);

    // 更新统计信息
    this.updateTransmissionStats(result);

    return result;
  }

  /**
   * 预测丢包率
   * @param nodeId 节点ID
   * @returns 预测的丢包率
   */
  private async predictPacketLoss(nodeId: string): Promise<number> {
    const history = this.packetLossHistory.get(nodeId) || [];

    if (history.length === 0) {
      return 0.01; // 默认1%丢包率
    }

    // 简单的移动平均预测
    const recentHistory = history.slice(-10); // 最近10次记录
    const average = recentHistory.reduce((sum, loss) => sum + loss, 0) / recentHistory.length;

    // 考虑趋势
    if (recentHistory.length >= 3) {
      const trend = (recentHistory[recentHistory.length - 1] - recentHistory[0]) / recentHistory.length;
      return Math.max(0, Math.min(1, average + trend));
    }

    return average;
  }

  /**
   * 选择FEC策略
   * @param lossRate 丢包率
   * @param reliabilityLevel 可靠性等级
   * @returns FEC策略
   */
  private selectFECStrategy(
    lossRate: number,
    reliabilityLevel: ReliabilityLevel
  ): FECStrategy {
    if (reliabilityLevel === ReliabilityLevel.CRITICAL || lossRate > 0.05) {
      return {
        algorithm: 'reed-solomon',
        redundancy: Math.min(0.5, lossRate * 3), // 最多50%冗余
        blockSize: 1024,
        interleaving: true
      };
    }

    if (reliabilityLevel === ReliabilityLevel.HIGH || lossRate > 0.02) {
      return {
        algorithm: 'hamming',
        redundancy: Math.min(0.25, lossRate * 2),
        blockSize: 512,
        interleaving: false
      };
    }

    if (reliabilityLevel === ReliabilityLevel.RELIABLE || lossRate > 0.01) {
      return {
        algorithm: 'ldpc',
        redundancy: Math.min(0.15, lossRate * 1.5),
        blockSize: 256,
        interleaving: false
      };
    }

    return {
      algorithm: 'none',
      redundancy: 0,
      blockSize: 0,
      interleaving: false
    };
  }

  /**
   * FEC编码
   * @param data 原始数据
   * @param strategy FEC策略
   * @returns 编码后的数据
   */
  private async encodeFEC(data: Buffer, strategy: FECStrategy): Promise<Buffer> {
    if (strategy.algorithm === 'none') {
      return data;
    }

    const redundantBytes = Math.floor(data.length * strategy.redundancy);
    const encodedData = Buffer.alloc(data.length + redundantBytes);

    // 复制原始数据
    data.copy(encodedData, 0);

    // 生成冗余数据
    switch (strategy.algorithm) {
      case 'hamming':
        this.generateHammingCode(data, encodedData, data.length);
        break;
      case 'reed-solomon':
        this.generateReedSolomonCode(data, encodedData, data.length);
        break;
      case 'ldpc':
        this.generateLDPCCode(data, encodedData, data.length);
        break;
    }

    // 应用交织
    if (strategy.interleaving) {
      return this.applyInterleaving(encodedData, strategy.blockSize);
    }

    return encodedData;
  }

  /**
   * 生成汉明码
   * @param data 原始数据
   * @param encoded 编码缓冲区
   * @param offset 偏移量
   */
  private generateHammingCode(data: Buffer, encoded: Buffer, offset: number): void {
    // 简化的汉明码实现
    for (let i = 0; i < data.length; i += 4) {
      let parity = 0;
      for (let j = 0; j < 4 && i + j < data.length; j++) {
        parity ^= data[i + j];
      }
      if (offset + Math.floor(i / 4) < encoded.length) {
        encoded[offset + Math.floor(i / 4)] = parity;
      }
    }
  }

  /**
   * 生成Reed-Solomon码
   * @param data 原始数据
   * @param encoded 编码缓冲区
   * @param offset 偏移量
   */
  private generateReedSolomonCode(data: Buffer, encoded: Buffer, offset: number): void {
    // 简化的Reed-Solomon码实现
    const redundantBytes = encoded.length - offset;
    for (let i = 0; i < redundantBytes; i++) {
      let checksum = 0;
      for (let j = 0; j < data.length; j++) {
        checksum ^= data[j] * (i + 1);
      }
      encoded[offset + i] = checksum & 0xFF;
    }
  }

  /**
   * 生成LDPC码
   * @param data 原始数据
   * @param encoded 编码缓冲区
   * @param offset 偏移量
   */
  private generateLDPCCode(data: Buffer, encoded: Buffer, offset: number): void {
    // 简化的LDPC码实现
    const redundantBytes = encoded.length - offset;
    for (let i = 0; i < redundantBytes; i++) {
      let parity = 0;
      // 稀疏校验矩阵的简化实现
      for (let j = i; j < data.length; j += redundantBytes) {
        parity ^= data[j];
      }
      encoded[offset + i] = parity;
    }
  }

  /**
   * 应用交织
   * @param data 数据
   * @param blockSize 块大小
   * @returns 交织后的数据
   */
  private applyInterleaving(data: Buffer, blockSize: number): Buffer {
    const interleaved = Buffer.alloc(data.length);
    const blocks = Math.ceil(data.length / blockSize);

    for (let i = 0; i < data.length; i++) {
      const block = Math.floor(i / blockSize);
      const position = i % blockSize;
      const newIndex = position * blocks + block;

      if (newIndex < interleaved.length) {
        interleaved[newIndex] = data[i];
      }
    }

    return interleaved;
  }

  /**
   * 创建传输计划
   * @param data 编码数据
   * @param destination 目标节点
   * @param fecStrategy FEC策略
   * @param reliability 可靠性等级
   * @returns 传输计划
   */
  private async createTransmissionPlan(
    data: Buffer,
    destination: EdgeNode,
    fecStrategy: FECStrategy,
    reliability: ReliabilityLevel
  ): Promise<TransmissionPlan> {
    const chunkSize = this.calculateOptimalChunkSize(destination);
    const chunks: TransmissionChunk[] = [];

    for (let i = 0; i < data.length; i += chunkSize) {
      const chunkData = data.subarray(i, Math.min(i + chunkSize, data.length));
      const chunk: TransmissionChunk = {
        id: `chunk_${i / chunkSize}`,
        data: chunkData,
        sequence: i / chunkSize,
        size: chunkData.length,
        checksum: this.calculateChecksum(chunkData)
      };

      // 为关键数据添加FEC
      if (fecStrategy.algorithm !== 'none' && reliability !== ReliabilityLevel.BEST_EFFORT) {
        chunk.fecData = await this.generateChunkFEC(chunkData, fecStrategy);
      }

      chunks.push(chunk);
    }

    return {
      chunks,
      totalSize: data.length,
      estimatedTime: this.estimateTransmissionTime(data.length, destination),
      reliability,
      strategy: {
        encoding: 'binary',
        compression: 'none',
        priority: 'balanced',
        fec: fecStrategy.algorithm !== 'none',
        redundancy: fecStrategy.redundancy
      },
      fecStrategy
    };
  }

  /**
   * 计算最优块大小
   * @param destination 目标节点
   * @returns 块大小
   */
  private calculateOptimalChunkSize(destination: EdgeNode): number {
    const networkQuality = this.networkQualities.get(destination.id);
    if (!networkQuality) {
      return 1024; // 默认1KB
    }

    // 根据网络质量调整块大小
    if (networkQuality.packetLoss > 0.05) {
      return 512; // 高丢包率使用小块
    } else if (networkQuality.bandwidth > 10000000) {
      return 4096; // 高带宽使用大块
    } else {
      return 1024; // 默认块大小
    }
  }

  /**
   * 生成块FEC数据
   * @param chunkData 块数据
   * @param strategy FEC策略
   * @returns FEC数据
   */
  private async generateChunkFEC(chunkData: Buffer, strategy: FECStrategy): Promise<Buffer> {
    const fecSize = Math.floor(chunkData.length * strategy.redundancy);
    const fecData = Buffer.alloc(fecSize);

    // 简化的FEC生成
    for (let i = 0; i < fecSize; i++) {
      let checksum = 0;
      for (let j = 0; j < chunkData.length; j++) {
        checksum ^= chunkData[j] * (i + 1);
      }
      fecData[i] = checksum & 0xFF;
    }

    return fecData;
  }

  /**
   * 估算传输时间
   * @param size 数据大小
   * @param destination 目标节点
   * @returns 传输时间（毫秒）
   */
  private estimateTransmissionTime(size: number, destination: EdgeNode): number {
    const networkQuality = this.networkQualities.get(destination.id);
    if (!networkQuality) {
      return (size * 8 * 1000) / 10000000; // 假设10Mbps
    }

    const baseTime = (size * 8 * 1000) / networkQuality.bandwidth;
    const latencyPenalty = networkQuality.latency * 2; // 往返延迟
    const lossPenalty = networkQuality.packetLoss * baseTime * 2; // 重传开销

    return baseTime + latencyPenalty + lossPenalty;
  }

  /**
   * 执行传输
   * @param plan 传输计划
   * @param destination 目标节点
   * @returns 传输结果
   */
  private async executeTransmission(
    plan: TransmissionPlan,
    destination: EdgeNode
  ): Promise<TransmissionResult> {
    const startTime = Date.now();
    let retransmissions = 0;
    let errors = 0;

    try {
      // 模拟传输过程
      for (const chunk of plan.chunks) {
        const success = await this.transmitChunk(chunk, destination);
        if (!success) {
          // 重传
          retransmissions++;
          const retrySuccess = await this.transmitChunk(chunk, destination);
          if (!retrySuccess) {
            errors++;
          }
        }
      }

      const actualLatency = Date.now() - startTime;
      const throughput = (plan.totalSize * 8 * 1000) / actualLatency; // bps
      const errorRate = errors / plan.chunks.length;

      return {
        success: errors === 0,
        actualLatency,
        throughput,
        retransmissions,
        errorRate,
        timestamp: new Date()
      };
    } catch (error) {
      this.logger.error('传输执行失败', error);
      return {
        success: false,
        actualLatency: Date.now() - startTime,
        throughput: 0,
        retransmissions,
        errorRate: 1,
        timestamp: new Date()
      };
    }
  }

  /**
   * 传输单个块
   * @param chunk 数据块
   * @param destination 目标节点
   * @returns 是否成功
   */
  private async transmitChunk(_chunk: TransmissionChunk, destination: EdgeNode): Promise<boolean> {
    // 模拟网络传输
    const networkQuality = this.networkQualities.get(destination.id);
    const lossRate = networkQuality?.packetLoss || 0.01;

    // 模拟传输延迟
    const delay = networkQuality?.latency || 50;
    await new Promise(resolve => setTimeout(resolve, delay));

    // 模拟丢包
    return Math.random() > lossRate;
  }

  /**
   * 更新传输统计
   * @param result 传输结果
   */
  private updateTransmissionStats(result: TransmissionResult): void {
    this.transmissionStats.totalTransmissions++;

    if (result.success) {
      this.transmissionStats.successfulTransmissions++;
    }

    this.transmissionStats.totalRetransmissions += result.retransmissions;

    // 更新平均延迟
    this.transmissionStats.averageLatency =
      (this.transmissionStats.averageLatency * (this.transmissionStats.totalTransmissions - 1) +
       result.actualLatency) / this.transmissionStats.totalTransmissions;

    // 更新平均吞吐量
    this.transmissionStats.averageThroughput =
      (this.transmissionStats.averageThroughput * (this.transmissionStats.totalTransmissions - 1) +
       result.throughput) / this.transmissionStats.totalTransmissions;
  }

  /**
   * 更新网络质量
   * @param nodeId 节点ID
   * @param quality 网络质量
   */
  updateNetworkQuality(nodeId: string, quality: Partial<NetworkQuality>): void {
    const currentQuality = this.networkQualities.get(nodeId) || {
      bandwidth: 10000000,
      latency: 50,
      packetLoss: 0.01,
      jitter: 5,
      stability: 0.9,
      quality: 'good'
    };

    const updatedQuality = { ...currentQuality, ...quality };

    // 更新质量等级
    if (updatedQuality.latency < 20 && updatedQuality.packetLoss < 0.001) {
      updatedQuality.quality = 'excellent';
    } else if (updatedQuality.latency < 50 && updatedQuality.packetLoss < 0.01) {
      updatedQuality.quality = 'good';
    } else if (updatedQuality.latency < 100 && updatedQuality.packetLoss < 0.05) {
      updatedQuality.quality = 'fair';
    } else {
      updatedQuality.quality = 'poor';
    }

    this.networkQualities.set(nodeId, updatedQuality);

    // 更新丢包率历史
    const lossHistory = this.packetLossHistory.get(nodeId) || [];
    lossHistory.push(updatedQuality.packetLoss);
    if (lossHistory.length > 100) {
      lossHistory.shift(); // 保持最近100条记录
    }
    this.packetLossHistory.set(nodeId, lossHistory);

    this.eventEmitter.emit('network.quality.updated', {
      nodeId,
      quality: updatedQuality
    });
  }

  /**
   * 获取传输统计信息
   * @returns 统计信息
   */
  getTransmissionStatistics(): any {
    const successRate = this.transmissionStats.totalTransmissions > 0 ?
      this.transmissionStats.successfulTransmissions / this.transmissionStats.totalTransmissions : 0;

    return {
      ...this.transmissionStats,
      successRate: successRate.toFixed(3),
      averageRetransmissions: this.transmissionStats.totalTransmissions > 0 ?
        this.transmissionStats.totalRetransmissions / this.transmissionStats.totalTransmissions : 0,
      compressionRatios: Object.fromEntries(this.transmissionStats.compressionRatios)
    };
  }

  /**
   * 重置统计信息
   */
  resetStatistics(): void {
    this.transmissionStats = {
      totalTransmissions: 0,
      successfulTransmissions: 0,
      totalRetransmissions: 0,
      averageLatency: 0,
      averageThroughput: 0,
      compressionRatios: new Map()
    };

    this.logger.log('传输统计信息已重置');
  }
}
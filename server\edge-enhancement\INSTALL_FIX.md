# 依赖安装问题修复指南

## 🔧 问题描述

在Windows环境下安装npm依赖时遇到以下问题：
1. ESLint版本过时警告
2. Windows权限问题导致无法删除node_modules
3. zlib包安装失败（node-waf命令不存在）

## ✅ 修复方案

### 方案一：使用自动化脚本（推荐）

#### Windows用户：
```bash
# 以管理员身份运行PowerShell或命令提示符
cd server/edge-enhancement
scripts\clean-install.bat
```

#### Linux/Mac用户：
```bash
cd server/edge-enhancement
chmod +x scripts/clean-install.sh
./scripts/clean-install.sh
```

### 方案二：手动修复步骤

#### 1. 清理现有环境
```bash
# 删除node_modules（如果权限问题，以管理员身份运行）
rmdir /s /q node_modules
# 或使用PowerShell
Remove-Item -Path "node_modules" -Recurse -Force

# 删除package-lock.json
del package-lock.json

# 清理npm缓存
npm cache clean --force
```

#### 2. 配置npm设置
```bash
npm config set fund false
npm config set audit false
npm config set legacy-peer-deps true
npm config set optional false
```

#### 3. 安装依赖
```bash
npm install --no-optional --legacy-peer-deps
```

### 方案三：使用yarn（替代方案）

如果npm持续出现问题，可以使用yarn：

```bash
# 安装yarn
npm install -g yarn

# 删除package-lock.json
del package-lock.json

# 使用yarn安装
yarn install
```

## 🔍 修复内容说明

### 1. 依赖版本更新
- **ESLint**: 从8.42.0更新到8.56.0（最新稳定版）
- **NestJS**: 更新到最新稳定版本
- **TypeScript**: 更新到5.3.3
- **移除zlib**: 这是一个过时的包，Node.js内置了zlib模块

### 2. 配置文件优化
- **添加.npmrc**: 配置npm以避免常见问题
- **更新ESLint配置**: 兼容最新版本
- **添加清理脚本**: 自动化清理和安装过程

### 3. 新增的npm脚本
```json
{
  "clean": "rimraf dist node_modules package-lock.json",
  "clean:install": "npm run clean && npm install --legacy-peer-deps",
  "lint:check": "eslint \"{src,apps,libs,test}/**/*.ts\""
}
```

## 🚀 验证安装

安装完成后，运行以下命令验证：

```bash
# 检查依赖是否正确安装
npm list --depth=0

# 运行代码检查
npm run lint:check

# 尝试构建项目
npm run build

# 运行测试
npm test

# 启动开发服务器
npm run start:dev
```

## 🛠️ 常见问题解决

### 问题1：权限错误
**解决方案**：以管理员身份运行命令提示符或PowerShell

### 问题2：网络超时
**解决方案**：
```bash
# 设置npm镜像源
npm config set registry https://registry.npmmirror.com/
# 或者
npm config set registry https://registry.npmjs.org/
```

### 问题3：Python/Visual Studio构建工具缺失
**解决方案**：
```bash
# 安装Windows构建工具
npm install -g windows-build-tools
# 或者安装Visual Studio Build Tools
```

### 问题4：Node.js版本不兼容
**解决方案**：确保使用Node.js 18.x或更高版本
```bash
node --version  # 应该显示v18.x.x或更高
```

## 📞 获取帮助

如果仍然遇到问题，请：
1. 检查Node.js版本是否为18.x或更高
2. 确保以管理员权限运行命令
3. 尝试使用yarn替代npm
4. 查看完整的错误日志并搜索解决方案

## 🎉 安装成功标志

当看到以下输出时，表示安装成功：
```
✅ 所有依赖安装完成
✅ 没有严重的安全漏洞
✅ 项目可以正常构建和运行
```

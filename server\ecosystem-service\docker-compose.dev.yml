version: '3.8'

services:
  # 生态系统服务 - 开发模式
  ecosystem-service-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: ecosystem-service-dev
    restart: unless-stopped
    ports:
      - "3030:3030"
      - "9229:9229" # Debug端口
    environment:
      - NODE_ENV=development
      - PORT=3030
      - HOST=0.0.0.0
      
      # 数据库配置
      - DB_TYPE=mysql
      - DB_HOST=mysql-dev
      - DB_PORT=3306
      - DB_USERNAME=ecosystem_user
      - DB_PASSWORD=ecosystem_password
      - DB_DATABASE=ecosystem_service_dev
      - DB_SYNCHRONIZE=true
      - DB_LOGGING=true
      
      # Redis配置
      - REDIS_HOST=redis-dev
      - REDIS_PORT=6379
      - REDIS_PASSWORD=redis_password
      - REDIS_DB=0
      
      # JWT配置
      - JWT_SECRET=dev-secret-key
      - JWT_EXPIRES_IN=24h
      
      # 开发配置
      - API_PREFIX=api
      - CORS_ORIGIN=*
      - LOG_LEVEL=debug
      - LOG_FORMAT=pretty
    volumes:
      - .:/app
      - /app/node_modules
      - dev_storage:/app/storage
    depends_on:
      mysql-dev:
        condition: service_healthy
      redis-dev:
        condition: service_healthy
    networks:
      - ecosystem-dev-network
    command: npm run start:debug

  # MySQL数据库 - 开发环境
  mysql-dev:
    image: mysql:8.0
    container_name: ecosystem-mysql-dev
    restart: unless-stopped
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=ecosystem_service_dev
      - MYSQL_USER=ecosystem_user
      - MYSQL_PASSWORD=ecosystem_password
      - MYSQL_CHARACTER_SET_SERVER=utf8mb4
      - MYSQL_COLLATION_SERVER=utf8mb4_unicode_ci
    ports:
      - "3307:3306"
    volumes:
      - mysql_dev_data:/var/lib/mysql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - ecosystem-dev-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-proot_password"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Redis缓存 - 开发环境
  redis-dev:
    image: redis:7-alpine
    container_name: ecosystem-redis-dev
    restart: unless-stopped
    command: redis-server --requirepass redis_password
    ports:
      - "6380:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - ecosystem-dev-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Redis Commander - Redis管理界面
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: redis-commander
    restart: unless-stopped
    environment:
      - REDIS_HOSTS=local:redis-dev:6379:0:redis_password
    ports:
      - "8081:8081"
    depends_on:
      - redis-dev
    networks:
      - ecosystem-dev-network

  # phpMyAdmin - MySQL管理界面
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: phpmyadmin
    restart: unless-stopped
    environment:
      - PMA_HOST=mysql-dev
      - PMA_PORT=3306
      - PMA_USER=ecosystem_user
      - PMA_PASSWORD=ecosystem_password
      - MYSQL_ROOT_PASSWORD=root_password
    ports:
      - "8080:80"
    depends_on:
      - mysql-dev
    networks:
      - ecosystem-dev-network

# 数据卷
volumes:
  mysql_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  dev_storage:
    driver: local

# 网络
networks:
  ecosystem-dev-network:
    driver: bridge

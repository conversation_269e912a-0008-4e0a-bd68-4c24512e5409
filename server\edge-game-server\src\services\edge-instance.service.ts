import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { v4 as uuidv4 } from 'uuid';

/**
 * 游戏实例状态枚举
 */
export enum GameInstanceStatus {
  INITIALIZING = 'initializing',
  RUNNING = 'running',
  PAUSED = 'paused',
  STOPPING = 'stopping',
  STOPPED = 'stopped',
  ERROR = 'error'
}

/**
 * 游戏实例接口
 */
export interface GameInstance {
  id: string;
  gameId: string;
  roomId: string;
  status: GameInstanceStatus;
  players: string[];
  maxPlayers: number;
  createdAt: Date;
  updatedAt: Date;
  metadata: {
    gameType: string;
    gameMode: string;
    settings: Record<string, any>;
    version: string;
  };
  resources: {
    cpuUsage: number;
    memoryUsage: number;
    networkBandwidth: number;
  };
}

/**
 * 用户会话接口
 */
export interface UserSession {
  userId: string;
  sessionId: string;
  gameInstanceId?: string;
  status: 'connected' | 'disconnected' | 'in-game';
  connectedAt: Date;
  lastActivity: Date;
  metadata: {
    userAgent: string;
    ipAddress: string;
    region: string;
  };
}

/**
 * 边缘实例服务
 * 管理边缘节点上的游戏实例和用户会话
 */
@Injectable()
export class EdgeInstanceService {
  private readonly logger = new Logger(EdgeInstanceService.name);
  private readonly gameInstances = new Map<string, GameInstance>();
  private readonly userSessions = new Map<string, UserSession>();
  private readonly maxInstances: number;
  private readonly maxUsersPerInstance: number;

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.maxInstances = this.configService.get<number>('MAX_CONCURRENT_GAMES', 10);
    this.maxUsersPerInstance = this.configService.get<number>('MAX_USERS_PER_GAME', 20);
    this.logger.log('边缘实例服务初始化完成');
  }

  /**
   * 创建游戏实例
   */
  async createGameInstance(params: {
    gameId: string;
    roomId: string;
    gameType: string;
    gameMode: string;
    maxPlayers: number;
    settings: Record<string, any>;
  }): Promise<GameInstance> {
    if (this.gameInstances.size >= this.maxInstances) {
      throw new Error(`已达到最大实例数限制: ${this.maxInstances}`);
    }

    const instanceId = uuidv4();
    const now = new Date();

    const instance: GameInstance = {
      id: instanceId,
      gameId: params.gameId,
      roomId: params.roomId,
      status: GameInstanceStatus.INITIALIZING,
      players: [],
      maxPlayers: Math.min(params.maxPlayers, this.maxUsersPerInstance),
      createdAt: now,
      updatedAt: now,
      metadata: {
        gameType: params.gameType,
        gameMode: params.gameMode,
        settings: params.settings,
        version: '1.0.0'
      },
      resources: {
        cpuUsage: 0,
        memoryUsage: 0,
        networkBandwidth: 0
      }
    };

    this.gameInstances.set(instanceId, instance);

    // 触发实例创建事件
    this.eventEmitter.emit('game.instance.created', instance);

    this.logger.log(`游戏实例已创建: ${instanceId}`);
    return instance;
  }

  /**
   * 启动游戏实例
   */
  async startGameInstance(instanceId: string): Promise<void> {
    const instance = this.gameInstances.get(instanceId);
    if (!instance) {
      throw new Error(`游戏实例不存在: ${instanceId}`);
    }

    if (instance.status !== GameInstanceStatus.INITIALIZING) {
      throw new Error(`游戏实例状态错误: ${instance.status}`);
    }

    instance.status = GameInstanceStatus.RUNNING;
    instance.updatedAt = new Date();

    // 触发实例启动事件
    this.eventEmitter.emit('game.instance.started', instance);

    this.logger.log(`游戏实例已启动: ${instanceId}`);
  }

  /**
   * 停止游戏实例
   */
  async stopGameInstance(instanceId: string): Promise<void> {
    const instance = this.gameInstances.get(instanceId);
    if (!instance) {
      throw new Error(`游戏实例不存在: ${instanceId}`);
    }

    instance.status = GameInstanceStatus.STOPPING;
    instance.updatedAt = new Date();

    // 断开所有玩家连接
    for (const playerId of instance.players) {
      await this.removePlayerFromInstance(instanceId, playerId);
    }

    instance.status = GameInstanceStatus.STOPPED;
    instance.updatedAt = new Date();

    // 触发实例停止事件
    this.eventEmitter.emit('game.instance.stopped', instance);

    this.logger.log(`游戏实例已停止: ${instanceId}`);
  }

  /**
   * 删除游戏实例
   */
  async deleteGameInstance(instanceId: string): Promise<void> {
    const instance = this.gameInstances.get(instanceId);
    if (!instance) {
      throw new Error(`游戏实例不存在: ${instanceId}`);
    }

    if (instance.status === GameInstanceStatus.RUNNING) {
      await this.stopGameInstance(instanceId);
    }

    this.gameInstances.delete(instanceId);

    // 触发实例删除事件
    this.eventEmitter.emit('game.instance.deleted', { instanceId });

    this.logger.log(`游戏实例已删除: ${instanceId}`);
  }

  /**
   * 添加玩家到实例
   */
  async addPlayerToInstance(instanceId: string, userId: string): Promise<void> {
    const instance = this.gameInstances.get(instanceId);
    if (!instance) {
      throw new Error(`游戏实例不存在: ${instanceId}`);
    }

    if (instance.status !== GameInstanceStatus.RUNNING) {
      throw new Error(`游戏实例未运行: ${instance.status}`);
    }

    if (instance.players.length >= instance.maxPlayers) {
      throw new Error(`游戏实例已满: ${instance.players.length}/${instance.maxPlayers}`);
    }

    if (instance.players.includes(userId)) {
      throw new Error(`玩家已在实例中: ${userId}`);
    }

    instance.players.push(userId);
    instance.updatedAt = new Date();

    // 更新用户会话
    const session = this.userSessions.get(userId);
    if (session) {
      session.gameInstanceId = instanceId;
      session.status = 'in-game';
      session.lastActivity = new Date();
    }

    // 触发玩家加入事件
    this.eventEmitter.emit('game.player.joined', {
      instanceId,
      userId,
      playerCount: instance.players.length
    });

    this.logger.log(`玩家 ${userId} 加入实例 ${instanceId}`);
  }

  /**
   * 从实例移除玩家
   */
  async removePlayerFromInstance(instanceId: string, userId: string): Promise<void> {
    const instance = this.gameInstances.get(instanceId);
    if (!instance) {
      throw new Error(`游戏实例不存在: ${instanceId}`);
    }

    const playerIndex = instance.players.indexOf(userId);
    if (playerIndex === -1) {
      throw new Error(`玩家不在实例中: ${userId}`);
    }

    instance.players.splice(playerIndex, 1);
    instance.updatedAt = new Date();

    // 更新用户会话
    const session = this.userSessions.get(userId);
    if (session) {
      session.gameInstanceId = undefined;
      session.status = 'connected';
      session.lastActivity = new Date();
    }

    // 触发玩家离开事件
    this.eventEmitter.emit('game.player.left', {
      instanceId,
      userId,
      playerCount: instance.players.length
    });

    this.logger.log(`玩家 ${userId} 离开实例 ${instanceId}`);

    // 如果没有玩家了，考虑停止实例
    if (instance.players.length === 0) {
      setTimeout(() => {
        if (instance.players.length === 0) {
          this.stopGameInstance(instanceId);
        }
      }, 30000); // 30秒后检查
    }
  }

  /**
   * 创建用户会话
   */
  createUserSession(params: {
    userId: string;
    userAgent: string;
    ipAddress: string;
    region: string;
  }): UserSession {
    const sessionId = uuidv4();
    const now = new Date();

    const session: UserSession = {
      userId: params.userId,
      sessionId,
      status: 'connected',
      connectedAt: now,
      lastActivity: now,
      metadata: {
        userAgent: params.userAgent,
        ipAddress: params.ipAddress,
        region: params.region
      }
    };

    this.userSessions.set(params.userId, session);

    // 触发会话创建事件
    this.eventEmitter.emit('user.session.created', session);

    this.logger.log(`用户会话已创建: ${params.userId}`);
    return session;
  }

  /**
   * 删除用户会话
   */
  async deleteUserSession(userId: string): Promise<void> {
    const session = this.userSessions.get(userId);
    if (!session) {
      return;
    }

    // 如果用户在游戏中，先移除
    if (session.gameInstanceId) {
      await this.removePlayerFromInstance(session.gameInstanceId, userId);
    }

    this.userSessions.delete(userId);

    // 触发会话删除事件
    this.eventEmitter.emit('user.session.deleted', { userId, sessionId: session.sessionId });

    this.logger.log(`用户会话已删除: ${userId}`);
  }

  /**
   * 获取游戏实例
   */
  getGameInstance(instanceId: string): GameInstance | undefined {
    return this.gameInstances.get(instanceId);
  }

  /**
   * 获取所有游戏实例
   */
  getAllGameInstances(): GameInstance[] {
    return Array.from(this.gameInstances.values());
  }

  /**
   * 获取用户会话
   */
  getUserSession(userId: string): UserSession | undefined {
    return this.userSessions.get(userId);
  }

  /**
   * 获取所有用户会话
   */
  getAllUserSessions(): UserSession[] {
    return Array.from(this.userSessions.values());
  }

  /**
   * 获取实例统计信息
   */
  getInstanceStats(): {
    totalInstances: number;
    runningInstances: number;
    totalPlayers: number;
    totalSessions: number;
    resourceUsage: {
      avgCpuUsage: number;
      avgMemoryUsage: number;
      avgNetworkBandwidth: number;
    };
  } {
    const instances = Array.from(this.gameInstances.values());
    const runningInstances = instances.filter(i => i.status === GameInstanceStatus.RUNNING);
    const totalPlayers = instances.reduce((sum, i) => sum + i.players.length, 0);

    const avgCpuUsage = instances.length > 0 
      ? instances.reduce((sum, i) => sum + i.resources.cpuUsage, 0) / instances.length 
      : 0;
    const avgMemoryUsage = instances.length > 0 
      ? instances.reduce((sum, i) => sum + i.resources.memoryUsage, 0) / instances.length 
      : 0;
    const avgNetworkBandwidth = instances.length > 0 
      ? instances.reduce((sum, i) => sum + i.resources.networkBandwidth, 0) / instances.length 
      : 0;

    return {
      totalInstances: instances.length,
      runningInstances: runningInstances.length,
      totalPlayers,
      totalSessions: this.userSessions.size,
      resourceUsage: {
        avgCpuUsage,
        avgMemoryUsage,
        avgNetworkBandwidth
      }
    };
  }

  /**
   * 更新实例资源使用情况
   */
  updateInstanceResources(instanceId: string, resources: {
    cpuUsage?: number;
    memoryUsage?: number;
    networkBandwidth?: number;
  }): void {
    const instance = this.gameInstances.get(instanceId);
    if (!instance) {
      return;
    }

    if (resources.cpuUsage !== undefined) {
      instance.resources.cpuUsage = resources.cpuUsage;
    }
    if (resources.memoryUsage !== undefined) {
      instance.resources.memoryUsage = resources.memoryUsage;
    }
    if (resources.networkBandwidth !== undefined) {
      instance.resources.networkBandwidth = resources.networkBandwidth;
    }

    instance.updatedAt = new Date();

    // 触发资源更新事件
    this.eventEmitter.emit('game.instance.resources.updated', {
      instanceId,
      resources: instance.resources
    });
  }
}

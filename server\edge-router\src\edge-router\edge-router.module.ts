import { Module } from '@nestjs/common';

// 服务
import { IntelligentRoutingService } from '../services/intelligent-routing.service';
import { RoutingCacheService } from '../services/routing-cache.service';
import { EdgeRegistryClientService } from '../services/edge-registry-client.service';

// 控制器
import { EdgeRouterController } from '../controllers/edge-router.controller';
import { EdgeRouterMicroserviceController } from '../controllers/edge-router-microservice.controller';

/**
 * 边缘路由模块
 */
@Module({
  controllers: [
    EdgeRouterController,
    EdgeRouterMicroserviceController,
  ],
  providers: [
    IntelligentRoutingService,
    RoutingCacheService,
    EdgeRegistryClientService,
  ],
  exports: [
    IntelligentRoutingService,
    RoutingCacheService,
    EdgeRegistryClientService,
  ],
})
export class EdgeRouterModule {}

import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { DeviceService } from './device.service';
import { EdgeDevice, DeviceStatus } from './entities/edge-device.entity';

@ApiTags('devices')
@Controller('devices')
export class DeviceController {
  private readonly logger = new Logger(DeviceController.name);

  constructor(private readonly deviceService: DeviceService) {}

  @Get()
  @ApiOperation({ summary: '获取设备列表' })
  @ApiQuery({ name: 'status', required: false, enum: DeviceStatus })
  @ApiQuery({ name: 'type', required: false })
  @ApiQuery({ name: 'location', required: false })
  @ApiResponse({ status: 200, description: '设备列表获取成功' })
  async findAll(
    @Query('status') status?: DeviceStatus,
    @Query('type') type?: string,
    @Query('location') location?: string,
  ) {
    try {
      const devices = await this.deviceService.findAll({ status, type, location });
      return {
        success: true,
        data: devices,
        message: '设备列表获取成功',
      };
    } catch (error) {
      this.logger.error('获取设备列表失败', error);
      throw new HttpException(
        {
          success: false,
          message: '获取设备列表失败',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('statistics')
  @ApiOperation({ summary: '获取设备统计信息' })
  @ApiResponse({ status: 200, description: '统计信息获取成功' })
  async getStatistics() {
    try {
      const statistics = await this.deviceService.getStatistics();
      return {
        success: true,
        data: statistics,
        message: '设备统计信息获取成功',
      };
    } catch (error) {
      this.logger.error('获取设备统计信息失败', error);
      throw new HttpException(
        {
          success: false,
          message: '获取设备统计信息失败',
          error: error.message,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id')
  @ApiOperation({ summary: '获取设备详情' })
  @ApiParam({ name: 'id', description: '设备ID' })
  @ApiResponse({ status: 200, description: '设备详情获取成功' })
  @ApiResponse({ status: 404, description: '设备不存在' })
  async findOne(@Param('id') id: string) {
    try {
      const device = await this.deviceService.findOne(id);
      return {
        success: true,
        data: device,
        message: '设备详情获取成功',
      };
    } catch (error) {
      this.logger.error('获取设备详情失败', error);
      throw new HttpException(
        {
          success: false,
          message: '获取设备详情失败',
          error: error.message,
        },
        HttpStatus.NOT_FOUND,
      );
    }
  }

  @Put(':id')
  @ApiOperation({ summary: '更新设备信息' })
  @ApiParam({ name: 'id', description: '设备ID' })
  @ApiResponse({ status: 200, description: '设备更新成功' })
  @ApiResponse({ status: 404, description: '设备不存在' })
  async update(@Param('id') id: string, @Body() updateData: Partial<EdgeDevice>) {
    try {
      const device = await this.deviceService.update(id, updateData);
      return {
        success: true,
        data: device,
        message: '设备更新成功',
      };
    } catch (error) {
      this.logger.error('更新设备失败', error);
      throw new HttpException(
        {
          success: false,
          message: '更新设备失败',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Put(':id/status')
  @ApiOperation({ summary: '更新设备状态' })
  @ApiParam({ name: 'id', description: '设备ID' })
  @ApiResponse({ status: 200, description: '状态更新成功' })
  async updateStatus(
    @Param('id') id: string,
    @Body() statusData: { status: DeviceStatus },
  ) {
    try {
      const device = await this.deviceService.findOne(id);
      await this.deviceService.updateStatus(device.deviceId, statusData.status);
      return {
        success: true,
        message: '设备状态更新成功',
      };
    } catch (error) {
      this.logger.error('更新设备状态失败', error);
      throw new HttpException(
        {
          success: false,
          message: '更新设备状态失败',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除设备' })
  @ApiParam({ name: 'id', description: '设备ID' })
  @ApiResponse({ status: 200, description: '设备删除成功' })
  @ApiResponse({ status: 404, description: '设备不存在' })
  async remove(@Param('id') id: string) {
    try {
      await this.deviceService.remove(id);
      return {
        success: true,
        message: '设备删除成功',
      };
    } catch (error) {
      this.logger.error('删除设备失败', error);
      throw new HttpException(
        {
          success: false,
          message: '删除设备失败',
          error: error.message,
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }
}

// Jest setup file
import 'reflect-metadata';

// 设置测试超时
jest.setTimeout(30000);

// 模拟环境变量
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-secret';
process.env.REDIS_HOST = 'localhost';
process.env.REDIS_PORT = '6379';
process.env.DB_TYPE = 'sqlite';
process.env.DB_DATABASE = ':memory:';

// 全局测试配置
beforeAll(() => {
  // 静默日志输出
  jest.spyOn(console, 'log').mockImplementation(() => {});
  jest.spyOn(console, 'warn').mockImplementation(() => {});
  jest.spyOn(console, 'error').mockImplementation(() => {});
});

afterAll(() => {
  // 恢复日志输出
  jest.restoreAllMocks();
});

import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';

/**
 * 边缘节点负载信息接口
 */
export interface EdgeNodeLoad {
  nodeId: string;
  currentUsers: number;
  maxUsers: number;
  cpuUsage: number;
  memoryUsage: number;
  networkLatency: number;
  score: number; // 负载评分，越低越好
}

/**
 * 负载均衡策略枚举
 */
export enum LoadBalanceStrategy {
  ROUND_ROBIN = 'round-robin',
  LEAST_CONNECTIONS = 'least-connections',
  WEIGHTED_ROUND_ROBIN = 'weighted-round-robin',
  LEAST_RESPONSE_TIME = 'least-response-time',
  RESOURCE_BASED = 'resource-based'
}

/**
 * 边缘负载均衡服务
 * 负责在边缘节点之间分配用户和游戏实例
 */
@Injectable()
export class EdgeLoadBalancerService {
  private readonly logger = new Logger(EdgeLoadBalancerService.name);
  private readonly strategy: LoadBalanceStrategy;
  private roundRobinIndex = 0;
  private nodeLoads: Map<string, EdgeNodeLoad> = new Map();

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
  ) {
    this.strategy = this.configService.get<LoadBalanceStrategy>(
      'LOAD_BALANCE_ALGORITHM',
      LoadBalanceStrategy.ROUND_ROBIN
    );
    this.logger.log(`负载均衡策略: ${this.strategy}`);
  }

  /**
   * 选择最佳边缘节点
   * @param availableNodes 可用节点列表
   * @returns 选中的节点ID
   */
  selectBestNode(availableNodes: string[]): string | null {
    if (availableNodes.length === 0) {
      return null;
    }

    if (availableNodes.length === 1) {
      return availableNodes[0];
    }

    switch (this.strategy) {
      case LoadBalanceStrategy.ROUND_ROBIN:
        return this.roundRobinSelection(availableNodes);
      
      case LoadBalanceStrategy.LEAST_CONNECTIONS:
        return this.leastConnectionsSelection(availableNodes);
      
      case LoadBalanceStrategy.WEIGHTED_ROUND_ROBIN:
        return this.weightedRoundRobinSelection(availableNodes);
      
      case LoadBalanceStrategy.LEAST_RESPONSE_TIME:
        return this.leastResponseTimeSelection(availableNodes);
      
      case LoadBalanceStrategy.RESOURCE_BASED:
        return this.resourceBasedSelection(availableNodes);
      
      default:
        return this.roundRobinSelection(availableNodes);
    }
  }

  /**
   * 轮询选择
   */
  private roundRobinSelection(nodes: string[]): string {
    const selectedNode = nodes[this.roundRobinIndex % nodes.length];
    this.roundRobinIndex = (this.roundRobinIndex + 1) % nodes.length;
    return selectedNode;
  }

  /**
   * 最少连接选择
   */
  private leastConnectionsSelection(nodes: string[]): string {
    let bestNode = nodes[0];
    let minConnections = this.getNodeLoad(bestNode)?.currentUsers || 0;

    for (const nodeId of nodes) {
      const load = this.getNodeLoad(nodeId);
      const connections = load?.currentUsers || 0;
      
      if (connections < minConnections) {
        minConnections = connections;
        bestNode = nodeId;
      }
    }

    return bestNode;
  }

  /**
   * 加权轮询选择
   */
  private weightedRoundRobinSelection(nodes: string[]): string {
    // 根据节点容量计算权重
    const weights = nodes.map(nodeId => {
      const load = this.getNodeLoad(nodeId);
      if (!load) return 1;
      
      const availableCapacity = load.maxUsers - load.currentUsers;
      return Math.max(1, availableCapacity);
    });

    const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
    let randomWeight = Math.random() * totalWeight;

    for (let i = 0; i < nodes.length; i++) {
      randomWeight -= weights[i];
      if (randomWeight <= 0) {
        return nodes[i];
      }
    }

    return nodes[0];
  }

  /**
   * 最少响应时间选择
   */
  private leastResponseTimeSelection(nodes: string[]): string {
    let bestNode = nodes[0];
    let minLatency = this.getNodeLoad(bestNode)?.networkLatency || Infinity;

    for (const nodeId of nodes) {
      const load = this.getNodeLoad(nodeId);
      const latency = load?.networkLatency || Infinity;
      
      if (latency < minLatency) {
        minLatency = latency;
        bestNode = nodeId;
      }
    }

    return bestNode;
  }

  /**
   * 基于资源的选择
   */
  private resourceBasedSelection(nodes: string[]): string {
    let bestNode = nodes[0];
    let bestScore = this.calculateNodeScore(bestNode);

    for (const nodeId of nodes) {
      const score = this.calculateNodeScore(nodeId);
      
      if (score < bestScore) {
        bestScore = score;
        bestNode = nodeId;
      }
    }

    return bestNode;
  }

  /**
   * 计算节点负载评分
   * @param nodeId 节点ID
   * @returns 评分（越低越好）
   */
  private calculateNodeScore(nodeId: string): number {
    const load = this.getNodeLoad(nodeId);
    if (!load) return Infinity;

    // 用户负载权重 (40%)
    const userLoadRatio = load.currentUsers / load.maxUsers;
    const userScore = userLoadRatio * 0.4;

    // CPU使用率权重 (30%)
    const cpuScore = (load.cpuUsage / 100) * 0.3;

    // 内存使用率权重 (20%)
    const memoryScore = (load.memoryUsage / 100) * 0.2;

    // 网络延迟权重 (10%)
    const latencyScore = Math.min(load.networkLatency / 1000, 1) * 0.1;

    return userScore + cpuScore + memoryScore + latencyScore;
  }

  /**
   * 更新节点负载信息
   * @param nodeId 节点ID
   * @param load 负载信息
   */
  updateNodeLoad(nodeId: string, load: Partial<EdgeNodeLoad>): void {
    const existingLoad = this.nodeLoads.get(nodeId) || {
      nodeId,
      currentUsers: 0,
      maxUsers: 50,
      cpuUsage: 0,
      memoryUsage: 0,
      networkLatency: 0,
      score: 0
    };

    const updatedLoad = { ...existingLoad, ...load };
    updatedLoad.score = this.calculateNodeScore(nodeId);
    
    this.nodeLoads.set(nodeId, updatedLoad);

    // 触发负载更新事件
    this.eventEmitter.emit('edge.load.updated', {
      nodeId,
      load: updatedLoad
    });
  }

  /**
   * 获取节点负载信息
   * @param nodeId 节点ID
   * @returns 负载信息
   */
  getNodeLoad(nodeId: string): EdgeNodeLoad | undefined {
    return this.nodeLoads.get(nodeId);
  }

  /**
   * 获取所有节点负载信息
   * @returns 所有节点负载信息
   */
  getAllNodeLoads(): Map<string, EdgeNodeLoad> {
    return new Map(this.nodeLoads);
  }

  /**
   * 移除节点负载信息
   * @param nodeId 节点ID
   */
  removeNodeLoad(nodeId: string): void {
    this.nodeLoads.delete(nodeId);
    
    // 触发节点移除事件
    this.eventEmitter.emit('edge.node.removed', { nodeId });
  }

  /**
   * 检查节点是否过载
   * @param nodeId 节点ID
   * @returns 是否过载
   */
  isNodeOverloaded(nodeId: string): boolean {
    const load = this.getNodeLoad(nodeId);
    if (!load) return false;

    const maxCpuThreshold = this.configService.get<number>('MAX_CPU_USAGE', 80);
    const maxMemoryThreshold = this.configService.get<number>('MAX_MEMORY_USAGE', 80);
    const maxUserRatio = 0.9; // 90%用户容量

    return (
      load.cpuUsage > maxCpuThreshold ||
      load.memoryUsage > maxMemoryThreshold ||
      (load.currentUsers / load.maxUsers) > maxUserRatio
    );
  }

  /**
   * 获取可用节点列表
   * @param allNodes 所有节点列表
   * @returns 可用节点列表
   */
  getAvailableNodes(allNodes: string[]): string[] {
    return allNodes.filter(nodeId => !this.isNodeOverloaded(nodeId));
  }

  /**
   * 获取负载均衡统计信息
   * @returns 统计信息
   */
  getLoadBalanceStats(): {
    totalNodes: number;
    availableNodes: number;
    overloadedNodes: number;
    totalUsers: number;
    averageLoad: number;
  } {
    const allLoads = Array.from(this.nodeLoads.values());
    const overloadedCount = allLoads.filter(load => 
      this.isNodeOverloaded(load.nodeId)
    ).length;

    const totalUsers = allLoads.reduce((sum, load) => sum + load.currentUsers, 0);
    const averageLoad = allLoads.length > 0 
      ? allLoads.reduce((sum, load) => sum + load.score, 0) / allLoads.length 
      : 0;

    return {
      totalNodes: allLoads.length,
      availableNodes: allLoads.length - overloadedCount,
      overloadedNodes: overloadedCount,
      totalUsers,
      averageLoad
    };
  }
}

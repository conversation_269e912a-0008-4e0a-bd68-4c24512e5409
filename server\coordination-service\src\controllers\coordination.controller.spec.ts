/**
 * 群体协调控制器测试
 */

import { Test, TestingModule } from '@nestjs/testing';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { HttpException, HttpStatus } from '@nestjs/common';
import { CoordinationController } from './coordination.controller';
import { GroupCoordinationService, CoordinationTaskType, SocialRole } from '../services/group-coordination.service';

describe('CoordinationController', () => {
  let controller: CoordinationController;
  let service: GroupCoordinationService;
  let eventEmitter: EventEmitter2;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CoordinationController],
      providers: [
        {
          provide: GroupCoordinationService,
          useValue: {
            createCoordinationTask: jest.fn(),
            getTaskStatus: jest.fn(),
            getActiveConflicts: jest.fn(),
            getAvailableResources: jest.fn(),
          },
        },
        {
          provide: EventEmitter2,
          useValue: {
            emit: jest.fn(),
          },
        },
      ],
    }).compile();

    controller = module.get<CoordinationController>(CoordinationController);
    service = module.get<GroupCoordinationService>(GroupCoordinationService);
    eventEmitter = module.get<EventEmitter2>(EventEmitter2);
  });

  it('应该被定义', () => {
    expect(controller).toBeDefined();
  });

  describe('createTask', () => {
    it('应该创建协调任务', async () => {
      const createTaskDto = {
        type: CoordinationTaskType.GROUP_FORMATION,
        entityIds: ['entity1', 'entity2'],
        parameters: { maxDistance: 10 },
        priority: 1
      };

      const mockTaskId = 'task123';
      jest.spyOn(service, 'createCoordinationTask').mockResolvedValue(mockTaskId);

      const result = await controller.createTask(createTaskDto);

      expect(result).toEqual({ taskId: mockTaskId });
      expect(service.createCoordinationTask).toHaveBeenCalledWith(
        createTaskDto.type,
        createTaskDto.entityIds,
        createTaskDto.parameters,
        createTaskDto.priority,
        undefined
      );
    });

    it('应该处理创建任务失败', async () => {
      const createTaskDto = {
        type: CoordinationTaskType.GROUP_FORMATION,
        entityIds: ['entity1'],
        parameters: {}
      };

      jest.spyOn(service, 'createCoordinationTask').mockRejectedValue(new Error('创建失败'));

      await expect(controller.createTask(createTaskDto)).rejects.toThrow(HttpException);
    });
  });

  describe('getTaskStatus', () => {
    it('应该返回任务状态', async () => {
      const taskId = 'task123';
      const mockTask = {
        id: taskId,
        type: CoordinationTaskType.GROUP_FORMATION,
        status: 'pending' as const,
        entityIds: ['entity1'],
        parameters: {},
        priority: 1,
        createdAt: Date.now(),
        updatedAt: Date.now()
      };

      jest.spyOn(service, 'getTaskStatus').mockReturnValue(mockTask);

      const result = await controller.getTaskStatus(taskId);

      expect(result.id).toBe(taskId);
      expect(result.type).toBe(CoordinationTaskType.GROUP_FORMATION);
      expect(result.status).toBe('pending');
    });

    it('应该处理任务不存在', async () => {
      const taskId = 'nonexistent';
      jest.spyOn(service, 'getTaskStatus').mockReturnValue(null);

      await expect(controller.getTaskStatus(taskId)).rejects.toThrow(
        new HttpException('任务不存在', HttpStatus.NOT_FOUND)
      );
    });
  });

  describe('createGroupFormation', () => {
    it('应该创建群体形成任务', async () => {
      const formationDto = {
        entityIds: ['entity1', 'entity2'],
        strategy: 'proximity_grouping',
        maxDistance: 10,
        minGroupSize: 2
      };

      const mockTaskId = 'formation_task123';
      jest.spyOn(service, 'createCoordinationTask').mockResolvedValue(mockTaskId);

      const result = await controller.createGroupFormation(formationDto);

      expect(result).toEqual({ taskId: mockTaskId });
      expect(service.createCoordinationTask).toHaveBeenCalledWith(
        CoordinationTaskType.GROUP_FORMATION,
        formationDto.entityIds,
        {
          strategy: formationDto.strategy,
          maxDistance: formationDto.maxDistance,
          minGroupSize: formationDto.minGroupSize,
          maxGroupSize: undefined
        },
        2
      );
    });
  });

  describe('assignRoles', () => {
    it('应该创建角色分配任务', async () => {
      const assignmentDto = {
        entityIds: ['entity1', 'entity2'],
        groupId: 'group123',
        requiredRoles: [SocialRole.LEADER, SocialRole.PARTICIPANT],
        strategy: 'capability_based'
      };

      const mockTaskId = 'role_task123';
      jest.spyOn(service, 'createCoordinationTask').mockResolvedValue(mockTaskId);

      const result = await controller.assignRoles(assignmentDto);

      expect(result).toEqual({ taskId: mockTaskId });
      expect(service.createCoordinationTask).toHaveBeenCalledWith(
        CoordinationTaskType.ROLE_ASSIGNMENT,
        assignmentDto.entityIds,
        {
          groupId: assignmentDto.groupId,
          requiredRoles: assignmentDto.requiredRoles,
          strategy: assignmentDto.strategy
        },
        2
      );
    });
  });

  describe('reportConflict', () => {
    it('应该报告冲突', async () => {
      const conflictDto = {
        type: 'resource_conflict',
        participants: ['entity1', 'entity2'],
        severity: 0.8,
        description: '资源争夺冲突'
      };

      const result = await controller.reportConflict(conflictDto);

      expect(result).toEqual({ message: '冲突报告已提交' });
      expect(eventEmitter.emit).toHaveBeenCalledWith(
        'coordination.conflict.reported',
        expect.objectContaining({
          type: conflictDto.type,
          participants: conflictDto.participants,
          severity: conflictDto.severity,
          description: conflictDto.description
        })
      );
    });
  });

  describe('allocateResources', () => {
    it('应该创建资源分配任务', async () => {
      const allocationDto = {
        entityIds: ['entity1', 'entity2'],
        resourceType: 'computing_power',
        amount: 100,
        strategy: 'fair_allocation'
      };

      const mockTaskId = 'resource_task123';
      jest.spyOn(service, 'createCoordinationTask').mockResolvedValue(mockTaskId);

      const result = await controller.allocateResources(allocationDto);

      expect(result).toEqual({ taskId: mockTaskId });
      expect(service.createCoordinationTask).toHaveBeenCalledWith(
        CoordinationTaskType.RESOURCE_ALLOCATION,
        allocationDto.entityIds,
        {
          resourceType: allocationDto.resourceType,
          amount: allocationDto.amount,
          strategy: allocationDto.strategy
        },
        2
      );
    });
  });

  describe('getActiveConflicts', () => {
    it('应该返回活跃冲突', async () => {
      const mockConflicts = [
        {
          id: 'conflict1',
          type: 'resource_conflict',
          participants: ['entity1', 'entity2'],
          severity: 0.8,
          description: '资源冲突',
          startTime: Date.now(),
          resolutionAttempts: 0
        }
      ];

      jest.spyOn(service, 'getActiveConflicts').mockReturnValue(mockConflicts);

      const result = await controller.getActiveConflicts();

      expect(result).toEqual({ conflicts: mockConflicts });
    });
  });

  describe('getAvailableResources', () => {
    it('应该返回可用资源', async () => {
      const mockResources = [
        {
          id: 'resource1',
          type: 'computing_power',
          capacity: 1000,
          currentUsage: 200,
          location: { x: 0, y: 0, z: 0 },
          accessRequirements: [],
          priority: 1
        }
      ];

      jest.spyOn(service, 'getAvailableResources').mockReturnValue(mockResources);

      const result = await controller.getAvailableResources();

      expect(result).toEqual({ resources: mockResources });
    });

    it('应该按类型过滤资源', async () => {
      const mockResources = [
        {
          id: 'resource1',
          type: 'computing_power',
          capacity: 1000,
          currentUsage: 200,
          location: { x: 0, y: 0, z: 0 },
          accessRequirements: [],
          priority: 1
        },
        {
          id: 'resource2',
          type: 'storage',
          capacity: 500,
          currentUsage: 100,
          location: { x: 10, y: 10, z: 0 },
          accessRequirements: [],
          priority: 2
        }
      ];

      jest.spyOn(service, 'getAvailableResources').mockReturnValue(mockResources);

      const result = await controller.getAvailableResources('computing_power');

      expect(result.resources).toHaveLength(1);
      expect(result.resources[0].type).toBe('computing_power');
    });
  });

  describe('healthCheck', () => {
    it('应该返回健康状态', async () => {
      const result = await controller.healthCheck();

      expect(result.status).toBe('ok');
      expect(result.timestamp).toBeInstanceOf(Date);
    });
  });
});

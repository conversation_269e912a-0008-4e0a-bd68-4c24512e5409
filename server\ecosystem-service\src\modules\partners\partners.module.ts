import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PartnersController } from './partners.controller';
import { PartnersService } from './partners.service';
import { Partner, PartnerCertification, PartnerAgreement } from '../../entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([Partner, PartnerCertification, PartnerAgreement]),
  ],
  controllers: [PartnersController],
  providers: [PartnersService],
  exports: [PartnersService],
})
export class PartnersModule {}

# 开发环境Dockerfile
FROM node:18-alpine

# 安装必要的系统依赖
RUN apk add --no-cache curl

# 创建非root用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装所有依赖（包括开发依赖）
RUN npm ci

# 创建必要的目录
RUN mkdir -p /app/storage /app/logs

# 设置目录权限
RUN chown -R nestjs:nodejs /app

# 切换到非root用户
USER nestjs

# 暴露端口
EXPOSE 3030 9229

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3030/health || exit 1

# 默认启动命令（可以被docker-compose覆盖）
CMD ["npm", "run", "start:dev"]

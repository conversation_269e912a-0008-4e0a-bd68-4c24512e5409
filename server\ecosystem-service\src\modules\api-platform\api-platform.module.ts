import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ApiPlatformController } from './api-platform.controller';
import { ApiPlatformService } from './api-platform.service';
import { ApiSpecification, ApiUsageRecord } from '../../entities';

@Module({
  imports: [
    TypeOrmModule.forFeature([ApiSpecification, ApiUsageRecord]),
  ],
  controllers: [ApiPlatformController],
  providers: [ApiPlatformService],
  exports: [ApiPlatformService],
})
export class ApiPlatformModule {}

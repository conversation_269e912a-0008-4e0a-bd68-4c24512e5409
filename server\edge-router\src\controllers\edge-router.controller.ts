import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';

import { IntelligentRoutingService } from '../services/intelligent-routing.service';
import { RoutingCacheService } from '../services/routing-cache.service';
import { EdgeRegistryClientService } from '../services/edge-registry-client.service';

import { RoutingDecisionRequestDto } from '../dto/routing-request.dto';
import { 
  ApiResponseDto, 
  RoutingDecisionResponseDto, 
  NetworkQualityResponseDto,
  RoutingStatsResponseDto,
  EdgeNodeResponseDto 
} from '../dto/routing-response.dto';

/**
 * 边缘路由控制器
 */
@ApiTags('edge-router')
@Controller('router')
export class EdgeRouterController {
  private readonly logger = new Logger(EdgeRouterController.name);

  constructor(
    private readonly routingService: IntelligentRoutingService,
    private readonly cacheService: RoutingCacheService,
    private readonly edgeRegistryClient: EdgeRegistryClientService,
  ) {}

  /**
   * 路由决策
   */
  @Post('decide')
  @ApiOperation({ summary: '路由决策', description: '根据客户端信息和要求选择最优的边缘节点' })
  @ApiResponse({ 
    status: 200, 
    description: '路由决策成功', 
    type: ApiResponseDto<RoutingDecisionResponseDto> 
  })
  @ApiResponse({ status: 400, description: '请求参数错误' })
  @ApiResponse({ status: 404, description: '没有可用节点' })
  async makeRoutingDecision(
    @Body() requestDto: RoutingDecisionRequestDto
  ): Promise<ApiResponseDto<RoutingDecisionResponseDto>> {
    try {
      this.logger.log(`收到路由决策请求: 客户端 ${requestDto.clientInfo.clientId}`);
      
      const startTime = Date.now();
      const decision = await this.routingService.makeRoutingDecision(requestDto);
      const decisionTime = Date.now() - startTime;

      if (!decision) {
        throw new HttpException(
          ApiResponseDto.error('没有可用的边缘节点', 'NO_AVAILABLE_NODES'),
          HttpStatus.NOT_FOUND,
        );
      }

      const response: RoutingDecisionResponseDto = {
        selectedNode: {
          nodeId: decision.selectedNode.nodeId,
          region: decision.selectedNode.region,
          endpoint: decision.selectedNode.endpoint,
          status: decision.selectedNode.status,
          location: decision.selectedNode.location,
          metrics: decision.selectedNode.metrics,
          capabilities: decision.selectedNode.capabilities,
        },
        score: decision.score,
        reason: decision.reason,
        decisionTime,
        strategy: decision.strategy,
        alternativeNodes: decision.alternativeNodes?.map(node => ({
          nodeId: node.nodeId,
          region: node.region,
          endpoint: node.endpoint,
          status: node.status,
          location: node.location,
          metrics: node.metrics,
          capabilities: node.capabilities,
        })),
        metadata: decision.metadata,
      };

      return ApiResponseDto.success('路由决策成功', response);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      
      this.logger.error(`路由决策失败: ${error.message}`, error.stack);
      throw new HttpException(
        ApiResponseDto.error(`路由决策失败: ${error.message}`, 'ROUTING_DECISION_FAILED'),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 获取网络质量信息
   */
  @Get('network-quality/:nodeId')
  @ApiOperation({ summary: '获取网络质量', description: '获取到指定节点的网络质量信息' })
  @ApiParam({ name: 'nodeId', description: '节点ID', example: 'edge-node-001' })
  @ApiQuery({ name: 'clientIp', required: false, description: '客户端IP地址' })
  @ApiResponse({ 
    status: 200, 
    description: '获取成功', 
    type: ApiResponseDto<NetworkQualityResponseDto> 
  })
  @ApiResponse({ status: 404, description: '节点不存在' })
  async getNetworkQuality(
    @Param('nodeId') nodeId: string,
    @Query('clientIp') clientIp?: string
  ): Promise<ApiResponseDto<NetworkQualityResponseDto>> {
    try {
      this.logger.log(`获取网络质量: 节点 ${nodeId}, 客户端IP ${clientIp}`);
      
      const quality = await this.routingService.measureNetworkQuality(nodeId, clientIp);
      
      if (!quality) {
        throw new HttpException(
          ApiResponseDto.error('节点不存在或无法测量网络质量', 'NODE_NOT_FOUND'),
          HttpStatus.NOT_FOUND,
        );
      }

      return ApiResponseDto.success('获取网络质量成功', quality);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      
      this.logger.error(`获取网络质量失败: ${error.message}`, error.stack);
      throw new HttpException(
        ApiResponseDto.error(`获取网络质量失败: ${error.message}`, 'NETWORK_QUALITY_FAILED'),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 获取可用节点列表
   */
  @Get('nodes')
  @ApiOperation({ summary: '获取可用节点', description: '获取所有可用的边缘节点列表' })
  @ApiQuery({ name: 'region', required: false, description: '过滤指定区域的节点' })
  @ApiResponse({ 
    status: 200, 
    description: '获取成功', 
    type: ApiResponseDto<EdgeNodeResponseDto[]> 
  })
  async getAvailableNodes(
    @Query('region') region?: string
  ): Promise<ApiResponseDto<EdgeNodeResponseDto[]>> {
    try {
      this.logger.log(`获取可用节点列表: 区域 ${region || '全部'}`);
      
      const nodes = await this.edgeRegistryClient.getAllNodes(region);
      
      const nodeResponses: EdgeNodeResponseDto[] = nodes.map(node => ({
        nodeId: node.nodeId,
        region: node.region,
        endpoint: node.endpoint,
        status: node.status,
        location: node.location,
        metrics: node.metrics,
        capabilities: node.capabilities,
      }));

      return ApiResponseDto.success('获取可用节点成功', nodeResponses);
    } catch (error) {
      this.logger.error(`获取可用节点失败: ${error.message}`, error.stack);
      throw new HttpException(
        ApiResponseDto.error(`获取可用节点失败: ${error.message}`, 'GET_NODES_FAILED'),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 获取路由统计信息
   */
  @Get('stats')
  @ApiOperation({ summary: '获取路由统计', description: '获取路由服务的统计信息' })
  @ApiResponse({ 
    status: 200, 
    description: '获取成功', 
    type: ApiResponseDto<RoutingStatsResponseDto> 
  })
  async getRoutingStats(): Promise<ApiResponseDto<RoutingStatsResponseDto>> {
    try {
      this.logger.log('获取路由统计信息');
      
      const stats = await this.routingService.getRoutingStats();
      const cacheStats = this.cacheService.getCacheStats();
      
      const response: RoutingStatsResponseDto = {
        ...stats,
        cacheStats: {
          ...cacheStats,
          hitRate: 0.85, // 这里应该从实际缓存服务获取
        },
      };

      return ApiResponseDto.success('获取路由统计成功', response);
    } catch (error) {
      this.logger.error(`获取路由统计失败: ${error.message}`, error.stack);
      throw new HttpException(
        ApiResponseDto.error(`获取路由统计失败: ${error.message}`, 'GET_STATS_FAILED'),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 清空缓存
   */
  @Post('cache/clear')
  @ApiOperation({ summary: '清空缓存', description: '清空所有路由缓存' })
  @ApiResponse({ status: 200, description: '清空成功' })
  async clearCache(): Promise<ApiResponseDto> {
    try {
      this.logger.log('清空路由缓存');
      
      await this.cacheService.clear();
      
      return ApiResponseDto.success('缓存清空成功');
    } catch (error) {
      this.logger.error(`清空缓存失败: ${error.message}`, error.stack);
      throw new HttpException(
        ApiResponseDto.error(`清空缓存失败: ${error.message}`, 'CLEAR_CACHE_FAILED'),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * 获取可用区域
   */
  @Get('regions')
  @ApiOperation({ summary: '获取可用区域', description: '获取所有可用的边缘节点区域' })
  @ApiResponse({ 
    status: 200, 
    description: '获取成功', 
    type: ApiResponseDto<string[]> 
  })
  async getAvailableRegions(): Promise<ApiResponseDto<string[]>> {
    try {
      this.logger.log('获取可用区域列表');
      
      const regions = await this.edgeRegistryClient.getAvailableRegions();
      
      return ApiResponseDto.success('获取可用区域成功', regions);
    } catch (error) {
      this.logger.error(`获取可用区域失败: ${error.message}`, error.stack);
      throw new HttpException(
        ApiResponseDto.error(`获取可用区域失败: ${error.message}`, 'GET_REGIONS_FAILED'),
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

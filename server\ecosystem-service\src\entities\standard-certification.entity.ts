import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { IndustryStandard } from './industry-standard.entity';

export enum CertificationApplicationStatus {
  PENDING = 'pending',
  IN_REVIEW = 'in_review',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  ISSUED = 'issued',
  EXPIRED = 'expired',
  REVOKED = 'revoked'
}

@Entity('standard_certifications')
@Index(['standardId', 'status'])
@Index(['applicantId', 'status'])
export class StandardCertification {
  @PrimaryGeneratedColumn('uuid')
  certificationId: string;

  @Column('uuid')
  @Index()
  standardId: string;

  @Column({ length: 255 })
  @Index()
  applicantId: string; // 申请者ID（可能是合作伙伴ID或组织ID）

  @Column({ length: 255 })
  applicantName: string;

  @Column({ length: 100 })
  applicantType: string; // 'partner', 'organization', 'individual'

  @Column({
    type: 'enum',
    enum: CertificationApplicationStatus,
    default: CertificationApplicationStatus.PENDING,
  })
  @Index()
  status: CertificationApplicationStatus;

  @Column({ length: 100, nullable: true })
  certificationLevel: string; // 认证级别

  @Column('json', { nullable: true })
  applicationData: {
    contact_info: {
      name: string;
      email: string;
      phone: string;
      organization: string;
    };
    technical_details: any;
    supporting_documents: string[];
    self_assessment: any;
  };

  @Column('json', { nullable: true })
  reviewResults: {
    reviewer_id: string;
    reviewer_name: string;
    review_date: Date;
    score: number;
    comments: string;
    recommendations: string[];
    deficiencies: string[];
  };

  @Column({ type: 'date', nullable: true })
  appliedAt: Date;

  @Column({ type: 'date', nullable: true })
  reviewedAt: Date;

  @Column({ type: 'date', nullable: true })
  issuedAt: Date;

  @Column({ type: 'date', nullable: true })
  expiresAt: Date;

  @Column({ length: 500, nullable: true })
  certificateUrl: string;

  @Column({ length: 255, nullable: true })
  certificateNumber: string;

  @Column('text', { nullable: true })
  notes: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // 关联关系
  @ManyToOne(() => IndustryStandard, standard => standard.certifications, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'standardId' })
  standard: IndustryStandard;
}

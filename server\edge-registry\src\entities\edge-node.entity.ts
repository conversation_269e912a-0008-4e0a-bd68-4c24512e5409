import {
  Entity,
  PrimaryColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

/**
 * 边缘节点状态枚举
 */
export enum EdgeNodeStatus {
  ONLINE = 'online',
  OFFLINE = 'offline',
  MAINTENANCE = 'maintenance',
  OVERLOADED = 'overloaded',
  UNHEALTHY = 'unhealthy',
}

/**
 * 边缘节点实体
 */
@Entity('edge_nodes')
@Index(['region'])
@Index(['status'])
@Index(['lastHeartbeat'])
export class EdgeNodeEntity {
  @PrimaryColumn({ type: 'varchar', length: 100, comment: '节点唯一标识' })
  nodeId: string;

  @Column({ type: 'varchar', length: 100, comment: '节点所属区域' })
  @Index()
  region: string;

  @Column({ type: 'varchar', length: 255, comment: '节点端点地址' })
  endpoint: string;

  @Column({
    type: 'enum',
    enum: EdgeNodeStatus,
    default: EdgeNodeStatus.ONLINE,
    comment: '节点状态',
  })
  @Index()
  status: EdgeNodeStatus;

  @Column({ type: 'varchar', length: 50, default: '1.0.0', comment: '节点版本' })
  version: string;

  // 节点能力配置
  @Column({ type: 'int', default: 100, comment: '最大用户数' })
  maxUsers: number;

  @Column({ type: 'json', nullable: true, comment: '支持的功能列表' })
  supportedFeatures: string[];

  @Column({ type: 'varchar', length: 50, nullable: true, comment: 'CPU资源配置' })
  cpuResources: string;

  @Column({ type: 'varchar', length: 50, nullable: true, comment: '内存资源配置' })
  memoryResources: string;

  @Column({ type: 'varchar', length: 50, nullable: true, comment: '存储资源配置' })
  storageResources: string;

  // 节点位置信息
  @Column({ type: 'decimal', precision: 10, scale: 7, nullable: true, comment: '纬度' })
  latitude: number;

  @Column({ type: 'decimal', precision: 10, scale: 7, nullable: true, comment: '经度' })
  longitude: number;

  @Column({ type: 'varchar', length: 100, nullable: true, comment: '城市' })
  city: string;

  @Column({ type: 'varchar', length: 100, nullable: true, comment: '国家' })
  country: string;

  // 节点指标
  @Column({ type: 'int', default: 0, comment: '当前用户数' })
  currentUsers: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0, comment: 'CPU使用率(%)' })
  cpuUsage: number;

  @Column({ type: 'decimal', precision: 5, scale: 2, default: 0, comment: '内存使用率(%)' })
  memoryUsage: number;

  @Column({ type: 'int', default: 0, comment: '网络延迟(ms)' })
  networkLatency: number;

  @Column({ type: 'bigint', default: 0, comment: '运行时间(秒)' })
  uptime: number;

  // 时间戳
  @Column({ type: 'timestamp', nullable: true, comment: '最后心跳时间' })
  @Index()
  lastHeartbeat: Date;

  @CreateDateColumn({ comment: '注册时间' })
  registeredAt: Date;

  @UpdateDateColumn({ comment: '更新时间' })
  updatedAt: Date;

  // 元数据
  @Column({ type: 'json', nullable: true, comment: '节点元数据' })
  metadata: Record<string, any>;

  /**
   * 转换为业务对象
   */
  toBusinessObject() {
    return {
      nodeId: this.nodeId,
      region: this.region,
      endpoint: this.endpoint,
      status: this.status,
      version: this.version,
      capabilities: {
        maxUsers: this.maxUsers,
        supportedFeatures: this.supportedFeatures || [],
        resources: {
          cpu: this.cpuResources,
          memory: this.memoryResources,
          storage: this.storageResources,
        },
      },
      location: {
        latitude: this.latitude,
        longitude: this.longitude,
        city: this.city,
        country: this.country,
      },
      metrics: {
        currentUsers: this.currentUsers,
        cpuUsage: this.cpuUsage,
        memoryUsage: this.memoryUsage,
        networkLatency: this.networkLatency,
        uptime: this.uptime,
      },
      lastHeartbeat: this.lastHeartbeat,
      registeredAt: this.registeredAt,
      metadata: this.metadata,
    };
  }

  /**
   * 从业务对象创建实体
   */
  static fromBusinessObject(nodeData: any): EdgeNodeEntity {
    const entity = new EdgeNodeEntity();
    
    entity.nodeId = nodeData.nodeId;
    entity.region = nodeData.region;
    entity.endpoint = nodeData.endpoint;
    entity.status = nodeData.status || EdgeNodeStatus.ONLINE;
    entity.version = nodeData.version || '1.0.0';
    
    // 能力配置
    if (nodeData.capabilities) {
      entity.maxUsers = nodeData.capabilities.maxUsers || 100;
      entity.supportedFeatures = nodeData.capabilities.supportedFeatures || [];
      
      if (nodeData.capabilities.resources) {
        entity.cpuResources = nodeData.capabilities.resources.cpu;
        entity.memoryResources = nodeData.capabilities.resources.memory;
        entity.storageResources = nodeData.capabilities.resources.storage;
      }
    }
    
    // 位置信息
    if (nodeData.location) {
      entity.latitude = nodeData.location.latitude;
      entity.longitude = nodeData.location.longitude;
      entity.city = nodeData.location.city;
      entity.country = nodeData.location.country;
    }
    
    // 指标信息
    if (nodeData.metrics) {
      entity.currentUsers = nodeData.metrics.currentUsers || 0;
      entity.cpuUsage = nodeData.metrics.cpuUsage || 0;
      entity.memoryUsage = nodeData.metrics.memoryUsage || 0;
      entity.networkLatency = nodeData.metrics.networkLatency || 0;
      entity.uptime = nodeData.metrics.uptime || 0;
    }
    
    entity.lastHeartbeat = nodeData.lastHeartbeat || new Date();
    entity.metadata = nodeData.metadata;
    
    return entity;
  }
}

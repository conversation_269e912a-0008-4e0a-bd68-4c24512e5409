global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  # 边缘AI服务监控
  - job_name: 'edge-ai-service'
    static_configs:
      - targets: ['edge-ai-service:3006']
    metrics_path: '/metrics'
    scrape_interval: 10s
    scrape_timeout: 5s

  # MySQL监控
  - job_name: 'mysql'
    static_configs:
      - targets: ['mysql:3306']
    scrape_interval: 30s

  # Redis监控
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s

  # Nginx监控
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx:80']
    metrics_path: '/nginx_status'
    scrape_interval: 30s

  # Node Exporter监控
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

  # 边缘设备监控（动态发现）
  - job_name: 'edge-devices'
    file_sd_configs:
      - files:
          - '/etc/prometheus/edge_devices.json'
        refresh_interval: 60s

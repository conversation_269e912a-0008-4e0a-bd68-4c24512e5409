-- 边缘计算增强服务数据库初始化脚本
-- 创建数据库、用户和基础表结构

-- 设置字符编码
SET client_encoding = 'UTF8';

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_stat_statements";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- 创建数据库用户（如果不存在）
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'edge_user') THEN
        CREATE ROLE edge_user WITH LOGIN PASSWORD 'edge_password';
    END IF;
END
$$;

-- 授予权限
GRANT ALL PRIVILEGES ON DATABASE edge_enhancement TO edge_user;
GRANT ALL PRIVILEGES ON SCHEMA public TO edge_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO edge_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO edge_user;
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO edge_user;

-- 设置默认权限
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO edge_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO edge_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON FUNCTIONS TO edge_user;

-- 创建智能调度相关表
CREATE TABLE IF NOT EXISTS scheduler_nodes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    node_id VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    capacity DECIMAL(10,2) NOT NULL DEFAULT 1.0,
    current_load DECIMAL(10,2) NOT NULL DEFAULT 0.0,
    status VARCHAR(50) NOT NULL DEFAULT 'active',
    location JSONB,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS scheduler_load_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    node_id VARCHAR(255) NOT NULL,
    load_value DECIMAL(10,2) NOT NULL,
    cpu_usage DECIMAL(5,2),
    memory_usage DECIMAL(5,2),
    network_usage DECIMAL(10,2),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB
);

CREATE TABLE IF NOT EXISTS scheduler_predictions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    node_id VARCHAR(255) NOT NULL,
    predicted_load DECIMAL(10,2) NOT NULL,
    confidence DECIMAL(5,4) NOT NULL,
    time_window INTEGER NOT NULL,
    prediction_timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    actual_load DECIMAL(10,2),
    accuracy DECIMAL(5,4),
    model_version VARCHAR(50)
);

-- 创建缓存相关表
CREATE TABLE IF NOT EXISTS cache_statistics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    cache_level VARCHAR(10) NOT NULL, -- L1, L2, L3
    hit_count BIGINT NOT NULL DEFAULT 0,
    miss_count BIGINT NOT NULL DEFAULT 0,
    total_requests BIGINT NOT NULL DEFAULT 0,
    hit_rate DECIMAL(5,4) NOT NULL DEFAULT 0.0,
    avg_response_time DECIMAL(10,3),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS user_behavior_patterns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id VARCHAR(255) NOT NULL,
    access_pattern JSONB NOT NULL,
    frequency_score DECIMAL(5,4) NOT NULL DEFAULT 0.0,
    last_access TIMESTAMP WITH TIME ZONE,
    prediction_accuracy DECIMAL(5,4),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS cache_preload_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id VARCHAR(255) NOT NULL,
    cache_key VARCHAR(500) NOT NULL,
    preload_reason VARCHAR(255),
    success BOOLEAN NOT NULL DEFAULT false,
    response_time DECIMAL(10,3),
    cache_level VARCHAR(10),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建网络传输相关表
CREATE TABLE IF NOT EXISTS network_quality_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    node_id VARCHAR(255) NOT NULL,
    latency DECIMAL(10,3) NOT NULL,
    bandwidth DECIMAL(15,2) NOT NULL,
    packet_loss DECIMAL(5,4) NOT NULL DEFAULT 0.0,
    jitter DECIMAL(10,3) NOT NULL DEFAULT 0.0,
    reliability_score DECIMAL(5,4) NOT NULL DEFAULT 1.0,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS transmission_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transmission_id UUID NOT NULL DEFAULT uuid_generate_v4(),
    source_node VARCHAR(255) NOT NULL,
    destination_node VARCHAR(255) NOT NULL,
    data_size BIGINT NOT NULL,
    compressed_size BIGINT,
    compression_ratio DECIMAL(5,4),
    reliability_level VARCHAR(50) NOT NULL,
    success BOOLEAN NOT NULL DEFAULT false,
    actual_latency DECIMAL(10,3),
    error_message TEXT,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建系统监控表
CREATE TABLE IF NOT EXISTS system_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    metric_name VARCHAR(255) NOT NULL,
    metric_value DECIMAL(15,6) NOT NULL,
    metric_unit VARCHAR(50),
    tags JSONB,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_scheduler_nodes_node_id ON scheduler_nodes(node_id);
CREATE INDEX IF NOT EXISTS idx_scheduler_nodes_status ON scheduler_nodes(status);
CREATE INDEX IF NOT EXISTS idx_scheduler_load_history_node_id ON scheduler_load_history(node_id);
CREATE INDEX IF NOT EXISTS idx_scheduler_load_history_timestamp ON scheduler_load_history(timestamp);
CREATE INDEX IF NOT EXISTS idx_scheduler_predictions_node_id ON scheduler_predictions(node_id);
CREATE INDEX IF NOT EXISTS idx_scheduler_predictions_timestamp ON scheduler_predictions(prediction_timestamp);

CREATE INDEX IF NOT EXISTS idx_cache_statistics_level ON cache_statistics(cache_level);
CREATE INDEX IF NOT EXISTS idx_cache_statistics_timestamp ON cache_statistics(timestamp);
CREATE INDEX IF NOT EXISTS idx_user_behavior_patterns_user_id ON user_behavior_patterns(user_id);
CREATE INDEX IF NOT EXISTS idx_cache_preload_logs_user_id ON cache_preload_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_cache_preload_logs_timestamp ON cache_preload_logs(timestamp);

CREATE INDEX IF NOT EXISTS idx_network_quality_metrics_node_id ON network_quality_metrics(node_id);
CREATE INDEX IF NOT EXISTS idx_network_quality_metrics_timestamp ON network_quality_metrics(timestamp);
CREATE INDEX IF NOT EXISTS idx_transmission_logs_transmission_id ON transmission_logs(transmission_id);
CREATE INDEX IF NOT EXISTS idx_transmission_logs_timestamp ON transmission_logs(timestamp);

CREATE INDEX IF NOT EXISTS idx_system_metrics_name ON system_metrics(metric_name);
CREATE INDEX IF NOT EXISTS idx_system_metrics_timestamp ON system_metrics(timestamp);

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表添加更新时间触发器
CREATE TRIGGER update_scheduler_nodes_updated_at 
    BEFORE UPDATE ON scheduler_nodes 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_behavior_patterns_updated_at 
    BEFORE UPDATE ON user_behavior_patterns 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 插入初始数据
INSERT INTO scheduler_nodes (node_id, name, capacity, current_load, status, location) VALUES
('edge-node-001', '边缘节点001', 1.0, 0.0, 'active', '{"region": "east", "zone": "zone-a"}'),
('edge-node-002', '边缘节点002', 1.5, 0.0, 'active', '{"region": "west", "zone": "zone-b"}'),
('edge-node-003', '边缘节点003', 2.0, 0.0, 'active', '{"region": "central", "zone": "zone-c"}')
ON CONFLICT (node_id) DO NOTHING;

-- 插入初始缓存统计数据
INSERT INTO cache_statistics (cache_level, hit_count, miss_count, total_requests, hit_rate) VALUES
('L1', 0, 0, 0, 0.0),
('L2', 0, 0, 0, 0.0),
('L3', 0, 0, 0, 0.0)
ON CONFLICT DO NOTHING;

-- 创建视图
CREATE OR REPLACE VIEW scheduler_node_summary AS
SELECT 
    n.node_id,
    n.name,
    n.capacity,
    n.current_load,
    n.status,
    COALESCE(AVG(h.load_value), 0) as avg_load_24h,
    COALESCE(MAX(h.load_value), 0) as max_load_24h,
    COUNT(h.id) as load_samples_24h
FROM scheduler_nodes n
LEFT JOIN scheduler_load_history h ON n.node_id = h.node_id 
    AND h.timestamp > CURRENT_TIMESTAMP - INTERVAL '24 hours'
GROUP BY n.id, n.node_id, n.name, n.capacity, n.current_load, n.status;

CREATE OR REPLACE VIEW cache_performance_summary AS
SELECT 
    cache_level,
    SUM(hit_count) as total_hits,
    SUM(miss_count) as total_misses,
    SUM(total_requests) as total_requests,
    CASE 
        WHEN SUM(total_requests) > 0 
        THEN ROUND(SUM(hit_count)::DECIMAL / SUM(total_requests)::DECIMAL, 4)
        ELSE 0.0 
    END as overall_hit_rate,
    AVG(avg_response_time) as avg_response_time
FROM cache_statistics
WHERE timestamp > CURRENT_TIMESTAMP - INTERVAL '24 hours'
GROUP BY cache_level;

-- 设置表注释
COMMENT ON TABLE scheduler_nodes IS '调度器节点信息表';
COMMENT ON TABLE scheduler_load_history IS '节点负载历史记录表';
COMMENT ON TABLE scheduler_predictions IS '负载预测记录表';
COMMENT ON TABLE cache_statistics IS '缓存统计信息表';
COMMENT ON TABLE user_behavior_patterns IS '用户行为模式表';
COMMENT ON TABLE cache_preload_logs IS '缓存预加载日志表';
COMMENT ON TABLE network_quality_metrics IS '网络质量指标表';
COMMENT ON TABLE transmission_logs IS '传输日志表';
COMMENT ON TABLE system_metrics IS '系统监控指标表';

-- 完成初始化
SELECT 'Database initialization completed successfully' as status;
